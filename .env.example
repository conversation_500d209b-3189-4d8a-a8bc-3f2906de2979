# Trading Agent Configuration

# Application Settings
APP_NAME=Trading Agent
VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# TopStep API Configuration
TOPSTEP_USERNAME=your_username
TOPSTEP_API_KEY=your_api_key
TOPSTEP_ENVIRONMENT=demo
TOPSTEP_TIMEOUT=30
TOPSTEP_MAX_RETRIES=3
TOPSTEP_RETRY_DELAY=1.0

# Qwen LLM Configuration
QWEN_API_KEY=your_qwen_api_key
QWEN_MODEL=qwen-turbo
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
QWEN_TIMEOUT=60
QWEN_MAX_TOKENS=2000
QWEN_TEMPERATURE=0.1

# Database Configuration
DB_URL=sqlite:///trading_agent.db
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_ECHO=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_TIMEOUT=5

# Risk Management
RISK_MAX_DAILY_LOSS=1000.0
RISK_MAX_POSITION_SIZE=10
RISK_DEFAULT_STOP_LOSS_PCT=2.0
RISK_MAX_PORTFOLIO_RISK=5.0
RISK_MAX_CORRELATION_EXPOSURE=50.0
RISK_POSITION_SIZE_METHOD=fixed_fractional
RISK_KELLY_FRACTION=0.25

# Trading Configuration
TRADING_ENABLED=false
TRADING_PAPER_TRADING=true
TRADING_DEFAULT_ACCOUNT_ID=
TRADING_CONTRACTS=["CON.F.US.EP.U25"]
TRADING_MAX_OPEN_POSITIONS=5
TRADING_MIN_TRADE_INTERVAL=60

# Monitoring and Alerting
MONITORING_ENABLED=true
MONITORING_METRICS_PORT=8000
MONITORING_ALERT_WEBHOOK_URL=
MONITORING_SLACK_WEBHOOK_URL=
MONITORING_EMAIL_ALERTS=false
MONITORING_EMAIL_SMTP_HOST=
MONITORING_EMAIL_SMTP_PORT=587
MONITORING_EMAIL_USERNAME=
MONITORING_EMAIL_PASSWORD=
MONITORING_EMAIL_RECIPIENTS=[]

# Data Directories
DATA_DIR=data
LOGS_DIR=logs
CACHE_DIR=cache
