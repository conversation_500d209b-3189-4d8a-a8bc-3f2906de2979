"""
Dashboard data models.
Real-time data structures for the trading dashboard.
"""

from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from enum import Enum


class SystemStatus(str, Enum):
    """System status enumeration."""
    STOPPED = "STOPPED"
    STARTING = "STARTING"
    RUNNING = "RUNNING"
    STOPPING = "STOPPING"
    ERROR = "ERROR"


class TradeStatus(str, Enum):
    """Trade status enumeration."""
    PENDING = "PENDING"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


class LogLevel(str, Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class AccountInfo(BaseModel):
    """Trading account information."""
    id: str
    name: str
    balance: float
    equity: float
    margin_used: float
    margin_available: float
    account_type: str
    status: str


class Position(BaseModel):
    """Trading position information."""
    id: str
    symbol: str
    side: str
    size: int
    entry_price: float
    current_price: float
    unrealized_pnl: float
    timestamp: datetime


class Trade(BaseModel):
    """Trade execution information."""
    id: str
    symbol: str
    side: str
    size: int
    price: float
    timestamp: datetime
    status: TradeStatus
    strategy: str
    pnl: Optional[float] = None


class Signal(BaseModel):
    """Trading signal information."""
    id: str
    strategy: str
    symbol: str
    action: str
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    timestamp: datetime
    status: str


class LogEntry(BaseModel):
    """System log entry."""
    timestamp: datetime
    level: LogLevel
    message: str
    component: str
    details: Optional[Dict[str, Any]] = None


class TradingMetrics(BaseModel):
    """Trading performance metrics."""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    daily_pnl: float
    max_drawdown: float
    sharpe_ratio: Optional[float] = None
    profit_factor: Optional[float] = None


class StrategyStats(BaseModel):
    """Strategy performance statistics."""
    name: str
    signals_generated: int
    trades_executed: int
    win_rate: float
    avg_win: float
    avg_loss: float
    total_pnl: float


class SystemHealth(BaseModel):
    """System health information."""
    api_status: str
    database_status: str
    llm_status: str
    market_data_status: str
    last_heartbeat: datetime
    uptime_seconds: int


class DashboardState(BaseModel):
    """Complete dashboard state."""
    system_status: SystemStatus
    system_health: SystemHealth
    selected_account: Optional[AccountInfo] = None
    available_accounts: List[AccountInfo] = []
    positions: List[Position] = []
    recent_trades: List[Trade] = []
    active_signals: List[Signal] = []
    trading_metrics: TradingMetrics
    strategy_stats: List[StrategyStats] = []
    recent_logs: List[LogEntry] = []
    last_updated: datetime


class DashboardCommand(BaseModel):
    """Dashboard command structure."""
    action: str
    parameters: Optional[Dict[str, Any]] = None


class DashboardResponse(BaseModel):
    """Dashboard API response."""
    success: bool
    message: str
    data: Optional[Any] = None
    timestamp: datetime = datetime.now(timezone.utc)
