"""
Dashboard API Backend.
Real-time API for the trading dashboard with live system integration.
"""

import asyncio
import json
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware

from .models import (
    DashboardState, SystemStatus, AccountInfo, Position, Trade, Signal,
    LogEntry, TradingMetrics, StrategyStats, SystemHealth, LogLevel,
    DashboardCommand, DashboardResponse
)

# Import our real trading system components
from api import TopStepClient
from llm import QwenClient
from workflow import TradingWorkflow
from agents import (
    MarketDataAgent, FVGDetectionAgent, OrderBlocksAgent,
    LiquiditySweepsAgent, RiskManagementAgent, ExecutionAgent
)


class DashboardAPI:
    """Real-time dashboard API with live trading system integration."""
    
    def __init__(self):
        self.app = FastAPI(title="Trading Dashboard API", version="1.0.0")
        self.setup_cors()
        self.setup_routes()
        
        # Real system components
        self.topstep_client: Optional[TopStepClient] = None
        self.qwen_client: Optional[QwenClient] = None
        self.trading_workflow: Optional[TradingWorkflow] = None
        
        # System state
        self.system_status = SystemStatus.STOPPED
        self.selected_account_id: Optional[str] = None
        self.trading_task: Optional[asyncio.Task] = None
        self.websocket_connections: List[WebSocket] = []
        
        # Data storage
        self.log_entries: List[LogEntry] = []
        self.recent_trades: List[Trade] = []
        self.active_signals: List[Signal] = []
        self.system_start_time = datetime.now(timezone.utc)
        
    def setup_cors(self):
        """Setup CORS for frontend integration."""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """Setup API routes."""
        
        @self.app.get("/")
        async def dashboard_home():
            """Serve dashboard HTML."""
            return HTMLResponse(self.get_dashboard_html())
        
        @self.app.get("/api/status")
        async def get_system_status():
            """Get current system status."""
            try:
                dashboard_state = await self.get_dashboard_state()
                return DashboardResponse(
                    success=True,
                    message="Status retrieved successfully",
                    data=dashboard_state.dict()
                )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error getting status: {e}"
                )
        
        @self.app.get("/api/accounts")
        async def get_accounts():
            """Get available trading accounts."""
            try:
                accounts = await self.get_available_accounts()
                return DashboardResponse(
                    success=True,
                    message="Accounts retrieved successfully",
                    data=accounts
                )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error getting accounts: {e}"
                )
        
        @self.app.post("/api/select-account/{account_id}")
        async def select_account(account_id: str):
            """Select trading account."""
            try:
                success = await self.select_trading_account(account_id)
                if success:
                    await self.broadcast_update()
                    return DashboardResponse(
                        success=True,
                        message=f"Account {account_id} selected successfully"
                    )
                else:
                    return DashboardResponse(
                        success=False,
                        message="Failed to select account"
                    )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error selecting account: {e}"
                )
        
        @self.app.post("/api/start-trading")
        async def start_trading():
            """Start the trading system."""
            try:
                success = await self.start_trading_system()
                if success:
                    await self.broadcast_update()
                    return DashboardResponse(
                        success=True,
                        message="Trading system started successfully"
                    )
                else:
                    return DashboardResponse(
                        success=False,
                        message="Failed to start trading system"
                    )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error starting trading: {e}"
                )
        
        @self.app.post("/api/stop-trading")
        async def stop_trading():
            """Stop the trading system."""
            try:
                success = await self.stop_trading_system()
                if success:
                    await self.broadcast_update()
                    return DashboardResponse(
                        success=True,
                        message="Trading system stopped successfully"
                    )
                else:
                    return DashboardResponse(
                        success=False,
                        message="Failed to stop trading system"
                    )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error stopping trading: {e}"
                )
        
        @self.app.get("/api/positions")
        async def get_positions():
            """Get current positions."""
            try:
                positions = await self.get_current_positions()
                return DashboardResponse(
                    success=True,
                    message="Positions retrieved successfully",
                    data=positions
                )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error getting positions: {e}"
                )
        
        @self.app.get("/api/trades")
        async def get_trades():
            """Get recent trades."""
            try:
                return DashboardResponse(
                    success=True,
                    message="Trades retrieved successfully",
                    data=[trade.dict() for trade in self.recent_trades]
                )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error getting trades: {e}"
                )
        
        @self.app.get("/api/signals")
        async def get_signals():
            """Get active signals."""
            try:
                return DashboardResponse(
                    success=True,
                    message="Signals retrieved successfully",
                    data=[signal.dict() for signal in self.active_signals]
                )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error getting signals: {e}"
                )
        
        @self.app.get("/api/logs")
        async def get_logs():
            """Get system logs."""
            try:
                return DashboardResponse(
                    success=True,
                    message="Logs retrieved successfully",
                    data=[log.dict() for log in self.log_entries[-100:]]  # Last 100 logs
                )
            except Exception as e:
                return DashboardResponse(
                    success=False,
                    message=f"Error getting logs: {e}"
                )
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates."""
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                # Send initial state
                dashboard_state = await self.get_dashboard_state()
                await websocket.send_text(json.dumps({
                    "type": "dashboard_state",
                    "data": dashboard_state.dict()
                }, default=str))
                
                # Keep connection alive
                while True:
                    await websocket.receive_text()
                    
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
    
    async def get_available_accounts(self) -> List[Dict[str, Any]]:
        """Get available trading accounts from TopStep."""
        try:
            if not self.topstep_client:
                self.topstep_client = TopStepClient()
            
            async with self.topstep_client:
                accounts = []
                for account_data in self.topstep_client.available_accounts:
                    account = AccountInfo(
                        id=str(account_data.get('id')),
                        name=account_data.get('name', 'Unknown'),
                        balance=float(account_data.get('balance', 0)),
                        equity=float(account_data.get('equity', 0)),
                        margin_used=float(account_data.get('marginUsed', 0)),
                        margin_available=float(account_data.get('marginAvailable', 0)),
                        account_type=account_data.get('type', 'Unknown'),
                        status=account_data.get('status', 'Unknown')
                    )
                    accounts.append(account.dict())
                
                return accounts
                
        except Exception as e:
            self.add_log_entry(LogLevel.ERROR, f"Error getting accounts: {e}", "API")
            return []
    
    async def select_trading_account(self, account_id: str) -> bool:
        """Select a trading account."""
        try:
            self.selected_account_id = account_id
            self.add_log_entry(LogLevel.INFO, f"Selected account: {account_id}", "Dashboard")
            return True
        except Exception as e:
            self.add_log_entry(LogLevel.ERROR, f"Error selecting account: {e}", "Dashboard")
            return False
    
    async def start_trading_system(self) -> bool:
        """Start the real trading system."""
        try:
            if self.system_status == SystemStatus.RUNNING:
                return True
            
            self.system_status = SystemStatus.STARTING
            self.add_log_entry(LogLevel.INFO, "Starting trading system...", "System")
            
            # Initialize real components
            self.topstep_client = TopStepClient()
            self.qwen_client = QwenClient()
            
            # Initialize trading workflow
            async with self.topstep_client:
                self.trading_workflow = TradingWorkflow(
                    topstep_client=self.topstep_client,
                    qwen_client=self.qwen_client
                )
                
                # Start trading loop
                self.trading_task = asyncio.create_task(self.trading_loop())
                
                self.system_status = SystemStatus.RUNNING
                self.add_log_entry(LogLevel.INFO, "Trading system started successfully", "System")
                return True
                
        except Exception as e:
            self.system_status = SystemStatus.ERROR
            self.add_log_entry(LogLevel.ERROR, f"Error starting trading system: {e}", "System")
            return False
    
    async def stop_trading_system(self) -> bool:
        """Stop the trading system."""
        try:
            if self.system_status == SystemStatus.STOPPED:
                return True
            
            self.system_status = SystemStatus.STOPPING
            self.add_log_entry(LogLevel.INFO, "Stopping trading system...", "System")
            
            # Cancel trading task
            if self.trading_task:
                self.trading_task.cancel()
                try:
                    await self.trading_task
                except asyncio.CancelledError:
                    pass
            
            # Close connections
            if self.topstep_client:
                await self.topstep_client.close()
            
            self.system_status = SystemStatus.STOPPED
            self.add_log_entry(LogLevel.INFO, "Trading system stopped successfully", "System")
            return True
            
        except Exception as e:
            self.system_status = SystemStatus.ERROR
            self.add_log_entry(LogLevel.ERROR, f"Error stopping trading system: {e}", "System")
            return False
    
    async def trading_loop(self):
        """Main trading loop."""
        try:
            while self.system_status == SystemStatus.RUNNING:
                self.add_log_entry(LogLevel.INFO, "Running trading cycle...", "Trading")
                
                # Run trading workflow
                if self.trading_workflow:
                    result = await self.trading_workflow.run_trading_cycle(symbol="ES")
                    
                    # Process results
                    if result.get('success'):
                        self.add_log_entry(
                            LogLevel.INFO, 
                            f"Trading cycle completed - Signals: {result.get('signals_detected', {})}", 
                            "Trading"
                        )
                        
                        # Update signals and trades
                        await self.update_signals_and_trades(result)
                        
                    else:
                        self.add_log_entry(
                            LogLevel.WARNING, 
                            f"Trading cycle failed: {result.get('error_message')}", 
                            "Trading"
                        )
                
                # Broadcast updates to connected clients
                await self.broadcast_update()
                
                # Wait before next cycle
                await asyncio.sleep(60)  # 1 minute cycle
                
        except asyncio.CancelledError:
            self.add_log_entry(LogLevel.INFO, "Trading loop cancelled", "Trading")
        except Exception as e:
            self.add_log_entry(LogLevel.ERROR, f"Trading loop error: {e}", "Trading")
            self.system_status = SystemStatus.ERROR
    
    async def update_signals_and_trades(self, trading_result: Dict[str, Any]):
        """Update signals and trades from trading results."""
        try:
            # Update active signals
            if trading_result.get('selected_decision'):
                decision = trading_result['selected_decision']
                signal = Signal(
                    id=f"signal_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    strategy=decision.get('strategy_name', 'Unknown'),
                    symbol="ES",
                    action=decision.get('action', 'Unknown'),
                    confidence=decision.get('confidence', 0.0),
                    entry_price=decision.get('entry_price', 0.0),
                    stop_loss=decision.get('stop_loss', 0.0),
                    take_profit=decision.get('take_profit', 0.0),
                    timestamp=datetime.now(timezone.utc),
                    status="ACTIVE"
                )
                self.active_signals.append(signal)
                
                # Keep only last 10 signals
                self.active_signals = self.active_signals[-10:]
            
            # Update trades from execution results
            if trading_result.get('execution_results'):
                for execution in trading_result['execution_results']:
                    if execution.get('success'):
                        trade = Trade(
                            id=execution.get('order_id', f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                            symbol="ES",
                            side=execution.get('action', 'Unknown'),
                            size=execution.get('filled_size', 1),
                            price=execution.get('filled_price', 0.0),
                            timestamp=datetime.now(timezone.utc),
                            status="FILLED",
                            strategy=trading_result.get('selected_decision', {}).get('strategy_name', 'Unknown')
                        )
                        self.recent_trades.append(trade)
                        
                        # Keep only last 20 trades
                        self.recent_trades = self.recent_trades[-20:]
                        
        except Exception as e:
            self.add_log_entry(LogLevel.ERROR, f"Error updating signals/trades: {e}", "Dashboard")
    
    def _contract_to_symbol(self, contract_id: str) -> str:
        """Convert contract ID to readable symbol."""
        if not contract_id or contract_id == 'Unknown':
            return 'Unknown'

        # Contract mapping
        contract_mapping = {
            'CON.F.US.EP.U25': 'ES',
            'CON.F.US.ENQ.U25': 'NQ',
            'CON.F.US.YM.U25': 'YM',
            'CON.F.US.RTY.U25': 'RTY',
            'CON.F.US.CL.U25': 'CL',
            'CON.F.US.GC.U25': 'GC'
        }

        return contract_mapping.get(contract_id, contract_id)

    async def get_current_positions(self) -> List[Dict[str, Any]]:
        """Get current trading positions."""
        try:
            if not self.topstep_client:
                return []

            positions_data = await self.topstep_client.get_positions()
            positions = []

            for pos_data in positions_data:
                # Convert contract_id to symbol
                contract_id = pos_data.get('contract_id', 'Unknown')
                symbol = self._contract_to_symbol(contract_id)

                position = Position(
                    id=str(pos_data.get('id', 'unknown')),
                    symbol=symbol,
                    side="LONG" if pos_data.get('size', 0) > 0 else "SHORT",
                    size=abs(pos_data.get('size', 0)),
                    entry_price=float(pos_data.get('averagePrice', 0)),
                    current_price=float(pos_data.get('currentPrice', 0)),
                    unrealized_pnl=float(pos_data.get('unrealizedPnl', 0)),
                    timestamp=datetime.now(timezone.utc)
                )
                positions.append(position.dict())

            return positions
            
        except Exception as e:
            self.add_log_entry(LogLevel.ERROR, f"Error getting positions: {e}", "API")
            return []
    
    def add_log_entry(self, level: LogLevel, message: str, component: str, details: Optional[Dict[str, Any]] = None):
        """Add a log entry."""
        log_entry = LogEntry(
            timestamp=datetime.now(timezone.utc),
            level=level,
            message=message,
            component=component,
            details=details
        )
        self.log_entries.append(log_entry)
        
        # Keep only last 1000 log entries
        self.log_entries = self.log_entries[-1000:]
    
    async def get_dashboard_state(self) -> DashboardState:
        """Get complete dashboard state."""
        try:
            # Get system health
            system_health = SystemHealth(
                api_status="CONNECTED" if self.topstep_client else "DISCONNECTED",
                database_status="CONNECTED",  # Assume connected
                llm_status="CONNECTED" if self.qwen_client else "DISCONNECTED",
                market_data_status="CONNECTED" if self.system_status == SystemStatus.RUNNING else "DISCONNECTED",
                last_heartbeat=datetime.now(timezone.utc),
                uptime_seconds=int((datetime.now(timezone.utc) - self.system_start_time).total_seconds())
            )
            
            # Get accounts
            available_accounts_data = await self.get_available_accounts()
            available_accounts = [AccountInfo(**acc) for acc in available_accounts_data]
            
            # Get selected account
            selected_account = None
            if self.selected_account_id:
                for acc in available_accounts:
                    if acc.id == self.selected_account_id:
                        selected_account = acc
                        break
            
            # Get positions
            positions_data = await self.get_current_positions()
            positions = [Position(**pos) for pos in positions_data]
            
            # Calculate trading metrics
            total_trades = len(self.recent_trades)
            winning_trades = len([t for t in self.recent_trades if t.pnl and t.pnl > 0])
            losing_trades = len([t for t in self.recent_trades if t.pnl and t.pnl < 0])
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            total_pnl = sum([t.pnl for t in self.recent_trades if t.pnl]) or 0
            
            trading_metrics = TradingMetrics(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_pnl=total_pnl,
                daily_pnl=total_pnl,  # Simplified
                max_drawdown=0.0  # Would calculate from trade history
            )
            
            # Strategy stats
            strategy_stats = []
            for strategy_name in ["FVG", "OrderBlocks", "LiquiditySweeps"]:
                strategy_signals = [s for s in self.active_signals if s.strategy == strategy_name]
                strategy_trades = [t for t in self.recent_trades if t.strategy == strategy_name]
                
                if strategy_signals or strategy_trades:
                    stats = StrategyStats(
                        name=strategy_name,
                        signals_generated=len(strategy_signals),
                        trades_executed=len(strategy_trades),
                        win_rate=0.0,  # Would calculate from trades
                        avg_win=0.0,
                        avg_loss=0.0,
                        total_pnl=sum([t.pnl for t in strategy_trades if t.pnl]) or 0
                    )
                    strategy_stats.append(stats)
            
            return DashboardState(
                system_status=self.system_status,
                system_health=system_health,
                selected_account=selected_account,
                available_accounts=available_accounts,
                positions=positions,
                recent_trades=self.recent_trades,
                active_signals=self.active_signals,
                trading_metrics=trading_metrics,
                strategy_stats=strategy_stats,
                recent_logs=self.log_entries[-50:],  # Last 50 logs
                last_updated=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            self.add_log_entry(LogLevel.ERROR, f"Error getting dashboard state: {e}", "Dashboard")
            # Return minimal state on error
            return DashboardState(
                system_status=SystemStatus.ERROR,
                system_health=SystemHealth(
                    api_status="ERROR",
                    database_status="ERROR",
                    llm_status="ERROR",
                    market_data_status="ERROR",
                    last_heartbeat=datetime.now(timezone.utc),
                    uptime_seconds=0
                ),
                trading_metrics=TradingMetrics(
                    total_trades=0,
                    winning_trades=0,
                    losing_trades=0,
                    win_rate=0.0,
                    total_pnl=0.0,
                    daily_pnl=0.0,
                    max_drawdown=0.0
                ),
                last_updated=datetime.now(timezone.utc)
            )
    
    async def broadcast_update(self):
        """Broadcast dashboard update to all connected WebSocket clients."""
        if not self.websocket_connections:
            return
        
        try:
            dashboard_state = await self.get_dashboard_state()
            message = json.dumps({
                "type": "dashboard_update",
                "data": dashboard_state.dict()
            }, default=str)
            
            # Send to all connected clients
            disconnected = []
            for websocket in self.websocket_connections:
                try:
                    await websocket.send_text(message)
                except:
                    disconnected.append(websocket)
            
            # Remove disconnected clients
            for websocket in disconnected:
                self.websocket_connections.remove(websocket)
                
        except Exception as e:
            self.add_log_entry(LogLevel.ERROR, f"Error broadcasting update: {e}", "WebSocket")
    
    def get_dashboard_html(self) -> str:
        """Get dashboard HTML content."""
        return """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Trading Dashboard</title>
            <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
            <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
            <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
            <style>
                .status-running { color: #10B981; }
                .status-stopped { color: #EF4444; }
                .status-starting { color: #F59E0B; }
                .status-error { color: #DC2626; }
                .log-info { color: #3B82F6; }
                .log-warning { color: #F59E0B; }
                .log-error { color: #EF4444; }
                .log-debug { color: #6B7280; }
            </style>
        </head>
        <body>
            <div id="dashboard-root"></div>
            <script type="text/babel" src="/static/dashboard.js"></script>
        </body>
        </html>
        """
