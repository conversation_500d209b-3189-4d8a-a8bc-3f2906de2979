// Trading Dashboard React Frontend
// Real-time dashboard with live system integration

const { useState, useEffect, useCallback } = React;

// Dashboard Components
const StatusIndicator = ({ status, label }) => {
    const getStatusClass = (status) => {
        switch (status) {
            case 'RUNNING': return 'status-running';
            case 'STOPPED': return 'status-stopped';
            case 'STARTING': return 'status-starting';
            case 'ERROR': return 'status-error';
            default: return 'text-gray-500';
        }
    };

    return (
        <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${getStatusClass(status)}`}></div>
            <span className="text-sm font-medium">{label}: {status}</span>
        </div>
    );
};

const AccountSelector = ({ accounts, selectedAccount, onSelectAccount }) => {
    return (
        <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-3">Trading Account</h3>
            <select 
                className="w-full p-2 border rounded"
                value={selectedAccount?.id || ''}
                onChange={(e) => onSelectAccount(e.target.value)}
            >
                <option value="">Select Account</option>
                {accounts.map(account => (
                    <option key={account.id} value={account.id}>
                        {account.name} - ${account.balance.toLocaleString()}
                    </option>
                ))}
            </select>
            {selectedAccount && (
                <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                    <div>Balance: ${selectedAccount.balance.toLocaleString()}</div>
                    <div>Equity: ${selectedAccount.equity.toLocaleString()}</div>
                    <div>Margin Used: ${selectedAccount.margin_used.toLocaleString()}</div>
                    <div>Available: ${selectedAccount.margin_available.toLocaleString()}</div>
                </div>
            )}
        </div>
    );
};

const SystemControls = ({ systemStatus, onStart, onStop }) => {
    const isRunning = systemStatus === 'RUNNING';
    const isStarting = systemStatus === 'STARTING';
    const isStopping = systemStatus === 'STOPPING';

    return (
        <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-3">System Control</h3>
            <div className="flex space-x-2">
                <button
                    onClick={onStart}
                    disabled={isRunning || isStarting}
                    className={`px-4 py-2 rounded font-medium ${
                        isRunning || isStarting 
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-green-500 text-white hover:bg-green-600'
                    }`}
                >
                    {isStarting ? 'Starting...' : 'Start Trading'}
                </button>
                <button
                    onClick={onStop}
                    disabled={!isRunning || isStopping}
                    className={`px-4 py-2 rounded font-medium ${
                        !isRunning || isStopping
                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-red-500 text-white hover:bg-red-600'
                    }`}
                >
                    {isStopping ? 'Stopping...' : 'Stop Trading'}
                </button>
            </div>
        </div>
    );
};

const PositionsTable = ({ positions }) => {
    const formatDateTime = (timestamp) => {
        return new Date(timestamp).toLocaleString();
    };

    const calculatePnLPercentage = (position) => {
        if (!position.entry_price || position.entry_price === 0) return 0;
        const priceDiff = position.current_price - position.entry_price;
        const percentage = (priceDiff / position.entry_price) * 100;
        return position.side === 'LONG' ? percentage : -percentage;
    };

    return (
        <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-3">Current Positions</h3>
            {positions.length === 0 ? (
                <p className="text-gray-500">No open positions</p>
            ) : (
                <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                        <thead>
                            <tr className="border-b bg-gray-50">
                                <th className="text-left p-2 font-semibold">Symbol</th>
                                <th className="text-left p-2 font-semibold">Side</th>
                                <th className="text-left p-2 font-semibold">Size</th>
                                <th className="text-left p-2 font-semibold">Entry Price</th>
                                <th className="text-left p-2 font-semibold">Current Price</th>
                                <th className="text-left p-2 font-semibold">Unrealized P&L</th>
                                <th className="text-left p-2 font-semibold">P&L %</th>
                                <th className="text-left p-2 font-semibold">Position ID</th>
                                <th className="text-left p-2 font-semibold">Account</th>
                                <th className="text-left p-2 font-semibold">Entry Time</th>
                                <th className="text-left p-2 font-semibold">Contract ID</th>
                            </tr>
                        </thead>
                        <tbody>
                            {positions.map(position => {
                                const pnlPercentage = calculatePnLPercentage(position);
                                return (
                                    <tr key={position.id} className="border-b hover:bg-gray-50">
                                        <td className="p-2 font-medium">{position.symbol}</td>
                                        <td className="p-2">
                                            <span className={`px-2 py-1 rounded text-xs font-semibold ${
                                                position.side === 'LONG'
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {position.side}
                                            </span>
                                        </td>
                                        <td className="p-2 font-medium">{position.size}</td>
                                        <td className="p-2">${position.entry_price?.toFixed(2) || 'N/A'}</td>
                                        <td className="p-2">${position.current_price?.toFixed(2) || 'N/A'}</td>
                                        <td className={`p-2 font-semibold ${
                                            (position.unrealized_pnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                            ${(position.unrealized_pnl || 0).toFixed(2)}
                                        </td>
                                        <td className={`p-2 font-semibold ${
                                            pnlPercentage >= 0 ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                            {pnlPercentage.toFixed(2)}%
                                        </td>
                                        <td className="p-2 text-xs text-gray-600">{position.id}</td>
                                        <td className="p-2 text-xs text-gray-600">{position.account_id || 'N/A'}</td>
                                        <td className="p-2 text-xs text-gray-600">
                                            {position.creation_timestamp ? formatDateTime(position.creation_timestamp) : 'N/A'}
                                        </td>
                                        <td className="p-2 text-xs text-gray-600" title={position.contract_id}>
                                            {position.contract_id ? position.contract_id.substring(0, 15) + '...' : 'N/A'}
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

const TradesTable = ({ trades }) => {
    return (
        <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-3">Recent Trades</h3>
            {trades.length === 0 ? (
                <p className="text-gray-500">No recent trades</p>
            ) : (
                <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                        <thead>
                            <tr className="border-b">
                                <th className="text-left p-2">Time</th>
                                <th className="text-left p-2">Symbol</th>
                                <th className="text-left p-2">Side</th>
                                <th className="text-left p-2">Size</th>
                                <th className="text-left p-2">Price</th>
                                <th className="text-left p-2">Strategy</th>
                                <th className="text-left p-2">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {trades.slice(-10).reverse().map(trade => (
                                <tr key={trade.id} className="border-b">
                                    <td className="p-2">{new Date(trade.timestamp).toLocaleTimeString()}</td>
                                    <td className="p-2">{trade.symbol}</td>
                                    <td className="p-2">
                                        <span className={trade.side === 'BUY' ? 'text-green-600' : 'text-red-600'}>
                                            {trade.side}
                                        </span>
                                    </td>
                                    <td className="p-2">{trade.size}</td>
                                    <td className="p-2">{trade.price.toFixed(2)}</td>
                                    <td className="p-2">{trade.strategy}</td>
                                    <td className="p-2">
                                        <span className={`px-2 py-1 rounded text-xs ${
                                            trade.status === 'FILLED' ? 'bg-green-100 text-green-800' :
                                            trade.status === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                                            'bg-yellow-100 text-yellow-800'
                                        }`}>
                                            {trade.status}
                                        </span>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

const SignalsTable = ({ signals }) => {
    const formatDateTime = (timestamp) => {
        return new Date(timestamp).toLocaleString();
    };

    const calculateRiskReward = (entry, stop, target) => {
        if (!entry || !stop || !target) return 'N/A';
        const risk = Math.abs(entry - stop);
        const reward = Math.abs(target - entry);
        return risk > 0 ? (reward / risk).toFixed(2) : 'N/A';
    };

    const getConfidenceColor = (confidence) => {
        if (confidence >= 0.8) return 'text-green-600 bg-green-100';
        if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
        return 'text-red-600 bg-red-100';
    };

    return (
        <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-3">Active Signals</h3>
            {signals.length === 0 ? (
                <p className="text-gray-500">No active signals</p>
            ) : (
                <div className="overflow-x-auto">
                    <table className="w-full text-xs">
                        <thead>
                            <tr className="border-b bg-gray-50">
                                <th className="text-left p-2 font-semibold">Time</th>
                                <th className="text-left p-2 font-semibold">Strategy</th>
                                <th className="text-left p-2 font-semibold">Symbol</th>
                                <th className="text-left p-2 font-semibold">Action</th>
                                <th className="text-left p-2 font-semibold">Confidence</th>
                                <th className="text-left p-2 font-semibold">Entry Price</th>
                                <th className="text-left p-2 font-semibold">Stop Loss</th>
                                <th className="text-left p-2 font-semibold">Take Profit</th>
                                <th className="text-left p-2 font-semibold">R:R Ratio</th>
                                <th className="text-left p-2 font-semibold">Reasoning</th>
                                <th className="text-left p-2 font-semibold">Metadata</th>
                            </tr>
                        </thead>
                        <tbody>
                            {signals.slice(-10).reverse().map(signal => {
                                const riskReward = calculateRiskReward(signal.entry_price, signal.stop_loss, signal.take_profit);
                                return (
                                    <tr key={signal.id} className="border-b hover:bg-gray-50">
                                        <td className="p-2 text-xs">{formatDateTime(signal.timestamp)}</td>
                                        <td className="p-2">
                                            <span className="px-2 py-1 rounded text-xs font-semibold bg-blue-100 text-blue-800">
                                                {signal.strategy}
                                            </span>
                                        </td>
                                        <td className="p-2 font-medium">{signal.symbol}</td>
                                        <td className="p-2">
                                            <span className={`px-2 py-1 rounded text-xs font-semibold ${
                                                signal.action === 'BUY'
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {signal.action}
                                            </span>
                                        </td>
                                        <td className="p-2">
                                            <span className={`px-2 py-1 rounded text-xs font-semibold ${getConfidenceColor(signal.confidence)}`}>
                                                {(signal.confidence * 100).toFixed(1)}%
                                            </span>
                                        </td>
                                        <td className="p-2 font-medium">${signal.entry_price?.toFixed(2) || 'N/A'}</td>
                                        <td className="p-2 text-red-600">${signal.stop_loss?.toFixed(2) || 'N/A'}</td>
                                        <td className="p-2 text-green-600">${signal.take_profit?.toFixed(2) || 'N/A'}</td>
                                        <td className="p-2 font-medium">
                                            <span className={`px-1 py-0.5 rounded text-xs ${
                                                parseFloat(riskReward) >= 2 ? 'bg-green-100 text-green-800' :
                                                parseFloat(riskReward) >= 1 ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-red-100 text-red-800'
                                            }`}>
                                                1:{riskReward}
                                            </span>
                                        </td>
                                        <td className="p-2 max-w-xs truncate" title={signal.reasoning}>
                                            {signal.reasoning || 'No reasoning provided'}
                                        </td>
                                        <td className="p-2 text-xs text-gray-600">
                                            {signal.metadata ? (
                                                <div className="space-y-1">
                                                    {Object.entries(signal.metadata).slice(0, 3).map(([key, value]) => (
                                                        <div key={key} className="text-xs">
                                                            <span className="font-medium">{key}:</span> {String(value).substring(0, 20)}
                                                        </div>
                                                    ))}
                                                </div>
                                            ) : 'No metadata'}
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

const TradingMetrics = ({ metrics }) => {
    return (
        <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-3">Trading Metrics</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.total_trades}</div>
                    <div className="text-sm text-gray-600">Total Trades</div>
                </div>
                <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{metrics.winning_trades}</div>
                    <div className="text-sm text-gray-600">Winners</div>
                </div>
                <div className="text-center">
                    <div className="text-2xl font-bold">{metrics.win_rate.toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">Win Rate</div>
                </div>
                <div className="text-center">
                    <div className={`text-2xl font-bold ${metrics.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        ${metrics.total_pnl.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-600">Total P&L</div>
                </div>
            </div>
        </div>
    );
};

const SystemLogs = ({ logs }) => {
    const getLogClass = (level) => {
        switch (level) {
            case 'INFO': return 'log-info';
            case 'WARNING': return 'log-warning';
            case 'ERROR': return 'log-error';
            case 'DEBUG': return 'log-debug';
            default: return 'text-gray-600';
        }
    };

    return (
        <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-3">System Logs</h3>
            <div className="h-64 overflow-y-auto bg-gray-50 p-3 rounded text-sm font-mono">
                {logs.length === 0 ? (
                    <p className="text-gray-500">No logs available</p>
                ) : (
                    logs.slice(-20).reverse().map((log, index) => (
                        <div key={index} className="mb-1">
                            <span className="text-gray-400">
                                {new Date(log.timestamp).toLocaleTimeString()}
                            </span>
                            <span className={`ml-2 ${getLogClass(log.level)}`}>
                                [{log.level}]
                            </span>
                            <span className="ml-2 text-gray-600">
                                [{log.component}]
                            </span>
                            <span className="ml-2">
                                {log.message}
                            </span>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

// Main Dashboard Component
const TradingDashboard = () => {
    const [dashboardState, setDashboardState] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [websocket, setWebsocket] = useState(null);

    // API calls
    const apiCall = useCallback(async (endpoint, options = {}) => {
        try {
            const response = await fetch(`/api${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.message);
            }
            return data;
        } catch (err) {
            setError(err.message);
            throw err;
        }
    }, []);

    // Load initial data
    useEffect(() => {
        const loadInitialData = async () => {
            try {
                setLoading(true);
                const response = await apiCall('/status');
                setDashboardState(response.data);
                setError(null);
            } catch (err) {
                console.error('Failed to load initial data:', err);
            } finally {
                setLoading(false);
            }
        };

        loadInitialData();
    }, [apiCall]);

    // Setup WebSocket connection
    useEffect(() => {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        const ws = new WebSocket(wsUrl);
        
        ws.onopen = () => {
            console.log('WebSocket connected');
            setWebsocket(ws);
        };
        
        ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                if (message.type === 'dashboard_update' || message.type === 'dashboard_state') {
                    setDashboardState(message.data);
                }
            } catch (err) {
                console.error('WebSocket message error:', err);
            }
        };
        
        ws.onclose = () => {
            console.log('WebSocket disconnected');
            setWebsocket(null);
        };
        
        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };

        return () => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        };
    }, []);

    // Event handlers
    const handleSelectAccount = async (accountId) => {
        try {
            await apiCall(`/select-account/${accountId}`, { method: 'POST' });
        } catch (err) {
            console.error('Failed to select account:', err);
        }
    };

    const handleStartTrading = async () => {
        try {
            await apiCall('/start-trading', { method: 'POST' });
        } catch (err) {
            console.error('Failed to start trading:', err);
        }
    };

    const handleStopTrading = async () => {
        try {
            await apiCall('/stop-trading', { method: 'POST' });
        } catch (err) {
            console.error('Failed to stop trading:', err);
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <div className="text-xl">Loading dashboard...</div>
            </div>
        );
    }

    if (error && !dashboardState) {
        return (
            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                <div className="text-xl text-red-600">Error: {error}</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-100">
            {/* Header */}
            <header className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-4">
                        <h1 className="text-2xl font-bold text-gray-900">Trading Dashboard</h1>
                        <div className="flex items-center space-x-4">
                            <StatusIndicator 
                                status={dashboardState?.system_status || 'UNKNOWN'} 
                                label="System" 
                            />
                            <div className="text-sm text-gray-500">
                                Last Updated: {dashboardState?.last_updated ? 
                                    new Date(dashboardState.last_updated).toLocaleTimeString() : 'Never'}
                            </div>
                            <div className={`w-3 h-3 rounded-full ${websocket ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                {error && (
                    <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        {error}
                    </div>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Left Column */}
                    <div className="space-y-6">
                        <AccountSelector
                            accounts={dashboardState?.available_accounts || []}
                            selectedAccount={dashboardState?.selected_account}
                            onSelectAccount={handleSelectAccount}
                        />
                        <SystemControls
                            systemStatus={dashboardState?.system_status}
                            onStart={handleStartTrading}
                            onStop={handleStopTrading}
                        />
                        <TradingMetrics metrics={dashboardState?.trading_metrics || {
                            total_trades: 0, winning_trades: 0, losing_trades: 0,
                            win_rate: 0, total_pnl: 0, daily_pnl: 0, max_drawdown: 0
                        }} />
                    </div>

                    {/* Middle Column */}
                    <div className="space-y-6">
                        <PositionsTable positions={dashboardState?.positions || []} />
                        <SignalsTable signals={dashboardState?.active_signals || []} />
                    </div>

                    {/* Right Column */}
                    <div className="space-y-6">
                        <TradesTable trades={dashboardState?.recent_trades || []} />
                        <SystemLogs logs={dashboardState?.recent_logs || []} />
                    </div>
                </div>
            </main>
        </div>
    );
};

// Render the dashboard
ReactDOM.render(<TradingDashboard />, document.getElementById('dashboard-root'));
