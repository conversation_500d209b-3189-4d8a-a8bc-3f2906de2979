#!/usr/bin/env python3
"""
Test market data sources for trading agents.
"""

import os
import sys
import asyncio
import ssl
import aiohttp
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_topstep_market_data():
    """Test TopStep market data API."""
    print("🔍 Testing TopStep Market Data API...")
    
    # Authenticate first
    username = os.getenv('TOPSTEP_USERNAME')
    api_key = os.getenv('TOPSTEP_API_KEY')
    base_url = os.getenv('TOPSTEP_GATEWAY_URL', 'https://api.topstepx.com')
    
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    
    async with aiohttp.ClientSession(connector=connector) as session:
        # Authenticate
        print("🔐 Authenticating...")
        auth_payload = {"userName": username, "apiKey": api_key}
        auth_headers = {'accept': 'text/plain', 'Content-Type': 'application/json'}
        
        async with session.post(f"{base_url}/api/Auth/loginKey", json=auth_payload, headers=auth_headers) as response:
            if response.status != 200:
                print(f"❌ Authentication failed: {response.status}")
                return False
            
            result = await response.json()
            if not result.get('success'):
                print(f"❌ Authentication failed: {result.get('errorMessage')}")
                return False
            
            token = result.get('token')
            print(f"✅ Authenticated successfully")
        
        # Headers for authenticated requests
        headers = {
            'accept': 'text/plain',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }
        
        # Test historical market data
        print("\n📊 Testing Historical Market Data...")
        
        # Get recent data for ES futures
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=2)
        
        market_data_request = {
            "contractId": "CON.F.US.EP.U25",  # ES futures
            "live": False,
            "startTime": start_time.isoformat(),
            "endTime": end_time.isoformat(),
            "unit": 2,  # Minutes
            "unitNumber": 1,  # 1-minute bars
            "limit": 10,
            "includePartialBar": False
        }
        
        print(f"   Requesting data for: {market_data_request['contractId']}")
        print(f"   Time range: {start_time.strftime('%H:%M')} - {end_time.strftime('%H:%M')} UTC")
        print(f"   Request: {market_data_request}")
        
        try:
            async with session.post(f"{base_url}/api/History/retrieveBars", json=market_data_request, headers=headers) as response:
                response_text = await response.text()
                print(f"   Response Status: {response.status}")
                print(f"   Response: {response_text}")
                
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        bars = data.get('bars', [])
                        print(f"   ✅ Retrieved {len(bars)} market data bars")
                        
                        if bars:
                            print("   📈 Sample bars:")
                            for i, bar in enumerate(bars[:3]):
                                timestamp = bar.get('t', 'N/A')
                                ohlc = f"O:{bar.get('o')} H:{bar.get('h')} L:{bar.get('l')} C:{bar.get('c')}"
                                volume = f"V:{bar.get('v')}"
                                print(f"      {i+1}. {timestamp} - {ohlc} {volume}")
                            return True
                        else:
                            print("   ⚠️ No bars returned (might be outside market hours)")
                            return True  # Still successful, just no data
                    else:
                        print(f"   ❌ API returned error: {data.get('errorMessage')}")
                        return False
                else:
                    print(f"   ❌ HTTP error: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return False

async def test_alternative_data_sources():
    """Test alternative market data sources."""
    print("\n🔍 Testing Alternative Market Data Sources...")
    
    # Test free data sources
    sources = [
        {
            "name": "Yahoo Finance",
            "url": "https://query1.finance.yahoo.com/v8/finance/chart/ES=F",
            "description": "Free, delayed data for ES futures"
        },
        {
            "name": "Alpha Vantage", 
            "url": "https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol=SPY&interval=1min&apikey=demo",
            "description": "Free API with rate limits (SPY as ES proxy)"
        },
        {
            "name": "Polygon.io",
            "url": "https://api.polygon.io/v2/aggs/ticker/ES/range/1/minute/2024-01-01/2024-01-02?apikey=demo",
            "description": "Professional data (paid)"
        }
    ]
    
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    
    async with aiohttp.ClientSession(connector=connector) as session:
        for source in sources:
            print(f"\n   Testing {source['name']}...")
            print(f"   Description: {source['description']}")
            
            try:
                async with session.get(source['url'], timeout=10) as response:
                    print(f"   Status: {response.status}")
                    
                    if response.status == 200:
                        response_text = await response.text()
                        print(f"   ✅ {source['name']} accessible")
                        print(f"   Response preview: {response_text[:200]}...")
                    else:
                        print(f"   ❌ {source['name']} failed with status {response.status}")
                        
            except Exception as e:
                print(f"   ❌ {source['name']} error: {e}")

def analyze_market_data_requirements():
    """Analyze what market data we need for trading strategies."""
    print("\n📋 Market Data Requirements Analysis...")
    
    strategies = {
        "Fair Value Gaps (FVG)": {
            "data_needed": ["OHLC bars", "Volume"],
            "timeframes": ["1m", "5m", "15m"],
            "lookback": "50-100 bars",
            "real_time": "Yes - for entry timing"
        },
        "Order Blocks": {
            "data_needed": ["OHLC bars", "Volume", "Price levels"],
            "timeframes": ["5m", "15m", "1h"],
            "lookback": "100-200 bars", 
            "real_time": "Yes - for confirmation"
        },
        "Liquidity Sweeps": {
            "data_needed": ["OHLC bars", "Volume", "High/Low levels"],
            "timeframes": ["1m", "5m"],
            "lookback": "20-50 bars",
            "real_time": "Yes - critical for detection"
        }
    }
    
    print("   Strategy Requirements:")
    for strategy, requirements in strategies.items():
        print(f"\n   📊 {strategy}:")
        for key, value in requirements.items():
            print(f"      {key}: {value}")
    
    print("\n   💡 Recommendations:")
    print("      1. Primary: TopStep API (if available) - real-time, accurate")
    print("      2. Backup: Yahoo Finance - free, 15-min delayed")
    print("      3. Professional: Polygon.io/Alpha Vantage - paid, real-time")
    print("      4. Hybrid: TopStep for trading + Yahoo for backtesting")

async def main():
    """Test all market data sources."""
    print("🚀 TESTING MARKET DATA SOURCES")
    print("=" * 60)
    
    # Test TopStep market data
    topstep_works = await test_topstep_market_data()
    
    # Test alternative sources
    await test_alternative_data_sources()
    
    # Analyze requirements
    analyze_market_data_requirements()
    
    print("\n" + "=" * 60)
    print("📊 MARKET DATA ANALYSIS COMPLETE")
    
    if topstep_works:
        print("✅ TopStep provides market data - use as primary source")
    else:
        print("❌ TopStep market data issues - need alternative source")
    
    print("💡 Recommendation: Test TopStep data during market hours")
    print("💡 Backup plan: Implement Yahoo Finance for development/testing")

if __name__ == "__main__":
    asyncio.run(main())
