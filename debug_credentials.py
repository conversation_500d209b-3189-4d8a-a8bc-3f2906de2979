#!/usr/bin/env python3
"""
Debug credential loading and API calls.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🔍 Debugging Credential Loading")
print("=" * 50)

# Check .env file exists
env_file = ".env"
if os.path.exists(env_file):
    print(f"✅ .env file exists at: {os.path.abspath(env_file)}")
    
    # Read and display .env contents (safely)
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    print(f"📄 .env file contents ({len(lines)} lines):")
    for i, line in enumerate(lines[:20], 1):  # Show first 20 lines
        if any(key in line.upper() for key in ['USERNAME', 'API_KEY']):
            # Mask sensitive data
            if '=' in line:
                key, value = line.split('=', 1)
                masked_value = value[:10] + "..." if len(value.strip()) > 10 else value
                print(f"  {i:2d}: {key}={masked_value.strip()}")
            else:
                print(f"  {i:2d}: {line.strip()}")
        else:
            print(f"  {i:2d}: {line.strip()}")
else:
    print(f"❌ .env file not found at: {os.path.abspath(env_file)}")

print()

# Check environment variables
print("🔍 Environment Variables:")
topstep_username = os.getenv("TOPSTEP_USERNAME")
topstep_api_key = os.getenv("TOPSTEP_API_KEY")
qwen_api_key = os.getenv("QWEN_API_KEY")

print(f"  TOPSTEP_USERNAME: {topstep_username}")
print(f"  TOPSTEP_API_KEY: {topstep_api_key[:20]}..." if topstep_api_key else "  TOPSTEP_API_KEY: None")
print(f"  QWEN_API_KEY: {qwen_api_key[:20]}..." if qwen_api_key else "  QWEN_API_KEY: None")

print()

# Validate credentials format
print("🔍 Credential Validation:")

if topstep_username:
    print(f"  ✅ TopStep Username: '{topstep_username}' (length: {len(topstep_username)})")
else:
    print("  ❌ TopStep Username: Missing")

if topstep_api_key:
    print(f"  ✅ TopStep API Key: Present (length: {len(topstep_api_key)})")
    if topstep_api_key.endswith('='):
        print("    ℹ️  Appears to be base64 encoded (ends with =)")
    else:
        print("    ⚠️  Does not appear to be base64 encoded")
else:
    print("  ❌ TopStep API Key: Missing")

if qwen_api_key:
    print(f"  ✅ Qwen API Key: Present (length: {len(qwen_api_key)})")
    if qwen_api_key.startswith('sk-'):
        print("    ✅ Correct format (starts with sk-)")
    else:
        print("    ⚠️  Unexpected format (should start with sk-)")
else:
    print("  ❌ Qwen API Key: Missing")

print()
print("🔍 Next Steps:")
if topstep_username and topstep_api_key:
    print("  ✅ TopStep credentials loaded - ready for API testing")
else:
    print("  ❌ TopStep credentials missing - check .env file")

if qwen_api_key and qwen_api_key.startswith('sk-'):
    print("  ✅ Qwen credentials loaded - ready for API testing")
else:
    print("  ❌ Qwen credentials missing or invalid format - check .env file")
