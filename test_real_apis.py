#!/usr/bin/env python3
"""
REAL API TESTING with actual TopStep and Qwen credentials.
Tests all components with real API calls and data.
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import after path setup
from core import configure_logging, get_logger, get_settings, init_database
from api import TopStepClient
from llm import QwenClient
from llm.models import LLMPrompt

logger = get_logger(__name__)


async def test_core_configuration():
    """Test core configuration with real environment variables."""
    print("🔍 Testing core configuration with real credentials...")
    
    try:
        settings = get_settings()
        
        # Verify TopStep credentials are loaded
        assert settings.topstep.username == "mrrain"
        assert settings.topstep.api_key.get_secret_value() == "FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs="
        assert settings.topstep.environment.value == "demo"
        
        # Verify Qwen credentials are loaded
        assert settings.qwen.api_key.get_secret_value() == "sk-be8c17f1999b46f380e1a1e2dc687b53"
        assert settings.qwen.model == "qwen-turbo"
        
        # Test URL generation
        assert "demo" in settings.topstep.base_url
        assert "demo" in settings.topstep.websocket_url
        
        print("✅ Core configuration loaded real credentials successfully")
        return True
        
    except Exception as e:
        print(f"❌ Core configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_authentication():
    """Test real TopStep API authentication."""
    print("🔍 Testing TopStep API authentication with real credentials...")
    
    try:
        async with TopStepClient() as client:
            # Test authentication
            await client._ensure_authenticated()
            assert client.token is not None
            assert client.token_expires_at is not None
            
            # Test session validation
            is_valid = await client.validate_session()
            assert is_valid == True
            
            print(f"✅ TopStep authentication successful - Token: {client.token[:20]}...")
            return True
            
    except Exception as e:
        print(f"❌ TopStep authentication test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_accounts():
    """Test real TopStep account retrieval."""
    print("🔍 Testing TopStep account retrieval...")
    
    try:
        async with TopStepClient() as client:
            accounts = await client.get_accounts()
            
            assert len(accounts) > 0, "No accounts found"
            
            for account in accounts:
                print(f"  Account: {account.name} (ID: {account.id}) - Balance: ${account.balance}")
                assert account.id > 0
                assert account.name is not None
                assert account.balance >= 0
            
            print(f"✅ Retrieved {len(accounts)} TopStep accounts successfully")
            return True
            
    except Exception as e:
        print(f"❌ TopStep accounts test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_contracts():
    """Test real TopStep contract retrieval."""
    print("🔍 Testing TopStep contract retrieval...")
    
    try:
        async with TopStepClient() as client:
            # Test contract search
            contracts = await client.search_contracts("ES", limit=5)
            
            assert len(contracts) > 0, "No contracts found"
            
            for contract in contracts[:3]:  # Show first 3
                print(f"  Contract: {contract.symbol} - {contract.name}")
                assert contract.id is not None
                assert contract.symbol is not None
            
            print(f"✅ Retrieved {len(contracts)} contracts successfully")
            return True
            
    except Exception as e:
        print(f"❌ TopStep contracts test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_market_data():
    """Test real TopStep market data retrieval."""
    print("🔍 Testing TopStep market data retrieval...")
    
    try:
        async with TopStepClient() as client:
            # Get recent market data for ES
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=2)
            
            market_data = await client.get_market_data(
                contract_id="CON.F.US.EP.U25",  # ES futures
                start_time=start_time,
                end_time=end_time,
                timeframe="1m",
                limit=10
            )
            
            assert market_data.success == True
            assert len(market_data.bars) > 0
            
            # Verify data structure
            for bar in market_data.bars[:3]:  # Check first 3 bars
                print(f"  Bar: {bar.timestamp} - O:{bar.open_price} H:{bar.high_price} L:{bar.low_price} C:{bar.close_price} V:{bar.volume}")
                assert bar.open_price > 0
                assert bar.high_price >= bar.low_price
                assert bar.volume >= 0
            
            print(f"✅ Retrieved {len(market_data.bars)} market data bars successfully")
            return True
            
    except Exception as e:
        print(f"❌ TopStep market data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_qwen_llm_basic():
    """Test real Qwen LLM basic functionality."""
    print("🔍 Testing Qwen LLM basic functionality...")
    
    try:
        async with QwenClient() as client:
            # Test basic prompt
            prompt = LLMPrompt(
                system_prompt="You are a helpful trading assistant.",
                user_prompt="What are the key factors to consider when trading ES futures?",
                max_tokens=200,
                temperature=0.1
            )
            
            response = await client.generate_response(prompt)
            
            assert response.content is not None
            assert len(response.content) > 50  # Should be substantial response
            assert response.model == "qwen-turbo"
            assert response.usage_tokens > 0
            
            print(f"✅ Qwen LLM response received - {response.usage_tokens} tokens used")
            print(f"  Response preview: {response.content[:100]}...")
            return True
            
    except Exception as e:
        print(f"❌ Qwen LLM basic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_qwen_market_analysis():
    """Test real Qwen market analysis."""
    print("🔍 Testing Qwen market analysis...")
    
    try:
        async with QwenClient() as client:
            # Mock market data for analysis
            market_data = {
                "symbol": "ES",
                "current_price": 4500.0,
                "daily_change": 15.0,
                "volume": 150000,
                "high": 4515.0,
                "low": 4485.0
            }
            
            technical_indicators = {
                "sma_20": 4495.0,
                "sma_50": 4480.0,
                "rsi": 65.0,
                "macd": 2.5
            }
            
            analysis = await client.analyze_market(
                market_data=market_data,
                technical_indicators=technical_indicators,
                symbol="ES"
            )
            
            assert analysis.symbol == "ES"
            assert analysis.analysis_text is not None
            assert len(analysis.analysis_text) > 100
            assert 0 <= analysis.confidence <= 1
            
            print(f"✅ Qwen market analysis completed - Confidence: {analysis.confidence}")
            print(f"  Analysis preview: {analysis.analysis_text[:150]}...")
            return True
            
    except Exception as e:
        print(f"❌ Qwen market analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_with_real_data():
    """Test database operations with real API data."""
    print("🔍 Testing database with real API data...")
    
    try:
        # Initialize database
        await init_database()
        
        from core import get_db_session
        from core.database import MarketData, TradingSignals
        
        # Get real market data
        async with TopStepClient() as client:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=1)
            
            market_data_response = await client.get_market_data(
                contract_id="CON.F.US.EP.U25",
                start_time=start_time,
                end_time=end_time,
                timeframe="5m",
                limit=5
            )
        
        # Store in database
        async with get_db_session() as session:
            for bar in market_data_response.bars[:3]:  # Store first 3 bars
                market_data = MarketData(
                    contract_id="CON.F.US.EP.U25",
                    timestamp=bar.timestamp,
                    open_price=bar.open_price,
                    high_price=bar.high_price,
                    low_price=bar.low_price,
                    close_price=bar.close_price,
                    volume=bar.volume,
                    timeframe="5m"
                )
                session.add(market_data)
            
            # Add a trading signal
            signal = TradingSignals(
                strategy_name="test_strategy",
                contract_id="CON.F.US.EP.U25",
                signal_type="BUY",
                confidence=0.75,
                price=market_data_response.bars[0].close_price,
                metadata={"source": "real_api_test"}
            )
            session.add(signal)
            
            await session.commit()
        
        # Verify data was stored
        async with get_db_session() as session:
            from sqlalchemy import select
            
            result = await session.execute(select(MarketData))
            stored_data = result.scalars().all()
            assert len(stored_data) >= 3
            
            result = await session.execute(select(TradingSignals))
            stored_signals = result.scalars().all()
            assert len(stored_signals) >= 1
        
        print(f"✅ Database operations with real API data successful")
        return True
        
    except Exception as e:
        print(f"❌ Database with real data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_integrated_workflow():
    """Test integrated workflow with real APIs."""
    print("🔍 Testing integrated workflow with real APIs...")
    
    try:
        # Step 1: Get real market data
        async with TopStepClient() as topstep_client:
            accounts = await topstep_client.get_accounts()
            assert len(accounts) > 0
            
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=1)
            
            market_data = await topstep_client.get_market_data(
                contract_id="CON.F.US.EP.U25",
                start_time=start_time,
                end_time=end_time,
                timeframe="1m",
                limit=10
            )
            assert len(market_data.bars) > 0
        
        # Step 2: Analyze with Qwen
        async with QwenClient() as qwen_client:
            latest_bar = market_data.bars[-1]
            
            market_context = {
                "current_price": latest_bar.close_price,
                "volume": latest_bar.volume,
                "price_change": latest_bar.close_price - latest_bar.open_price
            }
            
            technical_data = {
                "high": latest_bar.high_price,
                "low": latest_bar.low_price,
                "range": latest_bar.high_price - latest_bar.low_price
            }
            
            analysis = await qwen_client.analyze_market(
                market_data=market_context,
                technical_indicators=technical_data,
                symbol="ES"
            )
            assert analysis.analysis_text is not None
        
        # Step 3: Store results in database
        await init_database()
        
        async with get_db_session() as session:
            from core.database import MarketData, TradingSignals
            
            # Store latest market data
            db_market_data = MarketData(
                contract_id="CON.F.US.EP.U25",
                timestamp=latest_bar.timestamp,
                open_price=latest_bar.open_price,
                high_price=latest_bar.high_price,
                low_price=latest_bar.low_price,
                close_price=latest_bar.close_price,
                volume=latest_bar.volume,
                timeframe="1m"
            )
            session.add(db_market_data)
            
            # Store analysis as signal
            signal = TradingSignals(
                strategy_name="llm_analysis",
                contract_id="CON.F.US.EP.U25",
                signal_type="HOLD",
                confidence=analysis.confidence,
                price=latest_bar.close_price,
                metadata={"analysis": analysis.analysis_text[:500]}
            )
            session.add(signal)
            
            await session.commit()
        
        print(f"✅ Integrated workflow completed successfully")
        print(f"  - Retrieved {len(market_data.bars)} market data bars")
        print(f"  - Generated LLM analysis with {analysis.confidence} confidence")
        print(f"  - Stored data in database")
        return True
        
    except Exception as e:
        print(f"❌ Integrated workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all real API tests."""
    print("🚀 TESTING ALL COMPONENTS WITH REAL API CREDENTIALS")
    print("=" * 80)
    
    # Configure logging
    configure_logging()
    
    tests = [
        await test_core_configuration(),
        await test_topstep_authentication(),
        await test_topstep_accounts(),
        await test_topstep_contracts(),
        await test_topstep_market_data(),
        await test_qwen_llm_basic(),
        await test_qwen_market_analysis(),
        await test_database_with_real_data(),
        await test_integrated_workflow()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("=" * 80)
    if passed == total:
        print(f"🎉 ALL {total} REAL API TESTS PASSED! 100% SUCCESS!")
        print("✅ TopStep API authentication and data retrieval working")
        print("✅ Qwen LLM integration and analysis working")
        print("✅ Database operations with real data working")
        print("✅ Integrated workflow with real APIs working")
        print("✅ ALL COMPONENTS FULLY FUNCTIONAL WITH REAL CREDENTIALS!")
        print("=" * 80)
        return True
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("❌ Some components need fixing before proceeding")
        print("=" * 80)
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
