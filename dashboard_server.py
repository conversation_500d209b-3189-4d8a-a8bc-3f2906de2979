#!/usr/bin/env python3
"""
Simplified Trading Dashboard Server.
Real-time web dashboard with direct system integration.
"""

import os
import sys
import asyncio
import json
import ssl
import aiohttp
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn


# Import existing system components
from api import TopStepClient
from llm import QwenClient
from workflow import TradingWorkflow
from agents import (
    MarketDataAgent, FVGDetectionAgent, OrderBlocksAgent,
    LiquiditySweepsAgent, RiskManagementAgent, ExecutionAgent
)
from core.settings_manager import settings_manager

# Legacy TopStepAPI class - will be replaced with real TopStepClient
class TopStepAPI:
    """Real TopStep API client."""

    def __init__(self):
        self.base_url = "https://api.topstepx.com"
        self.username = "mrrain"
        self.api_key = "FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs="
        self.session_token = None
        self.account_id = None
        self.current_account = None
        self.available_accounts = []
        self.authenticated = False
        self.session = None

        # Real contract mappings
        self.available_contracts = {
            "ES": "CON.F.US.EP.U25",  # E-mini S&P 500
            "NQ": "CON.F.US.NQ.U25",  # E-mini NASDAQ
            "YM": "CON.F.US.YM.U25",  # E-mini Dow Jones
            "RTY": "CON.F.US.RTY.U25", # E-mini Russell 2000
            "CL": "CON.F.US.CL.V25",  # Crude Oil
            "GC": "CON.F.US.GC.Z25",  # Gold
            "SI": "CON.F.US.SI.Z25",  # Silver
            "6E": "CON.F.US.6E.U25",  # Euro FX
        }
    
    async def __aenter__(self):
        if not self.session:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self.session = aiohttp.ClientSession(connector=connector)

        if not self.authenticated:
            await self.authenticate()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    async def authenticate(self) -> bool:
        try:
            url = f"{self.base_url}/api/Auth/loginKey"
            payload = {"userName": self.username, "apiKey": self.api_key}
            headers = {'accept': 'text/plain', 'Content-Type': 'application/json'}
            
            async with self.session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('success') and result.get('errorCode') == 0:
                        self.session_token = result.get('token')
                        self.authenticated = True
                        if await self._get_account_info():
                            return True
            return False
        except Exception as e:
            print(f"Authentication error: {e}")
            return False
    
    async def _get_account_info(self) -> bool:
        try:
            data = {"onlyActiveAccounts": True}
            response = await self._make_request('POST', '/api/Account/search', data)
            
            if response and response.get('success'):
                accounts = response.get('accounts', [])
                self.available_accounts = accounts

                # Don't auto-select account - let user choose
                if accounts:
                    return True
            return False
        except Exception as e:
            print(f"Account info error: {e}")
            return False

    async def select_account(self, account_id: str) -> bool:
        """Select specific account for trading."""
        try:
            for account in self.available_accounts:
                if str(account.get('id')) == str(account_id):
                    self.account_id = account.get('id')
                    self.current_account = account
                    return True
            return False
        except Exception as e:
            print(f"Account selection error: {e}")
            return False

    async def _make_request(self, method: str, endpoint: str, data: dict = None) -> dict:
        try:
            url = f"{self.base_url}{endpoint}"
            headers = {
                'accept': 'text/plain',
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.session_token}'
            }

            async with self.session.request(method, url, json=data, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                return None
        except Exception as e:
            print(f"Request error: {e}")
            return None
    
    async def get_real_account_balance(self) -> float:
        try:
            data = {"onlyActiveAccounts": True}
            response = await self._make_request('POST', '/api/Account/search', data)

            if response and response.get('success'):
                accounts = response.get('accounts', [])
                for account in accounts:
                    if account.get('id') == self.account_id:
                        return float(account.get('balance', 0.0))
            return 50000.0
        except Exception as e:
            print(f"Balance error: {e}")
            return 50000.0
    
    async def get_real_positions(self) -> list:
        try:
            data = {'accountId': self.account_id}
            response = await self._make_request('POST', '/api/Position/searchOpen', data)
            if response and response.get('success'):
                return response.get('positions', [])
            return []
        except Exception as e:
            print(f"Positions error: {e}")
            return []

    async def get_real_market_data(self, contract_id: str, timeframe="1m", limit=100):
        """Get real market data for contract."""
        try:
            unit_mapping = {"1m": (2, 1), "5m": (2, 5), "15m": (2, 15), "1h": (3, 1)}
            unit, unit_number = unit_mapping.get(timeframe, (2, 1))

            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=4)  # Get more data

            data = {
                "contractId": contract_id,
                "live": False,
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "unit": unit,
                "unitNumber": unit_number,
                "limit": limit,
                "includePartialBar": False
            }

            response = await self._make_request('POST', '/api/History/retrieveBars', data)

            if response and response.get('success'):
                bars_data = response.get('bars', [])
                bars = []
                for bar_data in bars_data:
                    bars.append({
                        'timestamp': datetime.fromisoformat(bar_data.get('t').replace('Z', '+00:00')),
                        'open_price': float(bar_data.get('o')),
                        'high_price': float(bar_data.get('h')),
                        'low_price': float(bar_data.get('l')),
                        'close_price': float(bar_data.get('c')),
                        'volume': int(bar_data.get('v', 0))
                    })
                return bars
            return []
        except Exception as e:
            print(f"Market data error: {e}")
            return []


class TradingDashboard:
    """Real-time trading dashboard integrated with existing agents."""

    def __init__(self):
        self.app = FastAPI(title="Trading Dashboard", version="1.0.0")
        self.setup_cors()
        self.setup_routes()

        # System state
        self.system_status = "STOPPED"
        self.selected_account_id = None
        self.selected_contracts = set()  # User selected contracts
        self.trading_task = None
        self.websocket_connections = []

        # REAL SYSTEM COMPONENTS - Import existing agents
        self.topstep_client = None
        self.qwen_client = None
        self.trading_workflow = None

        # REAL AGENTS - Use existing agent classes
        self.market_data_agent = None
        self.fvg_agent = None
        self.order_blocks_agent = None
        self.liquidity_sweeps_agent = None
        self.risk_agent = None
        self.execution_agent = None

        # Data storage
        self.log_entries = []
        self.recent_trades = []
        self.trade_history = []  # Detailed trade execution history
        self.order_history = []  # Order placement and status history
        self.pending_orders = {}  # Track orders that need status updates
        self.active_signals = []
        self.current_positions = []
        self.system_start_time = datetime.now(timezone.utc)
        
    def setup_cors(self):
        """Setup CORS."""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """Setup API routes."""
        
        @self.app.get("/")
        async def dashboard_home():
            """Serve dashboard HTML."""
            return HTMLResponse(self.get_dashboard_html())
        
        @self.app.get("/api/status")
        async def get_system_status():
            """Get current system status."""
            try:
                dashboard_state = await self.get_dashboard_state()
                return {"success": True, "data": dashboard_state}
            except Exception as e:
                return {"success": False, "message": str(e)}
        
        @self.app.get("/api/accounts")
        async def get_accounts():
            """Get available trading accounts."""
            try:
                accounts = await self.get_available_accounts()
                return {"success": True, "data": accounts}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.get("/api/contracts")
        async def get_contracts():
            """Get available contracts from REAL system."""
            try:
                # Use real contract mappings from our system
                contracts = ["ES", "NQ", "YM", "RTY", "CL", "GC", "SI", "6E"]
                return {"success": True, "data": contracts}
            except Exception as e:
                return {"success": False, "message": str(e)}
        
        @self.app.post("/api/select-account/{account_id}")
        async def select_account(account_id: str):
            """Select trading account."""
            try:
                if not self.topstep_client:
                    self.topstep_client = TopStepAPI()

                async with self.topstep_client:
                    success = await self.topstep_client.select_account(account_id)
                    if success:
                        self.selected_account_id = account_id
                        self.add_log_entry("INFO", f"Selected account: {account_id}", "Account")
                        await self.broadcast_update()
                        return {"success": True, "message": f"Account {account_id} selected"}
                    else:
                        return {"success": False, "message": "Failed to select account"}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.post("/api/select-contracts")
        async def select_contracts(request_data: dict):
            """Select contracts for trading."""
            try:
                contracts = request_data.get('contracts', [])

                self.selected_contracts = set(contracts)
                self.add_log_entry("INFO", f"Selected contracts: {', '.join(contracts)}", "Contracts")
                await self.broadcast_update()
                return {"success": True, "message": f"Selected {len(contracts)} contracts"}
            except Exception as e:
                return {"success": False, "message": str(e)}
        
        @self.app.post("/api/start-trading")
        async def start_trading():
            """Start the trading system."""
            try:
                # Validate requirements
                if not self.selected_account_id:
                    return {"success": False, "message": "Please select an account first"}

                if not self.selected_contracts:
                    return {"success": False, "message": "Please select contracts first"}

                success = await self.start_real_trading_system()
                if success:
                    await self.broadcast_update()
                    return {"success": True, "message": "Real trading system started"}
                else:
                    return {"success": False, "message": "Failed to start trading"}
            except Exception as e:
                return {"success": False, "message": str(e)}
        
        @self.app.post("/api/stop-trading")
        async def stop_trading():
            """Stop the trading system."""
            try:
                success = await self.stop_real_trading_system()
                if success:
                    await self.broadcast_update()
                    return {"success": True, "message": "Trading system stopped"}
                else:
                    return {"success": False, "message": "Failed to stop trading"}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.post("/api/set-confidence")
        async def set_confidence(request_data: dict):
            """Set confidence threshold."""
            try:
                threshold = float(request_data.get("threshold", 0.6))
                settings_manager.set_confidence_threshold(threshold)

                await self.broadcast_update()
                return {"success": True, "threshold": threshold, "message": f"Confidence threshold set to {threshold:.1%}"}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.get("/api/trade-history")
        async def get_trade_history():
            """Get detailed trade execution history."""
            try:
                return {
                    "success": True,
                    "trade_history": self.trade_history[-50:],  # Last 50 trades
                    "order_history": self.order_history[-50:]   # Last 50 orders
                }
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.get("/api/recent-trades")
        async def get_recent_trades():
            """Get recent trades from TopStep API."""
            try:
                if not self.topstep_client:
                    return {"success": False, "message": "TopStep client not initialized"}

                # Get trades from last 4 hours
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=4)

                data = {
                    'accountId': self.topstep_client.account_id,
                    'startDate': start_time.isoformat(),
                    'endDate': end_time.isoformat()
                }
                response = await self.topstep_client._make_request('POST', '/api/Trade/search', data)

                if response and response.get('success'):
                    trades = response.get('trades', [])
                    return {"success": True, "trades": trades}
                else:
                    return {"success": False, "message": "Failed to fetch trades from API"}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.get("/api/system-messages")
        async def get_system_messages():
            """Get recent system behavior messages."""
            try:
                messages = settings_manager.get_system_messages(limit=50)
                return {
                    "success": True,
                    "data": [
                        {
                            "timestamp": msg.timestamp,
                            "component": msg.component,
                            "message": msg.message,
                            "level": msg.level,
                            "metadata": msg.metadata
                        }
                        for msg in messages
                    ]
                }
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.post("/api/set-risk-settings")
        async def set_risk_settings(request_data: dict):
            """Set risk management settings."""
            try:
                updated_settings = {}

                if "max_daily_loss" in request_data:
                    amount = float(request_data["max_daily_loss"])
                    settings_manager.set_max_daily_loss(amount)
                    updated_settings["max_daily_loss"] = amount

                if "max_position_size" in request_data:
                    size = int(request_data["max_position_size"])
                    settings_manager.set_max_position_size(size)
                    updated_settings["max_position_size"] = size

                if "stop_loss_pct" in request_data:
                    pct = float(request_data["stop_loss_pct"])
                    settings_manager.set_stop_loss_pct(pct)
                    updated_settings["stop_loss_pct"] = pct

                await self.broadcast_update()
                return {"success": True, "updated": updated_settings, "message": "Risk settings updated"}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.get("/api/settings")
        async def get_settings():
            """Get current system settings."""
            try:
                settings = settings_manager.get_all_settings()
                return {"success": True, "data": settings}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.get("/api/signals")
        async def get_signals():
            """Get active signals."""
            try:
                return {"success": True, "data": self.active_signals}
            except Exception as e:
                return {"success": False, "message": str(e)}

        @self.app.get("/api/logs")
        async def get_logs():
            """Get system logs."""
            try:
                return {"success": True, "data": self.log_entries[-100:]}
            except Exception as e:
                return {"success": False, "message": str(e)}
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates."""
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                # Send initial state
                dashboard_state = await self.get_dashboard_state()
                await websocket.send_text(json.dumps({
                    "type": "dashboard_state",
                    "data": dashboard_state
                }, default=str))
                
                # Keep connection alive
                while True:
                    await websocket.receive_text()
                    
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
    
    async def get_available_accounts(self) -> List[Dict[str, Any]]:
        """Get available trading accounts from REAL TopStepClient."""
        try:
            # Use the REAL TopStepClient
            temp_client = TopStepClient(preferred_account_type="PRACTICE")
            async with temp_client:
                accounts = []
                for account_data in temp_client.available_accounts:
                    account = {
                        'id': str(account_data.get('id')),
                        'name': account_data.get('name', 'Unknown'),
                        'balance': float(account_data.get('balance', 0)),
                        'equity': float(account_data.get('equity', 0)),
                        'margin_used': float(account_data.get('marginUsed', 0)),
                        'margin_available': float(account_data.get('marginAvailable', 0)),
                        'account_type': account_data.get('type', 'Unknown'),
                        'status': account_data.get('status', 'Unknown')
                    }
                    accounts.append(account)
                return accounts
        except Exception as e:
            self.add_log_entry("ERROR", f"Error getting accounts from REAL client: {e}", "RealAPI")
            return []
    
    async def start_real_trading_system(self) -> bool:
        """Start the REAL trading system with existing agents."""
        try:
            if self.system_status == "RUNNING":
                return True

            self.system_status = "STARTING"
            self.add_log_entry("INFO", "Starting REAL trading system with existing agents...", "System")

            # Initialize REAL system components
            self.topstep_client = TopStepClient(preferred_account_type="PRACTICE")
            self.qwen_client = QwenClient()

            # Select the user's chosen account
            if self.selected_account_id:
                # Set account in TopStep client
                await self.topstep_client.authenticate()
                # Find and select the account
                for account in self.topstep_client.available_accounts:
                    if str(account.get('id')) == str(self.selected_account_id):
                        self.topstep_client.account_id = account.get('id')
                        self.topstep_client.current_account = account
                        break

            # Initialize REAL trading workflow with existing agents
            self.trading_workflow = TradingWorkflow(
                topstep_client=self.topstep_client,
                qwen_client=self.qwen_client
            )

            # Get references to the real agents
            self.market_data_agent = self.trading_workflow.market_data_agent
            self.fvg_agent = self.trading_workflow.fvg_agent
            self.order_blocks_agent = self.trading_workflow.order_blocks_agent
            self.liquidity_sweeps_agent = self.trading_workflow.liquidity_sweeps_agent
            self.risk_agent = self.trading_workflow.risk_agent
            self.execution_agent = self.trading_workflow.execution_agent

            # Start REAL trading loop
            self.trading_task = asyncio.create_task(self.real_trading_loop())

            self.system_status = "RUNNING"
            self.add_log_entry("INFO", "REAL trading system started with existing agents", "System")
            return True

        except Exception as e:
            self.system_status = "ERROR"
            self.add_log_entry("ERROR", f"Error starting REAL trading system: {e}", "System")
            return False
    
    async def stop_real_trading_system(self) -> bool:
        """Stop the trading system."""
        try:
            if self.system_status == "STOPPED":
                return True
            
            self.system_status = "STOPPING"
            self.add_log_entry("INFO", "Stopping trading system...", "System")
            
            # Cancel trading task
            if self.trading_task:
                self.trading_task.cancel()
                try:
                    await self.trading_task
                except asyncio.CancelledError:
                    pass

            # Clean up HTTP sessions
            if self.topstep_client and hasattr(self.topstep_client, 'session'):
                try:
                    await self.topstep_client.session.close()
                except:
                    pass
            
            self.system_status = "STOPPED"
            self.add_log_entry("INFO", "Trading system stopped successfully", "System")
            return True
            
        except Exception as e:
            self.system_status = "ERROR"
            self.add_log_entry("ERROR", f"Error stopping trading system: {e}", "System")
            return False
    
    async def real_trading_loop(self):
        """REAL trading loop using existing workflow and agents."""
        try:
            cycle_count = 0
            while self.system_status == "RUNNING":
                cycle_count += 1
                self.add_log_entry("INFO", f"Running REAL trading cycle {cycle_count} with existing workflow...", "Trading")

                # Process each selected contract using REAL workflow
                # Convert to list and filter out None/empty values to prevent iteration issues
                valid_contracts = [c for c in self.selected_contracts if c and isinstance(c, str) and c.strip()]
                for contract_symbol in valid_contracts:
                    try:
                        await self.process_contract_with_real_workflow(contract_symbol, cycle_count)
                    except Exception as e:
                        self.add_log_entry("ERROR", f"Error processing {contract_symbol or 'Unknown'}: {e}", "Trading")

                # Update real positions and account data from agents
                await self.update_real_data_from_agents()

                # Check and update pending orders
                await self.update_pending_orders()

                # Broadcast real updates
                await self.broadcast_update()

                # Wait before next cycle (60 seconds for real analysis)
                await asyncio.sleep(60)
                
        except asyncio.CancelledError:
            self.add_log_entry("INFO", "Trading loop cancelled", "Trading")
        except Exception as e:
            self.add_log_entry("ERROR", f"Trading loop error: {e}", "Trading")
            self.system_status = "ERROR"

    async def process_contract_with_real_workflow(self, contract_symbol: str, cycle_count: int):
        """Process contract using REAL workflow and existing agents."""
        try:
            # Validate contract_symbol first
            if not contract_symbol or not isinstance(contract_symbol, str) or not contract_symbol.strip():
                self.add_log_entry("ERROR", "Invalid contract symbol provided", "Workflow")
                return

            if not self.trading_workflow:
                return

            self.add_log_entry("INFO", f"Processing {contract_symbol} with REAL workflow...", "Workflow")

            # Use the REAL trading workflow to run a complete cycle for this contract
            result = await self.trading_workflow.run_trading_cycle(symbol=contract_symbol)

            if result.get('success'):
                # Extract real data from workflow results
                signals_detected = result.get('signals_detected', {})
                selected_decision = result.get('selected_decision')
                execution_results = result.get('execution_results', [])

                self.add_log_entry("INFO",
                    f"{contract_symbol} workflow complete: FVG={signals_detected.get('fvg', 0)}, "
                    f"OB={signals_detected.get('order_blocks', 0)}, LS={signals_detected.get('liquidity_sweeps', 0)}",
                    "Workflow")

                # Process real signals from agents
                if selected_decision:
                    real_signal = {
                        'id': f"REAL_{contract_symbol}_{cycle_count}_{datetime.now().strftime('%H%M%S')}",
                        'strategy': selected_decision.get('strategy_name', 'Unknown'),
                        'contract': contract_symbol,
                        'action': selected_decision.get('action', 'Unknown'),
                        'confidence': selected_decision.get('confidence', 0.0),
                        'entry_price': selected_decision.get('entry_price', 0.0),
                        'stop_loss': selected_decision.get('stop_loss', 0.0),
                        'take_profit': selected_decision.get('take_profit', 0.0),
                        'timestamp': datetime.now(timezone.utc).isoformat(),
                        'reasoning': selected_decision.get('reasoning', 'No reasoning provided')
                    }
                    self.active_signals.append(real_signal)

                    entry_price = real_signal.get('entry_price', 0) or 0
                    confidence = real_signal.get('confidence', 0) or 0
                    self.add_log_entry("INFO",
                        f"REAL SIGNAL from agents: {real_signal['strategy']} {real_signal['action']} "
                        f"{contract_symbol} @ {entry_price:.2f} (Conf: {confidence:.2f})",
                        "RealAgents")

                # Process real execution results
                for execution in execution_results:
                    # Get action from selected_decision since it's not in execution result
                    action = selected_decision.get('action') if selected_decision else 'Unknown'
                    if hasattr(action, 'value'):  # Handle TradingAction enum
                        action = action.value

                    # Get execution status, handle enum conversion
                    exec_status = execution.get('execution_status', 'Unknown')
                    if hasattr(exec_status, 'value'):  # Handle ExecutionStatus enum
                        exec_status = exec_status.value

                    # Create detailed order history entry
                    order_entry = {
                        'id': execution.get('order_id', f"order_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                        'symbol': contract_symbol,
                        'action': action,
                        'size': execution.get('filled_size', 0),  # Use actual filled size, default to 0
                        'status': exec_status,
                        'timestamp': datetime.now(timezone.utc).isoformat(),
                        'strategy': selected_decision.get('strategy_name', 'Unknown') if selected_decision else 'Unknown',
                        'success': execution.get('success', False),
                        'error_message': execution.get('error_message'),
                        'entry_price': selected_decision.get('entry_price') if selected_decision else None,
                        'stop_loss': selected_decision.get('stop_loss') if selected_decision else None,
                        'take_profit': selected_decision.get('take_profit') if selected_decision else None,
                        'confidence': selected_decision.get('confidence') if selected_decision else None
                    }
                    self.order_history.append(order_entry)

                    # Track pending orders for status updates
                    order_id = execution.get('order_id')
                    if order_id and exec_status in ['SUBMITTED', 'PENDING']:
                        self.pending_orders[order_id] = {
                            'order_entry': order_entry,
                            'selected_decision': selected_decision,
                            'contract_symbol': contract_symbol,
                            'submission_time': datetime.now(timezone.utc)
                        }

                    if execution.get('success'):
                        real_trade = {
                            'id': execution.get('order_id', f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                            'symbol': contract_symbol,
                            'side': action,  # Use the action we extracted above
                            'size': execution.get('filled_size', 0),  # Use actual filled size
                            'price': execution.get('filled_price', 0.0),
                            'timestamp': datetime.now(timezone.utc).isoformat(),
                            'status': "FILLED",
                            'strategy': selected_decision.get('strategy_name', 'Unknown') if selected_decision else 'Unknown'
                        }
                        self.recent_trades.append(real_trade)

                        # Create detailed trade history entry
                        trade_history_entry = {
                            'order_id': execution.get('order_id'),
                            'symbol': contract_symbol,
                            'strategy': selected_decision.get('strategy_name', 'Unknown') if selected_decision else 'Unknown',
                            'action': action,  # Use the action we extracted above
                            'size': execution.get('filled_size', 0),  # Use actual filled size
                            'entry_price': selected_decision.get('entry_price') if selected_decision else None,
                            'filled_price': execution.get('filled_price', 0.0),
                            'stop_loss': selected_decision.get('stop_loss') if selected_decision else None,
                            'take_profit': selected_decision.get('take_profit') if selected_decision else None,
                            'confidence': selected_decision.get('confidence') if selected_decision else None,
                            'reasoning': selected_decision.get('reasoning') if selected_decision else None,
                            'timestamp': datetime.now(timezone.utc).isoformat(),
                            'execution_time': execution.get('execution_time'),
                            'fees': execution.get('fees'),
                            'status': 'FILLED'
                        }
                        self.trade_history.append(trade_history_entry)

                        # Ensure price is never None for formatting
                        trade_price = real_trade.get('price', 0.0) or 0.0
                        self.add_log_entry("INFO",
                            f"REAL TRADE executed: {real_trade['side']} {real_trade['size']} {contract_symbol} "
                            f"@ {trade_price:.2f}",
                            "RealExecution")

                # Keep only recent data
                self.active_signals = self.active_signals[-20:]
                self.recent_trades = self.recent_trades[-50:]
                self.trade_history = self.trade_history[-100:]  # Keep last 100 detailed trades
                self.order_history = self.order_history[-100:]  # Keep last 100 orders

            else:
                error_msg = result.get('error_message', 'Unknown error')
                self.add_log_entry("WARNING", f"{contract_symbol} workflow failed: {error_msg}", "Workflow")

        except Exception as e:
            error_msg = str(e) if e else "Unknown error"
            # Ensure symbol is never None and always a valid string
            symbol = str(contract_symbol) if contract_symbol is not None else "Unknown"
            if not symbol or symbol.strip() == "":
                symbol = "Unknown"
            self.add_log_entry("ERROR", f"Workflow error for {symbol}: {error_msg}", "Workflow")

    async def update_pending_orders(self):
        """Check status of pending orders and update trade history."""
        if not self.execution_agent or not self.pending_orders:
            return

        orders_to_remove = []

        for order_id, order_info in self.pending_orders.items():
            try:
                # Check order status
                updated_result = await self.execution_agent.check_order_status(order_id)

                if updated_result:
                    exec_status = updated_result.execution_status.value if hasattr(updated_result.execution_status, 'value') else str(updated_result.execution_status)

                    # Update order history entry
                    order_entry = order_info['order_entry']
                    order_entry['status'] = exec_status
                    order_entry['size'] = updated_result.filled_size

                    # If order is filled, create/update trade history entry
                    if exec_status == 'FILLED' and updated_result.filled_size > 0:
                        selected_decision = order_info['selected_decision']
                        contract_symbol = order_info['contract_symbol']
                        action = selected_decision.get('action') if selected_decision else 'Unknown'
                        if hasattr(action, 'value'):
                            action = action.value

                        # Create detailed trade history entry
                        trade_history_entry = {
                            'order_id': order_id,
                            'symbol': contract_symbol,
                            'strategy': selected_decision.get('strategy_name', 'Unknown') if selected_decision else 'Unknown',
                            'action': action,
                            'size': updated_result.filled_size,
                            'entry_price': selected_decision.get('entry_price') if selected_decision else None,
                            'filled_price': updated_result.filled_price,
                            'stop_loss': selected_decision.get('stop_loss') if selected_decision else None,
                            'take_profit': selected_decision.get('take_profit') if selected_decision else None,
                            'confidence': selected_decision.get('confidence') if selected_decision else None,
                            'reasoning': selected_decision.get('reasoning') if selected_decision else None,
                            'timestamp': datetime.now(timezone.utc).isoformat(),
                            'execution_time': updated_result.execution_time,
                            'fees': updated_result.fees,
                            'status': 'FILLED'
                        }

                        # Update existing trade history entry or add new one
                        existing_entry = None
                        for i, entry in enumerate(self.trade_history):
                            if entry.get('order_id') == order_id:
                                existing_entry = i
                                break

                        if existing_entry is not None:
                            self.trade_history[existing_entry] = trade_history_entry
                        else:
                            self.trade_history.append(trade_history_entry)

                        self.add_log_entry("INFO",
                            f"Order FILLED: {action} {updated_result.filled_size} {contract_symbol} @ ${updated_result.filled_price:.2f}",
                            "OrderUpdate")

                        orders_to_remove.append(order_id)

                    # Remove completed or cancelled orders
                    elif exec_status in ['FILLED', 'CANCELLED', 'REJECTED']:
                        orders_to_remove.append(order_id)

            except Exception as e:
                self.logger.error(f"Error checking order status for {order_id}: {e}")

        # Remove completed orders from pending list
        for order_id in orders_to_remove:
            self.pending_orders.pop(order_id, None)

    def _contract_to_symbol(self, contract_id: str) -> str:
        """Convert contract ID to readable symbol."""
        if not contract_id or contract_id == 'Unknown':
            return 'Unknown'

        # Contract mapping
        contract_mapping = {
            'CON.F.US.EP.U25': 'ES',
            'CON.F.US.ENQ.U25': 'NQ',
            'CON.F.US.YM.U25': 'YM',
            'CON.F.US.RTY.U25': 'RTY',
            'CON.F.US.CL.U25': 'CL',
            'CON.F.US.GC.U25': 'GC'
        }

        return contract_mapping.get(contract_id, contract_id)

    async def update_real_data_from_agents(self):
        """Update real data from existing agents."""
        try:
            if not self.topstep_client or not self.selected_account_id:
                return

            # Get real account balance from TopStep client
            balance = await self.topstep_client.get_account_balance()

            # Get real positions from TopStep client
            positions_data = await self.topstep_client.get_current_positions()

            # Clear and update current positions (important: always clear first to remove stale data)
            self.current_positions = []

            # Only add positions if we actually have real open positions
            if positions_data and len(positions_data) > 0:
                for pos_data in positions_data:
                    # Convert contract_id to symbol
                    contract_id = pos_data.get('contract_id', pos_data.get('contractId', 'Unknown'))
                    symbol = self._contract_to_symbol(contract_id)

                    # Only add positions with valid data (non-zero current price)
                    current_price = float(pos_data.get('current_price', pos_data.get('currentPrice', 0)))
                    if current_price > 0:  # Filter out closed/invalid positions
                        position = {
                            'id': str(pos_data.get('id', 'unknown')),
                            'symbol': symbol,
                            'contract_id': contract_id,
                            'side': "LONG" if pos_data.get('size', 0) > 0 else "SHORT",
                            'size': abs(pos_data.get('size', 0)),
                            'entry_price': float(pos_data.get('average_price', pos_data.get('averagePrice', 0))),
                            'current_price': current_price,
                            'unrealized_pnl': float(pos_data.get('unrealized_pnl', pos_data.get('unrealizedPnl', 0))),
                            'realized_pnl': float(pos_data.get('realized_pnl', pos_data.get('realizedPnl', 0))),
                            'position_type': pos_data.get('position_type', pos_data.get('type', 'Unknown')),
                            'creation_timestamp': pos_data.get('creation_timestamp', pos_data.get('creationTimestamp', datetime.now(timezone.utc).isoformat())),
                            'account_id': pos_data.get('account_id', pos_data.get('accountId', 'Unknown')),
                            'timestamp': datetime.now(timezone.utc).isoformat()
                        }
                        self.current_positions.append(position)

            # Get additional data from agents if available
            if self.risk_agent and balance > 0:
                try:
                    risk_metrics = self.risk_agent.get_risk_metrics(balance, self.current_positions)
                    self.add_log_entry("INFO",
                        f"Risk Metrics: Daily PnL ${risk_metrics.daily_pnl:.2f}, Max DD ${risk_metrics.max_drawdown:.2f}",
                        "RiskAgent")
                except Exception as e:
                    self.add_log_entry("WARNING", f"Risk metrics calculation error: {e}", "RiskAgent")

            self.add_log_entry("INFO",
                f"Real Data Update: Balance ${balance:,.2f}, Positions: {len(self.current_positions)}",
                "RealAgents")

        except Exception as e:
            self.add_log_entry("ERROR", f"Real data update error: {e}", "RealAgents")

    # NOTE: Strategy detection methods removed - using real agents instead
    # FVG detection is handled by self.fvg_agent (FVGDetectionAgent)
    # Order Blocks detection is handled by self.order_blocks_agent (OrderBlocksAgent)
    # Liquidity Sweeps detection is handled by self.liquidity_sweeps_agent (LiquiditySweepsAgent)





    def add_log_entry(self, level: str, message: str, component: str):
        """Add a log entry."""
        log_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'level': level,
            'message': message,
            'component': component
        }
        self.log_entries.append(log_entry)
        self.log_entries = self.log_entries[-1000:]  # Keep last 1000
        print(f"[{level}] [{component}] {message}")
    
    async def get_dashboard_state(self) -> Dict[str, Any]:
        """Get complete dashboard state."""
        try:
            # Get accounts
            available_accounts = await self.get_available_accounts()
            
            # Get selected account
            selected_account = None
            if self.selected_account_id:
                for acc in available_accounts:
                    if acc['id'] == self.selected_account_id:
                        selected_account = acc
                        break
            
            # Get real positions (use cached current_positions)
            positions = self.current_positions
            
            # Calculate metrics
            total_trades = len(self.recent_trades)
            trading_metrics = {
                'total_trades': total_trades,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'daily_pnl': 0.0,
                'max_drawdown': 0.0
            }
            
            return {
                'system_status': self.system_status,
                'system_health': {
                    'api_status': "CONNECTED" if self.topstep_client else "DISCONNECTED",
                    'database_status': "CONNECTED",
                    'llm_status': "CONNECTED" if self.qwen_client else "DISCONNECTED",
                    'market_data_status': "CONNECTED" if self.market_data_agent else "DISCONNECTED",
                    'workflow_status': "CONNECTED" if self.trading_workflow else "DISCONNECTED",
                    'agents_status': {
                        'fvg_agent': "CONNECTED" if self.fvg_agent else "DISCONNECTED",
                        'order_blocks_agent': "CONNECTED" if self.order_blocks_agent else "DISCONNECTED",
                        'liquidity_sweeps_agent': "CONNECTED" if self.liquidity_sweeps_agent else "DISCONNECTED",
                        'risk_agent': "CONNECTED" if self.risk_agent else "DISCONNECTED",
                        'execution_agent': "CONNECTED" if self.execution_agent else "DISCONNECTED"
                    },
                    'last_heartbeat': datetime.now(timezone.utc).isoformat(),
                    'uptime_seconds': int((datetime.now(timezone.utc) - self.system_start_time).total_seconds())
                },
                'selected_account': selected_account,
                'available_accounts': available_accounts,
                'selected_contracts': list(self.selected_contracts),
                'available_contracts': ["ES", "NQ", "YM", "RTY", "CL", "GC", "SI", "6E"],
                'positions': positions,
                'recent_trades': self.recent_trades,
                'trade_history': self.trade_history[-20:],  # Last 20 detailed trades
                'order_history': self.order_history[-20:],  # Last 20 orders
                'active_signals': self.active_signals,
                'trading_metrics': trading_metrics,
                'strategy_stats': [],
                'recent_logs': self.log_entries[-50:],
                'settings': settings_manager.get_all_settings(),
                'last_updated': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            self.add_log_entry("ERROR", f"Error getting dashboard state: {e}", "Dashboard")
            return {
                'system_status': "ERROR",
                'system_health': {
                    'api_status': "ERROR",
                    'database_status': "ERROR",
                    'llm_status': "ERROR",
                    'market_data_status': "ERROR",
                    'last_heartbeat': datetime.now(timezone.utc).isoformat(),
                    'uptime_seconds': 0
                },
                'selected_account': None,
                'available_accounts': [],
                'positions': [],
                'recent_trades': [],
                'active_signals': [],
                'trading_metrics': {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'win_rate': 0.0,
                    'total_pnl': 0.0,
                    'daily_pnl': 0.0,
                    'max_drawdown': 0.0
                },
                'strategy_stats': [],
                'recent_logs': [],
                'last_updated': datetime.now(timezone.utc).isoformat()
            }
    
    async def broadcast_update(self):
        """Broadcast dashboard update to all connected WebSocket clients."""
        if not self.websocket_connections:
            return
        
        try:
            dashboard_state = await self.get_dashboard_state()
            message = json.dumps({
                "type": "dashboard_update",
                "data": dashboard_state
            }, default=str)
            
            # Send to all connected clients
            disconnected = []
            for websocket in self.websocket_connections:
                try:
                    await websocket.send_text(message)
                except:
                    disconnected.append(websocket)
            
            # Remove disconnected clients
            for websocket in disconnected:
                self.websocket_connections.remove(websocket)
                
        except Exception as e:
            self.add_log_entry("ERROR", f"Error broadcasting update: {e}", "WebSocket")
    
    def get_dashboard_html(self) -> str:
        """Get dashboard HTML content."""
        return """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Trading Dashboard</title>
            <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
            <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
            <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
            <style>
                .status-running { color: #10B981; }
                .status-stopped { color: #EF4444; }
                .status-starting { color: #F59E0B; }
                .status-error { color: #DC2626; }
                .log-info { color: #3B82F6; }
                .log-warning { color: #F59E0B; }
                .log-error { color: #EF4444; }
                .log-debug { color: #6B7280; }
            </style>
        </head>
        <body>
            <div id="dashboard-root"></div>
            <script type="text/babel">
                // Include the React dashboard code here
                const { useState, useEffect, useCallback } = React;

                const TradingDashboard = () => {
                    const [dashboardState, setDashboardState] = useState(null);
                    const [loading, setLoading] = useState(true);
                    const [error, setError] = useState(null);
                    const [websocket, setWebsocket] = useState(null);
                    const [confidenceThreshold, setConfidenceThreshold] = useState(0.6);
                    const [systemMessages, setSystemMessages] = useState([]);
                    const [maxDailyLoss, setMaxDailyLoss] = useState(1000);
                    const [maxPositionSize, setMaxPositionSize] = useState(10);
                    const [stopLossPct, setStopLossPct] = useState(2.0);

                    // API calls
                    const apiCall = useCallback(async (endpoint, options = {}) => {
                        try {
                            const response = await fetch(`/api${endpoint}`, {
                                headers: {
                                    'Content-Type': 'application/json',
                                    ...options.headers
                                },
                                ...options
                            });
                            const data = await response.json();
                            if (!data.success) {
                                throw new Error(data.message);
                            }
                            return data;
                        } catch (err) {
                            setError(err.message);
                            throw err;
                        }
                    }, []);

                    // Load initial data
                    useEffect(() => {
                        const loadInitialData = async () => {
                            try {
                                setLoading(true);
                                const response = await apiCall('/status');
                                setDashboardState(response.data);
                                setError(null);
                            } catch (err) {
                                console.error('Failed to load initial data:', err);
                            } finally {
                                setLoading(false);
                            }
                        };

                        loadInitialData();
                    }, [apiCall]);

                    // Setup WebSocket connection
                    useEffect(() => {
                        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                        const wsUrl = `${protocol}//${window.location.host}/ws`;
                        
                        const ws = new WebSocket(wsUrl);
                        
                        ws.onopen = () => {
                            console.log('WebSocket connected');
                            setWebsocket(ws);
                        };
                        
                        ws.onmessage = (event) => {
                            try {
                                const message = JSON.parse(event.data);
                                if (message.type === 'dashboard_update' || message.type === 'dashboard_state') {
                                    setDashboardState(message.data);
                                }
                            } catch (err) {
                                console.error('WebSocket message error:', err);
                            }
                        };
                        
                        ws.onclose = () => {
                            console.log('WebSocket disconnected');
                            setWebsocket(null);
                        };

                        return () => {
                            if (ws.readyState === WebSocket.OPEN) {
                                ws.close();
                            }
                        };
                    }, []);

                    // Event handlers
                    const handleSelectAccount = async (accountId) => {
                        try {
                            await apiCall(`/select-account/${accountId}`, { method: 'POST' });
                        } catch (err) {
                            console.error('Failed to select account:', err);
                        }
                    };

                    const handleStartTrading = async () => {
                        try {
                            await apiCall('/start-trading', { method: 'POST' });
                        } catch (err) {
                            console.error('Failed to start trading:', err);
                        }
                    };

                    const handleStopTrading = async () => {
                        try {
                            await apiCall('/stop-trading', { method: 'POST' });
                        } catch (err) {
                            console.error('Failed to stop trading:', err);
                        }
                    };

                    const handleContractToggle = async (contract, checked) => {
                        try {
                            const currentContracts = dashboardState?.selected_contracts || [];
                            let newContracts;

                            if (checked) {
                                newContracts = [...currentContracts, contract];
                            } else {
                                newContracts = currentContracts.filter(c => c !== contract);
                            }

                            await apiCall('/select-contracts', {
                                method: 'POST',
                                body: JSON.stringify({ contracts: newContracts })
                            });
                        } catch (err) {
                            console.error('Failed to toggle contract:', err);
                        }
                    };

                    const handleUpdateConfidence = async () => {
                        try {
                            await apiCall('/set-confidence', {
                                method: 'POST',
                                body: JSON.stringify({ threshold: confidenceThreshold })
                            });
                        } catch (err) {
                            console.error('Failed to update confidence:', err);
                        }
                    };

                    const handleUpdateRiskSettings = async () => {
                        try {
                            await apiCall('/set-risk-settings', {
                                method: 'POST',
                                body: JSON.stringify({
                                    max_daily_loss: maxDailyLoss,
                                    max_position_size: maxPositionSize,
                                    stop_loss_pct: stopLossPct
                                })
                            });
                        } catch (err) {
                            console.error('Failed to update risk settings:', err);
                        }
                    };

                    // Load system messages
                    useEffect(() => {
                        const loadSystemMessages = async () => {
                            try {
                                const response = await apiCall('/system-messages');
                                setSystemMessages(response.data);
                            } catch (err) {
                                console.error('Failed to load system messages:', err);
                            }
                        };

                        loadSystemMessages();
                        const interval = setInterval(loadSystemMessages, 2000); // Update every 2 seconds
                        return () => clearInterval(interval);
                    }, [apiCall]);

                    // Load settings
                    useEffect(() => {
                        const loadSettings = async () => {
                            try {
                                const response = await apiCall('/settings');
                                setConfidenceThreshold(response.data.confidence_threshold);
                                setMaxDailyLoss(response.data.max_daily_loss);
                                setMaxPositionSize(response.data.max_position_size);
                                setStopLossPct(response.data.default_stop_loss_pct);
                            } catch (err) {
                                console.error('Failed to load settings:', err);
                            }
                        };

                        loadSettings();
                    }, [apiCall]);

                    if (loading) {
                        return (
                            <div className="min-h-screen bg-gray-100 flex items-center justify-center">
                                <div className="text-xl">Loading dashboard...</div>
                            </div>
                        );
                    }

                    return (
                        <div className="min-h-screen bg-gray-100">
                            {/* Header */}
                            <header className="bg-white shadow-sm border-b">
                                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                                    <div className="flex justify-between items-center py-4">
                                        <h1 className="text-2xl font-bold text-gray-900">🎛️ Trading Dashboard</h1>
                                        <div className="flex items-center space-x-4">
                                            <div className="flex items-center space-x-2">
                                                <div className={`w-3 h-3 rounded-full ${
                                                    dashboardState?.system_status === 'RUNNING' ? 'bg-green-500' :
                                                    dashboardState?.system_status === 'STOPPED' ? 'bg-red-500' :
                                                    dashboardState?.system_status === 'STARTING' ? 'bg-yellow-500' :
                                                    'bg-gray-500'
                                                }`}></div>
                                                <span className="text-sm font-medium">
                                                    System: {dashboardState?.system_status || 'UNKNOWN'}
                                                </span>
                                            </div>
                                            <div className={`w-3 h-3 rounded-full ${websocket ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                        </div>
                                    </div>
                                </div>
                            </header>

                            {/* Main Content */}
                            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                                {error && (
                                    <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                                        {error}
                                    </div>
                                )}

                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    {/* Left Column - Controls */}
                                    <div className="space-y-6">
                                        {/* Account Selector */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Trading Account</h3>
                                            <select
                                                className="w-full p-2 border rounded"
                                                value={dashboardState?.selected_account?.id || ''}
                                                onChange={(e) => handleSelectAccount(e.target.value)}
                                            >
                                                <option value="">Select Account</option>
                                                {(dashboardState?.available_accounts || []).map(account => (
                                                    <option key={account.id} value={account.id}>
                                                        {account.name} - ${account.balance.toLocaleString()}
                                                    </option>
                                                ))}
                                            </select>
                                            {dashboardState?.selected_account && (
                                                <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                                                    <div>Balance: ${dashboardState.selected_account.balance.toLocaleString()}</div>
                                                    <div>Type: {dashboardState.selected_account.account_type}</div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Contract Selector */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Trading Contracts</h3>
                                            <div className="space-y-2">
                                                {(dashboardState?.available_contracts || []).map(contract => (
                                                    <label key={contract} className="flex items-center">
                                                        <input
                                                            type="checkbox"
                                                            className="mr-2"
                                                            checked={(dashboardState?.selected_contracts || []).includes(contract)}
                                                            onChange={(e) => handleContractToggle(contract, e.target.checked)}
                                                        />
                                                        <span className="text-sm font-medium">{contract}</span>
                                                    </label>
                                                ))}
                                            </div>
                                            <div className="mt-3 text-sm text-gray-600">
                                                Selected: {(dashboardState?.selected_contracts || []).length} contracts
                                            </div>
                                        </div>

                                        {/* Confidence Settings */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Confidence Settings</h3>
                                            <div className="space-y-3">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Minimum Confidence Threshold
                                                    </label>
                                                    <div className="flex items-center space-x-2">
                                                        <input
                                                            type="range"
                                                            min="0.1"
                                                            max="0.95"
                                                            step="0.05"
                                                            value={confidenceThreshold}
                                                            onChange={(e) => setConfidenceThreshold(parseFloat(e.target.value))}
                                                            className="flex-1"
                                                        />
                                                        <span className="text-sm font-medium w-12">
                                                            {(confidenceThreshold * 100).toFixed(0)}%
                                                        </span>
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={handleUpdateConfidence}
                                                    className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 font-medium"
                                                >
                                                    Update Confidence
                                                </button>
                                                <div className="text-xs text-gray-500">
                                                    Current: {(confidenceThreshold * 100).toFixed(0)}% - Signals below this threshold will be rejected
                                                </div>
                                            </div>
                                        </div>

                                        {/* Risk Management Settings */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Risk Management</h3>
                                            <div className="space-y-4">
                                                {/* Max Daily Loss */}
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Max Daily Loss ($)
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="100"
                                                        max="10000"
                                                        step="100"
                                                        value={maxDailyLoss}
                                                        onChange={(e) => setMaxDailyLoss(parseFloat(e.target.value))}
                                                        className="w-full p-2 border rounded"
                                                    />
                                                </div>

                                                {/* Max Position Size */}
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Max Position Size (contracts)
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="1"
                                                        max="50"
                                                        step="1"
                                                        value={maxPositionSize}
                                                        onChange={(e) => setMaxPositionSize(parseInt(e.target.value))}
                                                        className="w-full p-2 border rounded"
                                                    />
                                                </div>

                                                {/* Stop Loss Percentage */}
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Stop Loss (% per trade)
                                                    </label>
                                                    <input
                                                        type="number"
                                                        min="0.1"
                                                        max="10.0"
                                                        step="0.1"
                                                        value={stopLossPct}
                                                        onChange={(e) => setStopLossPct(parseFloat(e.target.value))}
                                                        className="w-full p-2 border rounded"
                                                    />
                                                </div>

                                                <button
                                                    onClick={handleUpdateRiskSettings}
                                                    className="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 font-medium"
                                                >
                                                    Update Risk Settings
                                                </button>

                                                <div className="text-xs text-gray-500 space-y-1">
                                                    <div>Daily Loss: ${maxDailyLoss} max per day</div>
                                                    <div>Position Size: {maxPositionSize} contracts max</div>
                                                    <div>Stop Loss: {stopLossPct}% risk per trade</div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* System Controls */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">System Control</h3>
                                            <div className="flex space-x-2">
                                                <button
                                                    onClick={handleStartTrading}
                                                    disabled={dashboardState?.system_status === 'RUNNING'}
                                                    className={`px-4 py-2 rounded font-medium ${
                                                        dashboardState?.system_status === 'RUNNING'
                                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                                            : 'bg-green-500 text-white hover:bg-green-600'
                                                    }`}
                                                >
                                                    Start Trading
                                                </button>
                                                <button
                                                    onClick={handleStopTrading}
                                                    disabled={dashboardState?.system_status !== 'RUNNING'}
                                                    className={`px-4 py-2 rounded font-medium ${
                                                        dashboardState?.system_status !== 'RUNNING'
                                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                                            : 'bg-red-500 text-white hover:bg-red-600'
                                                    }`}
                                                >
                                                    Stop Trading
                                                </button>
                                            </div>
                                        </div>

                                        {/* Trading Metrics */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Trading Metrics</h3>
                                            <div className="grid grid-cols-2 gap-4">
                                                <div className="text-center">
                                                    <div className="text-2xl font-bold">{dashboardState?.trading_metrics?.total_trades || 0}</div>
                                                    <div className="text-sm text-gray-600">Total Trades</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="text-2xl font-bold text-green-600">{dashboardState?.trading_metrics?.winning_trades || 0}</div>
                                                    <div className="text-sm text-gray-600">Winners</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Middle Column - Positions & Signals */}
                                    <div className="space-y-6">
                                        {/* Current Positions */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Current Positions</h3>
                                            {(dashboardState?.positions || []).length === 0 ? (
                                                <p className="text-gray-500">No open positions</p>
                                            ) : (
                                                <div className="space-y-3">
                                                    {dashboardState.positions.map(position => {
                                                        const pnlPercentage = position.entry_price ?
                                                            ((position.current_price - position.entry_price) / position.entry_price * 100 * (position.side === 'LONG' ? 1 : -1)) : 0;
                                                        return (
                                                            <div key={position.id} className="border p-4 rounded-lg bg-gray-50">
                                                                <div className="flex justify-between items-center mb-3">
                                                                    <div className="flex items-center space-x-3">
                                                                        <span className="text-lg font-bold">{position.symbol}</span>
                                                                        <span className={`px-3 py-1 rounded font-semibold ${position.side === 'LONG' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                                            {position.side}
                                                                        </span>
                                                                        <span className="text-sm text-gray-600">Size: {position.size}</span>
                                                                    </div>
                                                                    <div className={`text-lg font-bold ${(position.unrealized_pnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                                        ${(position.unrealized_pnl || 0).toFixed(2)}
                                                                    </div>
                                                                </div>

                                                                <div className="grid grid-cols-3 gap-4 text-sm">
                                                                    <div>
                                                                        <div className="text-gray-600">Entry Price:</div>
                                                                        <div className="font-semibold">${(position.entry_price || 0).toFixed(2)}</div>
                                                                    </div>
                                                                    <div>
                                                                        <div className="text-gray-600">Current Price:</div>
                                                                        <div className="font-semibold">${(position.current_price || 0).toFixed(2)}</div>
                                                                    </div>
                                                                    <div>
                                                                        <div className="text-gray-600">P&L %:</div>
                                                                        <div className={`font-semibold ${pnlPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                                            {pnlPercentage.toFixed(2)}%
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div className="mt-3 pt-3 border-t border-gray-200">
                                                                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
                                                                        <div>
                                                                            <div>Position ID:</div>
                                                                            <div className="font-mono">{position.id}</div>
                                                                        </div>
                                                                        <div>
                                                                            <div>Account:</div>
                                                                            <div>{position.account_id || 'N/A'}</div>
                                                                        </div>
                                                                        <div>
                                                                            <div>Entry Time:</div>
                                                                            <div>{position.creation_timestamp ? new Date(position.creation_timestamp).toLocaleString() : 'N/A'}</div>
                                                                        </div>
                                                                        <div>
                                                                            <div>Contract:</div>
                                                                            <div className="font-mono">{position.contract_id ? position.contract_id.substring(0, 15) + '...' : 'N/A'}</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            )}
                                        </div>

                                        {/* Active Signals */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Active Signals</h3>
                                            {(dashboardState?.active_signals || []).length === 0 ? (
                                                <p className="text-gray-500">No active signals</p>
                                            ) : (
                                                <div className="space-y-3">
                                                    {dashboardState.active_signals.slice(-5).reverse().map(signal => (
                                                        <div key={signal.id} className="border p-3 rounded-lg bg-gray-50">
                                                            <div className="flex justify-between items-center mb-2">
                                                                <div className="flex items-center space-x-2">
                                                                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-semibold">
                                                                        {signal.strategy}
                                                                    </span>
                                                                    <span className="text-sm text-gray-600">{signal.contract || signal.symbol}</span>
                                                                </div>
                                                                <span className={`px-3 py-1 rounded font-semibold ${signal.action === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                                    {signal.action}
                                                                </span>
                                                            </div>

                                                            <div className="grid grid-cols-2 gap-4 text-sm">
                                                                <div>
                                                                    <div className="text-gray-600">Entry Price:</div>
                                                                    <div className="font-semibold">${(signal.entry_price || 0).toFixed(2)}</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-gray-600">Confidence:</div>
                                                                    <div className={`font-semibold ${signal.confidence >= 0.8 ? 'text-green-600' : signal.confidence >= 0.6 ? 'text-yellow-600' : 'text-red-600'}`}>
                                                                        {(signal.confidence * 100).toFixed(1)}%
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-gray-600">Stop Loss:</div>
                                                                    <div className="font-semibold text-red-600">${(signal.stop_loss || 0).toFixed(2)}</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-gray-600">Take Profit:</div>
                                                                    <div className="font-semibold text-green-600">${(signal.take_profit || 0).toFixed(2)}</div>
                                                                </div>
                                                            </div>

                                                            <div className="mt-2 pt-2 border-t border-gray-200">
                                                                <div className="text-xs text-gray-500">
                                                                    Risk/Reward: 1:{signal.entry_price && signal.stop_loss && signal.take_profit ?
                                                                        (Math.abs(signal.take_profit - signal.entry_price) / Math.abs(signal.entry_price - signal.stop_loss)).toFixed(2) : 'N/A'}
                                                                </div>
                                                                <div className="text-xs text-gray-500 mt-1">
                                                                    Time: {new Date(signal.timestamp).toLocaleString()}
                                                                </div>
                                                                {signal.reasoning && (
                                                                    <div className="text-xs text-gray-600 mt-1 italic">
                                                                        "{signal.reasoning.substring(0, 100)}..."
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>

                                        {/* Trade History */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">Trade History</h3>
                                            {(dashboardState?.trade_history || []).length === 0 ? (
                                                <p className="text-gray-500">No trade history available</p>
                                            ) : (
                                                <div className="space-y-2 max-h-96 overflow-y-auto">
                                                    {dashboardState.trade_history.slice(-10).reverse().map((trade, index) => (
                                                        <div key={index} className="border p-3 rounded-lg bg-gray-50">
                                                            <div className="flex justify-between items-center mb-2">
                                                                <div className="flex items-center space-x-2">
                                                                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-semibold">
                                                                        {trade.strategy}
                                                                    </span>
                                                                    <span className="text-sm font-medium">{trade.symbol}</span>
                                                                    <span className={`px-2 py-1 rounded text-sm font-semibold ${trade.action === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                                        {trade.action}
                                                                    </span>
                                                                    <span className="text-sm text-gray-600">Size: {trade.size}</span>
                                                                </div>
                                                                <div className="text-sm text-gray-500">
                                                                    {new Date(trade.timestamp).toLocaleTimeString()}
                                                                </div>
                                                            </div>

                                                            <div className="grid grid-cols-3 gap-3 text-sm">
                                                                <div>
                                                                    <div className="text-gray-600">Entry:</div>
                                                                    <div className="font-semibold">${(trade.entry_price || 0).toFixed(2)}</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-gray-600">Filled:</div>
                                                                    <div className="font-semibold">${(trade.filled_price || 0).toFixed(2)}</div>
                                                                </div>
                                                                <div>
                                                                    <div className="text-gray-600">Confidence:</div>
                                                                    <div className={`font-semibold ${(trade.confidence || 0) >= 0.8 ? 'text-green-600' : (trade.confidence || 0) >= 0.6 ? 'text-yellow-600' : 'text-red-600'}`}>
                                                                        {((trade.confidence || 0) * 100).toFixed(1)}%
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div className="mt-2 pt-2 border-t border-gray-200">
                                                                <div className="grid grid-cols-2 gap-3 text-xs text-gray-500">
                                                                    <div>
                                                                        <div>Order ID:</div>
                                                                        <div className="font-mono">{trade.order_id || 'N/A'}</div>
                                                                    </div>
                                                                    <div>
                                                                        <div>Status:</div>
                                                                        <div className={`font-semibold ${trade.status === 'FILLED' ? 'text-green-600' : 'text-yellow-600'}`}>
                                                                            {trade.status}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {trade.reasoning && (
                                                                    <div className="mt-2 text-xs text-gray-600 italic">
                                                                        "{trade.reasoning.substring(0, 80)}..."
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Right Column - Logs */}
                                    <div className="space-y-6">
                                        {/* System Logs */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">System Logs</h3>
                                            <div className="h-96 overflow-y-auto bg-gray-50 p-3 rounded text-sm font-mono">
                                                {(dashboardState?.recent_logs || []).length === 0 ? (
                                                    <p className="text-gray-500">No logs available</p>
                                                ) : (
                                                    dashboardState.recent_logs.slice(-20).reverse().map((log, index) => (
                                                        <div key={index} className="mb-1">
                                                            <span className="text-gray-400">
                                                                {new Date(log.timestamp).toLocaleTimeString()}
                                                            </span>
                                                            <span className={`ml-2 ${
                                                                log.level === 'INFO' ? 'text-blue-600' :
                                                                log.level === 'WARNING' ? 'text-yellow-600' :
                                                                log.level === 'ERROR' ? 'text-red-600' :
                                                                'text-gray-600'
                                                            }`}>
                                                                [{log.level}]
                                                            </span>
                                                            <span className="ml-2 text-gray-600">
                                                                [{log.component}]
                                                            </span>
                                                            <span className="ml-2">
                                                                {log.message}
                                                            </span>
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                        </div>

                                        {/* System Behavior Messages */}
                                        <div className="bg-white p-4 rounded-lg shadow">
                                            <h3 className="text-lg font-semibold mb-3">System Behavior</h3>
                                            <div className="h-64 overflow-y-auto bg-gray-50 p-3 rounded text-sm">
                                                {(systemMessages || []).length === 0 ? (
                                                    <p className="text-gray-500">No system messages</p>
                                                ) : (
                                                    systemMessages.slice(-15).reverse().map((msg, index) => (
                                                        <div key={index} className="mb-2 border-b border-gray-200 pb-1">
                                                            <div className="flex justify-between items-start">
                                                                <span className="text-xs text-gray-400">
                                                                    {msg.timestamp}
                                                                </span>
                                                                <span className={`text-xs px-2 py-1 rounded ${
                                                                    msg.level === 'INFO' ? 'bg-blue-100 text-blue-800' :
                                                                    msg.level === 'WARNING' ? 'bg-yellow-100 text-yellow-800' :
                                                                    msg.level === 'ERROR' ? 'bg-red-100 text-red-800' :
                                                                    'bg-gray-100 text-gray-800'
                                                                }`}>
                                                                    {msg.component}
                                                                </span>
                                                            </div>
                                                            <div className="mt-1 text-gray-700">
                                                                {msg.message}
                                                            </div>
                                                        </div>
                                                    ))
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </main>
                        </div>
                    );
                };

                // Render the dashboard
                ReactDOM.render(<TradingDashboard />, document.getElementById('dashboard-root'));
            </script>
        </body>
        </html>
        """


async def main():
    """Run the dashboard server."""
    
    print("🎛️ TRADING DASHBOARD SERVER")
    print("=" * 50)
    print("Starting real-time trading dashboard...")
    print("Dashboard will be available at: http://localhost:8000")
    print("=" * 50)
    
    # Create dashboard
    dashboard = TradingDashboard()
    
    # Add startup log
    dashboard.add_log_entry("INFO", "Dashboard server started", "Server")
    
    # Run the server
    config = uvicorn.Config(
        dashboard.app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False
    )
    
    server = uvicorn.Server(config)
    
    print("✅ Dashboard server started successfully")
    print("🌐 Open http://localhost:8000 in your browser")
    
    await server.serve()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Dashboard server stopped by user")
    except Exception as e:
        print(f"❌ Dashboard server error: {e}")
        sys.exit(1)
