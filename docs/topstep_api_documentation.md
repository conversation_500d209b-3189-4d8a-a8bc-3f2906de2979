# TopStep API Documentation

## Overview

TopStep uses the ProjectX Gateway API, a comprehensive REST API for prop trading operations. The API provides complete access to account management, market data, order execution, position tracking, and real-time updates via WebSocket connections.

**Base URLs:**
- Demo: `https://api-demo.topstepx.com`
- Live: `https://api.topstepx.com`
- Real-time Demo: `wss://ws-demo.topstepx.com`
- Real-time Live: `wss://ws.topstepx.com`

## Authentication

### API Key Authentication
**Endpoint:** `POST /api/Auth/loginKey`

**Request:**
```json
{
  "userName": "string",
  "apiKey": "string"
}
```

**Response:**
```json
{
  "token": "jwt_session_token",
  "success": true,
  "errorCode": 0,
  "errorMessage": null
}
```

**Notes:**
- Session tokens are valid for 24 hours
- Include token in Authorization header: `Bearer {token}`
- Use `POST /api/Auth/validate` to check token validity

## Account Management

### Get Account Information
**Endpoint:** `GET /api/Account/search`

**Response:**
```json
{
  "accounts": [
    {
      "id": 123,
      "name": "Main Trading Account",
      "balance": 10000.50,
      "canTrade": true,
      "isVisible": true,
      "simulated": false
    }
  ],
  "success": true,
  "errorCode": 0,
  "errorMessage": null
}
```

## Market Data

### Retrieve Historical Bars
**Endpoint:** `POST /api/History/retrieveBars`

**Request:**
```json
{
  "contractId": "CON.F.US.RTY.Z24",
  "live": false,
  "startTime": "2024-12-01T00:00:00Z",
  "endTime": "2024-12-31T21:00:00Z",
  "unit": 2,
  "unitNumber": 1,
  "limit": 1000,
  "includePartialBar": false
}
```

**Unit Types:**
- 1 = Second
- 2 = Minute  
- 3 = Hour
- 4 = Day
- 5 = Week
- 6 = Month

**Response:**
```json
{
  "bars": [
    {
      "t": "2024-12-20T14:00:00+00:00",
      "o": 2208.*********,
      "h": 2217.*********,
      "l": 2206.*********,
      "c": 2210.*********,
      "v": 87
    }
  ],
  "success": true,
  "errorCode": 0,
  "errorMessage": null
}
```

### Search Contracts
**Endpoint:** `GET /api/Contract/search`

**Parameters:**
- `query`: Search term for contract
- `limit`: Maximum results to return

### Get Available Contracts
**Endpoint:** `GET /api/Contract/available`

**Response:** List of all tradeable contracts with details

### Get Contract by ID
**Endpoint:** `GET /api/Contract/{contractId}`

## Order Management

### Place Order
**Endpoint:** `POST /api/Order/place`

**Request:**
```json
{
  "accountId": 465,
  "contractId": "CON.F.US.DA6.M25",
  "type": 2,
  "side": 1,
  "size": 1,
  "limitPrice": null,
  "stopPrice": null,
  "trailPrice": null,
  "customTag": "strategy-1",
  "linkedOrderId": null
}
```

**Order Types:**
- 1 = Limit
- 2 = Market
- 4 = Stop
- 5 = TrailingStop
- 6 = JoinBid
- 7 = JoinAsk

**Order Sides:**
- 0 = Bid (Buy)
- 1 = Ask (Sell)

**Response:**
```json
{
  "orderId": 9056,
  "success": true,
  "errorCode": 0,
  "errorMessage": null
}
```

### Cancel Order
**Endpoint:** `DELETE /api/Order/{orderId}`

### Modify Order
**Endpoint:** `PUT /api/Order/modify`

**Request:**
```json
{
  "orderId": 9056,
  "size": 2,
  "limitPrice": 2100.50,
  "stopPrice": null
}
```

### Search Orders
**Endpoint:** `GET /api/Order/search`

**Parameters:**
- `accountId`: Account ID
- `startDate`: Start date filter
- `endDate`: End date filter
- `status`: Order status filter

### Get Open Orders
**Endpoint:** `GET /api/Order/open`

**Parameters:**
- `accountId`: Account ID

## Position Management

### Get Positions
**Endpoint:** `GET /api/Position/search`

**Parameters:**
- `accountId`: Account ID

**Response:**
```json
{
  "positions": [
    {
      "id": 456,
      "accountId": 123,
      "contractId": "CON.F.US.EP.U25",
      "creationTimestamp": "2024-07-21T13:45:00Z",
      "type": 1,
      "size": 2,
      "averagePrice": 2100.25
    }
  ],
  "success": true,
  "errorCode": 0,
  "errorMessage": null
}
```

**Position Types:**
- 0 = Undefined
- 1 = Long
- 2 = Short

## Trade History

### Get Trades
**Endpoint:** `GET /api/Trade/search`

**Parameters:**
- `accountId`: Account ID
- `startDate`: Start date filter
- `endDate`: End date filter

**Response:**
```json
{
  "trades": [
    {
      "id": 101112,
      "accountId": 123,
      "contractId": "CON.F.US.EP.U25",
      "creationTimestamp": "2024-07-21T13:47:00Z",
      "price": 2100.75,
      "profitAndLoss": 50.25,
      "fees": 2.50,
      "side": 0,
      "size": 1,
      "voided": false,
      "orderId": 789
    }
  ],
  "success": true,
  "errorCode": 0,
  "errorMessage": null
}
```

## Real-Time Data (WebSocket)

### Connection Setup
Uses SignalR WebSocket connections with two hubs:

1. **User Hub:** Account, order, position, and trade updates
2. **Market Hub:** Market data, quotes, depth, and trade events

### User Hub Connection
**URL:** `wss://ws-demo.topstepx.com/hubs/user?access_token={JWT_TOKEN}`

**Subscriptions:**
- `SubscribeAccounts()`: Account balance updates
- `SubscribeOrders(accountId)`: Order status changes
- `SubscribePositions(accountId)`: Position updates
- `SubscribeTrades(accountId)`: Trade executions

**Events:**
- `GatewayUserAccount`: Account updates
- `GatewayUserOrder`: Order status changes
- `GatewayUserPosition`: Position changes
- `GatewayUserTrade`: Trade executions

### Market Hub Connection
**URL:** `wss://gateway-rtc-demo.s2f.projectx.com/hubs/market?access_token={JWT_TOKEN}`

**Subscriptions:**
- `SubscribeContractQuotes(contractId)`: Price quotes
- `SubscribeContractTrades(contractId)`: Market trades
- `SubscribeContractMarketDepth(contractId)`: Order book depth

**Events:**
- `GatewayQuote`: Real-time price quotes
- `GatewayTrade`: Market trade events
- `GatewayDepth`: Order book updates

### Sample Quote Data
```json
{
  "symbol": "F.US.EP",
  "symbolName": "/ES",
  "lastPrice": 2100.25,
  "bestBid": 2100.00,
  "bestAsk": 2100.50,
  "change": 25.50,
  "changePercent": 0.14,
  "open": 2090.00,
  "high": 2110.00,
  "low": 2080.00,
  "volume": 12000,
  "lastUpdated": "2024-07-21T13:45:00Z",
  "timestamp": "2024-07-21T13:45:00Z"
}
```

## Rate Limits

- Standard rate limits apply to all endpoints
- Real-time connections have connection limits
- Historical data limited to 20,000 bars per request

## Error Handling

All responses include:
- `success`: Boolean indicating success/failure
- `errorCode`: Numeric error code (0 = success)
- `errorMessage`: Human-readable error description

Common error codes:
- 401: Unauthorized (invalid/expired token)
- 400: Bad request (invalid parameters)
- 500: Internal server error

## Contract ID Format

Contract IDs follow the pattern: `CON.F.US.{SYMBOL}.{EXPIRY}`

Examples:
- `CON.F.US.EP.U25`: E-mini S&P 500 September 2025
- `CON.F.US.RTY.Z24`: Russell 2000 December 2024
- `CON.F.US.DA6.M25`: Micro Gold June 2025

## Important Notes

1. **Session Management:** Tokens expire after 24 hours
2. **Real-time Reconnection:** Implement automatic reconnection logic
3. **Data Limits:** Historical data requests limited to 20,000 bars
4. **Custom Tags:** Use unique custom tags for order tracking
5. **Time Zones:** All timestamps in UTC
6. **WebSocket Transport:** Use WebSocket transport for real-time data
