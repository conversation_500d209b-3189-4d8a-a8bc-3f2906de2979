#!/usr/bin/env python3
"""
Test the actual core modules with real implementations.
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """Test that all core modules can be imported."""
    print("🔍 Testing core module imports...")
    
    try:
        # Test individual imports
        from core.config import Settings, TopStepEnvironment, LogLevel
        from core.utils import round_price, calculate_position_size, calculate_kelly_fraction
        from core.database import DatabaseManager, MarketData, Orders, Positions
        
        print("✅ All core modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_settings_with_env():
    """Test settings with environment variables."""
    print("🔍 Testing settings with environment variables...")
    
    # Set test environment variables
    test_env = {
        'TOPSTEP_USERNAME': 'test_trader',
        'TOPSTEP_API_KEY': 'sk-test123456789',
        'QWEN_API_KEY': 'qwen-test-key-123',
        'RISK_MAX_DAILY_LOSS': '2500.0',
        'TRADING_ENABLED': 'false',
        'LOG_LEVEL': 'DEBUG'
    }
    
    # Store original values
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    try:
        from core.config import Settings
        
        settings = Settings()
        
        # Test that settings are loaded correctly
        assert settings.topstep.username == 'test_trader'
        assert settings.topstep.api_key.get_secret_value() == 'sk-test123456789'
        assert settings.qwen.api_key.get_secret_value() == 'qwen-test-key-123'
        assert settings.risk.max_daily_loss == 2500.0
        assert settings.trading.enabled == False
        assert settings.log_level.value == 'DEBUG'
        
        # Test URL generation
        assert 'demo' in settings.topstep.base_url
        assert 'demo' in settings.topstep.websocket_url
        
        print("✅ Settings work correctly with environment variables")
        return True
        
    except Exception as e:
        print(f"❌ Settings test failed: {e}")
        return False
    finally:
        # Restore original environment
        for key, value in original_env.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value

def test_utils_functions():
    """Test utility functions."""
    print("🔍 Testing utility functions...")
    
    try:
        from core.utils import (
            round_price, calculate_position_size, calculate_kelly_fraction,
            normalize_contract_id, parse_timeframe, moving_average
        )
        
        # Test price rounding
        assert round_price(100.123, 0.01) == 100.12
        assert round_price(2150.88, 0.25) == 2151.00
        
        # Test position sizing
        size = calculate_position_size(10000, 2.0, 100.0, 98.0)
        assert size == 100
        
        # Test Kelly fraction
        kelly = calculate_kelly_fraction(0.6, 150, 100)
        assert 0 < kelly <= 0.25
        
        # Test contract normalization
        assert normalize_contract_id("con.f.us.ep.u25") == "CON.F.US.EP.U25"
        
        # Test timeframe parsing
        assert parse_timeframe("5m") == 5
        assert parse_timeframe("1h") == 60
        
        # Test moving average
        values = [1, 2, 3, 4, 5]
        ma = moving_average(values, 3)
        assert len(ma) == 3
        assert ma[0] == 2.0
        
        print("✅ Utility functions work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Utility functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_operations():
    """Test database operations."""
    print("🔍 Testing database operations...")
    
    try:
        from core.database import DatabaseManager, MarketData
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set test database URL
            os.environ['DB_URL'] = f'sqlite:///{temp_dir}/test.db'
            
            # Create database manager
            db_manager = DatabaseManager()
            db_manager.initialize()
            await db_manager.create_tables_async()
            
            # Test session creation
            async with db_manager.get_async_session() as session:
                assert session is not None
                
                # Test creating a market data record
                from datetime import datetime, timezone
                market_data = MarketData(
                    contract_id="CON.F.US.EP.U25",
                    timestamp=datetime.now(timezone.utc),
                    open_price=4500.0,
                    high_price=4505.0,
                    low_price=4498.0,
                    close_price=4502.0,
                    volume=1000,
                    timeframe="1m"
                )
                
                session.add(market_data)
                await session.commit()
                
                # Verify the record was created
                from sqlalchemy import select
                result = await session.execute(select(MarketData))
                records = result.scalars().all()
                assert len(records) == 1
                assert records[0].contract_id == "CON.F.US.EP.U25"
            
            await db_manager.close()
        
        print("✅ Database operations work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logging_system():
    """Test logging system."""
    print("🔍 Testing logging system...")
    
    try:
        from core.logging import configure_logging, get_logger, get_trade_logger
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set test logs directory
            os.environ['LOGS_DIR'] = temp_dir
            
            # Configure logging
            configure_logging()
            
            # Test logger creation
            logger = get_logger("test_logger")
            assert logger is not None
            
            # Test logging
            logger.info("Test log message", test_param="test_value")
            
            # Test trade logger
            trade_logger = get_trade_logger()
            trade_logger.log_trade_signal(
                strategy="test_strategy",
                symbol="ES",
                signal_type="BUY",
                confidence=0.85,
                price=4500.0
            )
            
            # Check if log files were created
            logs_path = Path(temp_dir)
            log_files = list(logs_path.glob("*.log"))
            assert len(log_files) > 0
        
        print("✅ Logging system works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Logging system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_complete_integration():
    """Test complete integration of all core components."""
    print("🔍 Testing complete core integration...")
    
    try:
        # Set up test environment
        with tempfile.TemporaryDirectory() as temp_dir:
            test_env = {
                'TOPSTEP_USERNAME': 'integration_test',
                'TOPSTEP_API_KEY': 'sk-integration-test',
                'QWEN_API_KEY': 'qwen-integration-test',
                'DB_URL': f'sqlite:///{temp_dir}/integration.db',
                'LOGS_DIR': temp_dir,
                'DATA_DIR': temp_dir,
                'CACHE_DIR': temp_dir
            }
            
            # Store and set environment
            original_env = {}
            for key, value in test_env.items():
                original_env[key] = os.environ.get(key)
                os.environ[key] = value
            
            try:
                # Import and initialize all components
                from core import (
                    configure_logging, get_logger, get_settings,
                    get_db_manager, round_price, calculate_position_size
                )
                
                # Configure logging
                configure_logging()
                logger = get_logger("integration_test")
                
                # Load settings
                settings = get_settings()
                assert settings.topstep.username == 'integration_test'
                
                # Initialize database
                db_manager = get_db_manager()
                db_manager.initialize()
                await db_manager.create_tables_async()
                
                # Test database session
                async with db_manager.get_async_session() as session:
                    logger.info("Database session created successfully")
                
                # Test utility functions
                price = round_price(4500.123, 0.25)
                size = calculate_position_size(10000, 1.0, price, price - 10)
                
                logger.info("Integration test completed", price=price, size=size)
                
                await db_manager.close()
                
                print("✅ Complete core integration works correctly")
                return True
                
            finally:
                # Restore environment
                for key, value in original_env.items():
                    if value is None:
                        os.environ.pop(key, None)
                    else:
                        os.environ[key] = value
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all core module tests."""
    print("🚀 Testing Core Modules with Real Implementations")
    print("=" * 60)
    
    tests = [
        test_core_imports(),
        test_settings_with_env(),
        test_utils_functions(),
        await test_database_operations(),
        test_logging_system(),
        await test_complete_integration()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("=" * 60)
    if passed == total:
        print(f"🎉 ALL {total} CORE MODULE TESTS PASSED! 100% SUCCESS!")
        print("✅ Core modules are fully functional with real implementations")
        print("✅ Configuration, logging, database, and utilities all working")
        print("✅ Ready to proceed to TopStep API Integration")
        return True
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("❌ Core modules need fixing before proceeding")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
