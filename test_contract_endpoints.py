#!/usr/bin/env python3
"""
Test different contract endpoints to see which one works.
"""

import os
import sys
import asyncio
import ssl
import aiohttp
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_contract_endpoints():
    """Test all contract endpoints to see which works."""
    
    # First authenticate
    username = os.getenv('TOPSTEP_USERNAME')
    api_key = os.getenv('TOPSTEP_API_KEY')
    base_url = os.getenv('TOPSTEP_GATEWAY_URL', 'https://api.topstepx.com')
    
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    
    async with aiohttp.ClientSession(connector=connector) as session:
        # Authenticate
        print("🔐 Authenticating...")
        auth_payload = {"userName": username, "apiKey": api_key}
        auth_headers = {'accept': 'text/plain', 'Content-Type': 'application/json'}
        
        async with session.post(f"{base_url}/api/Auth/loginKey", json=auth_payload, headers=auth_headers) as response:
            if response.status != 200:
                print(f"❌ Authentication failed: {response.status}")
                return
            
            result = await response.json()
            if not result.get('success'):
                print(f"❌ Authentication failed: {result.get('errorMessage')}")
                return
            
            token = result.get('token')
            print(f"✅ Authenticated successfully")
        
        # Headers for authenticated requests
        headers = {
            'accept': 'text/plain',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }
        
        # Test 1: /api/Contract/available
        print("\n🔍 Testing /api/Contract/available...")
        try:
            data = {"live": True}
            async with session.post(f"{base_url}/api/Contract/available", json=data, headers=headers) as response:
                response_text = await response.text()
                print(f"   Status: {response.status}")
                print(f"   Response: {response_text}")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 2: /api/Contract/search with no query
        print("\n🔍 Testing /api/Contract/search (no query)...")
        try:
            async with session.get(f"{base_url}/api/Contract/search", headers=headers) as response:
                response_text = await response.text()
                print(f"   Status: {response.status}")
                print(f"   Response: {response_text[:500]}...")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 3: /api/Contract/search with ES query
        print("\n🔍 Testing /api/Contract/search?query=ES...")
        try:
            params = {"query": "ES", "limit": 10}
            async with session.get(f"{base_url}/api/Contract/search", params=params, headers=headers) as response:
                response_text = await response.text()
                print(f"   Status: {response.status}")
                print(f"   Response: {response_text[:500]}...")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 4: /api/Contract/search with EP query (ES contract symbol)
        print("\n🔍 Testing /api/Contract/search?query=EP...")
        try:
            params = {"query": "EP", "limit": 10}
            async with session.get(f"{base_url}/api/Contract/search", params=params, headers=headers) as response:
                response_text = await response.text()
                print(f"   Status: {response.status}")
                print(f"   Response: {response_text[:500]}...")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 5: Get specific contract by ID
        print("\n🔍 Testing /api/Contract/{contractId} with ES contract...")
        try:
            contract_id = "CON.F.US.EP.U25"  # ES September 2025
            async with session.get(f"{base_url}/api/Contract/{contract_id}", headers=headers) as response:
                response_text = await response.text()
                print(f"   Status: {response.status}")
                print(f"   Response: {response_text[:500]}...")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test 6: Try different contract endpoint variations
        print("\n🔍 Testing alternative endpoints...")
        
        endpoints_to_try = [
            "/api/Contract/list",
            "/api/Contract/all", 
            "/api/Contracts/available",
            "/api/Contracts/search"
        ]
        
        for endpoint in endpoints_to_try:
            try:
                print(f"   Trying {endpoint}...")
                async with session.get(f"{base_url}{endpoint}", headers=headers) as response:
                    if response.status == 200:
                        response_text = await response.text()
                        print(f"   ✅ {endpoint} works! Response: {response_text[:200]}...")
                    else:
                        print(f"   ❌ {endpoint} failed with status {response.status}")
            except Exception as e:
                print(f"   ❌ {endpoint} error: {e}")

if __name__ == "__main__":
    asyncio.run(test_contract_endpoints())
