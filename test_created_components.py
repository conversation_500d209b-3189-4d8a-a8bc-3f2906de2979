#!/usr/bin/env python3
"""
TEST THE ACTUAL COMPONENTS I CREATED
Tests all the real components with actual functionality.
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_core_configuration_real():
    """Test the real core configuration system."""
    print("🔍 Testing Core Configuration System...")
    
    try:
        from core.config import Settings, TopStepEnvironment, LogLevel
        
        # Test settings loading
        settings = Settings()
        
        # Verify real credentials are loaded
        assert settings.topstep.username == "mrrain"
        assert settings.topstep.api_key.get_secret_value() == "FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs="
        assert settings.qwen.api_key.get_secret_value() == "sk-be8c17f1999b46f380e1a1e2dc687b53"
        
        # Test configuration methods
        assert settings.topstep.base_url is not None
        assert settings.topstep.websocket_url is not None
        assert settings.is_development() or settings.is_production()
        
        # Test risk configuration
        assert settings.risk.max_daily_loss > 0
        assert 0 <= settings.risk.default_stop_loss_pct <= 100
        
        print("✅ Core configuration system working with real credentials")
        return True
        
    except Exception as e:
        print(f"❌ Core configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_logging_system_real():
    """Test the real logging system."""
    print("🔍 Testing Logging System...")
    
    try:
        from core.logging import configure_logging, get_logger, get_trade_logger, get_performance_logger
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set test logs directory
            os.environ['LOGS_DIR'] = temp_dir
            
            # Configure logging
            configure_logging()
            
            # Test logger creation
            logger = get_logger("test_component")
            trade_logger = get_trade_logger()
            perf_logger = get_performance_logger()
            
            # Test logging functionality
            logger.info("Test log message", component="test", status="working")
            
            # Test trade logging
            trade_logger.log_trade_signal(
                strategy="test_strategy",
                symbol="ES",
                signal_type="BUY",
                confidence=0.85,
                price=4500.0,
                metadata={"test": True}
            )
            
            # Test performance logging
            perf_logger.log_api_call(
                endpoint="/test",
                method="GET",
                duration_ms=150.5,
                status_code=200
            )
            
            # Verify log files were created
            logs_path = Path(temp_dir)
            log_files = list(logs_path.glob("*.log"))
            assert len(log_files) > 0, "No log files created"
            
            print(f"✅ Logging system working - {len(log_files)} log files created")
            return True
            
    except Exception as e:
        print(f"❌ Logging system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_system_real():
    """Test the real database system."""
    print("🔍 Testing Database System...")
    
    try:
        from core.database import DatabaseManager, MarketData, TradingSignals, Orders, Positions
        from core import init_database, get_db_session
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set test database
            os.environ['DB_URL'] = f'sqlite:///{temp_dir}/test.db'
            
            # Initialize database
            await init_database()
            
            # Test database operations
            async with get_db_session() as session:
                # Create market data
                market_data = MarketData(
                    contract_id="CON.F.US.EP.U25",
                    timestamp=datetime.now(timezone.utc),
                    open_price=4500.0,
                    high_price=4505.0,
                    low_price=4498.0,
                    close_price=4502.0,
                    volume=1000,
                    timeframe="1m"
                )
                session.add(market_data)
                
                # Create trading signal
                signal = TradingSignals(
                    strategy_name="fvg",
                    contract_id="CON.F.US.EP.U25",
                    signal_type="BUY",
                    confidence=0.75,
                    price=4502.0,
                    metadata={"test": True}
                )
                session.add(signal)
                
                # Create order
                order = Orders(
                    account_id=123,
                    contract_id="CON.F.US.EP.U25",
                    order_type="MARKET",
                    side="BUY",
                    size=1,
                    status="PENDING",
                    strategy_name="fvg"
                )
                session.add(order)
                
                await session.commit()
            
            # Verify data was stored
            async with get_db_session() as session:
                from sqlalchemy import select
                
                # Check market data
                result = await session.execute(select(MarketData))
                market_records = result.scalars().all()
                assert len(market_records) >= 1
                
                # Check signals
                result = await session.execute(select(TradingSignals))
                signal_records = result.scalars().all()
                assert len(signal_records) >= 1
                
                # Check orders
                result = await session.execute(select(Orders))
                order_records = result.scalars().all()
                assert len(order_records) >= 1
            
            print("✅ Database system working with real data models")
            return True
            
    except Exception as e:
        print(f"❌ Database system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_utility_functions_real():
    """Test the real utility functions."""
    print("🔍 Testing Utility Functions...")
    
    try:
        from core.utils import (
            round_price, calculate_position_size, calculate_kelly_fraction,
            normalize_contract_id, parse_timeframe, moving_average,
            generate_order_tag, RateLimiter
        )
        
        # Test price rounding with real market scenarios
        assert round_price(4500.123, 0.25) == 4500.00
        assert round_price(4500.88, 0.25) == 4501.00
        assert round_price(1.08567, 0.00001) == 1.08567
        
        # Test position sizing with real trading scenarios
        size = calculate_position_size(50000, 1.0, 4500, 4480, 20)  # $50k account, 1% risk, NQ
        assert size > 0
        
        # Test Kelly criterion
        kelly = calculate_kelly_fraction(0.6, 150, 100)
        assert 0 <= kelly <= 0.25
        
        # Test contract normalization
        assert normalize_contract_id("con.f.us.ep.u25") == "CON.F.US.EP.U25"
        
        # Test timeframe parsing
        assert parse_timeframe("5m") == 5
        assert parse_timeframe("1h") == 60
        
        # Test moving average
        prices = [4500, 4505, 4510, 4508, 4512]
        ma = moving_average(prices, 3)
        assert len(ma) == 3
        
        # Test order tag generation
        tag = generate_order_tag("fvg", "buy")
        assert tag.startswith("fvg_")
        assert len(tag) > 10
        
        # Test rate limiter
        limiter = RateLimiter(max_calls=2, time_window=1.0)
        assert await limiter.acquire() == True
        assert await limiter.acquire() == True
        assert await limiter.acquire() == False  # Should be rate limited
        
        print("✅ Utility functions working with real trading calculations")
        return True
        
    except Exception as e:
        print(f"❌ Utility functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_models_real():
    """Test the real API models."""
    print("🔍 Testing API Models...")
    
    try:
        from api.models import (
            MarketDataBar, Order, Position, Trade, OrderRequest, 
            OrderType, OrderSide, OrderStatus, PositionType
        )
        
        # Test market data bar
        bar_data = {
            "t": "2024-01-01T10:00:00Z",
            "o": 4500.0,
            "h": 4505.0,
            "l": 4498.0,
            "c": 4502.0,
            "v": 1000
        }
        bar = MarketDataBar(**bar_data)
        assert bar.open_price == 4500.0
        assert bar.volume == 1000
        
        # Test order model
        order_data = {
            "id": 123,
            "account_id": 456,
            "contract_id": "CON.F.US.EP.U25",
            "creation_timestamp": datetime.now(timezone.utc),
            "status": OrderStatus.OPEN,
            "type": OrderType.MARKET,
            "side": OrderSide.BID,
            "size": 1
        }
        order = Order(**order_data)
        assert order.id == 123
        assert order.order_type == OrderType.MARKET
        
        # Test order request
        request_data = {
            "account_id": 456,
            "contract_id": "CON.F.US.EP.U25",
            "type": OrderType.MARKET,
            "side": OrderSide.BID,
            "size": 1
        }
        order_request = OrderRequest(**request_data)
        assert order_request.account_id == 456
        
        print("✅ API models working with real data structures")
        return True
        
    except Exception as e:
        print(f"❌ API models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_llm_models_real():
    """Test the real LLM models."""
    print("🔍 Testing LLM Models...")
    
    try:
        from llm.models import (
            TradingDecision, MarketAnalysis, LLMResponse, LLMPrompt,
            TradingAction, MarketSentiment
        )
        
        # Test LLM response
        response = LLMResponse(
            content="This is a test response",
            usage_tokens=50,
            model="qwen-turbo"
        )
        assert response.content == "This is a test response"
        assert response.usage_tokens == 50
        
        # Test trading decision
        decision = TradingDecision(
            action=TradingAction.BUY,
            confidence=0.85,
            reasoning="Strong bullish signals detected",
            entry_price=4500.0,
            stop_loss=4480.0,
            take_profit=4520.0
        )
        assert decision.action == TradingAction.BUY
        assert decision.confidence == 0.85
        
        # Test market analysis
        analysis = MarketAnalysis(
            symbol="ES",
            sentiment=MarketSentiment.BULLISH,
            trend_direction="UP",
            confidence=0.8,
            analysis_text="Market showing strong bullish momentum"
        )
        assert analysis.symbol == "ES"
        assert analysis.sentiment == MarketSentiment.BULLISH
        
        # Test LLM prompt
        prompt = LLMPrompt(
            system_prompt="You are a trading assistant",
            user_prompt="Analyze this market data",
            max_tokens=1000,
            temperature=0.1
        )
        assert prompt.system_prompt == "You are a trading assistant"
        assert prompt.max_tokens == 1000
        
        print("✅ LLM models working with real data structures")
        return True
        
    except Exception as e:
        print(f"❌ LLM models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_integrated_workflow_real():
    """Test integrated workflow with real components."""
    print("🔍 Testing Integrated Workflow...")
    
    try:
        # Test complete workflow integration
        from core import configure_logging, get_logger, get_settings
        from core.database import MarketData
        from core import get_db_session, init_database
        from core.utils import round_price, calculate_position_size
        from api.models import OrderRequest, OrderType, OrderSide
        from llm.models import TradingDecision, TradingAction
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Setup environment
            os.environ['LOGS_DIR'] = temp_dir
            os.environ['DB_URL'] = f'sqlite:///{temp_dir}/workflow.db'
            
            # Initialize systems
            configure_logging()
            logger = get_logger("workflow_test")
            settings = get_settings()
            await init_database()
            
            # Simulate market data processing
            current_price = 4500.0
            rounded_price = round_price(current_price, 0.25)
            
            # Simulate position sizing
            position_size = calculate_position_size(
                account_balance=50000,
                risk_percentage=1.0,
                entry_price=rounded_price,
                stop_loss_price=rounded_price - 20,
                contract_multiplier=20
            )
            
            # Store market data
            async with get_db_session() as session:
                market_data = MarketData(
                    contract_id="CON.F.US.EP.U25",
                    timestamp=datetime.now(timezone.utc),
                    open_price=rounded_price,
                    high_price=rounded_price + 5,
                    low_price=rounded_price - 3,
                    close_price=rounded_price + 2,
                    volume=1000,
                    timeframe="1m"
                )
                session.add(market_data)
                await session.commit()
            
            # Simulate trading decision
            decision = TradingDecision(
                action=TradingAction.BUY,
                confidence=0.8,
                reasoning="Workflow test decision",
                entry_price=rounded_price,
                stop_loss=rounded_price - 20,
                take_profit=rounded_price + 30,
                position_size=position_size
            )
            
            # Create order request
            order_request = OrderRequest(
                account_id=123,
                contract_id="CON.F.US.EP.U25",
                order_type=OrderType.MARKET,
                side=OrderSide.BID,
                size=position_size
            )
            
            # Log the workflow
            logger.info("Workflow completed successfully", 
                       price=rounded_price, 
                       position_size=position_size,
                       decision=decision.action.value)
            
            # Verify everything worked
            assert rounded_price > 0
            assert position_size > 0
            assert decision.confidence > 0
            assert order_request.size == position_size
            
            print("✅ Integrated workflow working with all real components")
            return True
            
    except Exception as e:
        print(f"❌ Integrated workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all component tests."""
    print("🚀 TESTING ALL CREATED COMPONENTS WITH REAL FUNCTIONALITY")
    print("=" * 80)
    
    tests = [
        await test_core_configuration_real(),
        await test_logging_system_real(),
        await test_database_system_real(),
        await test_utility_functions_real(),
        await test_api_models_real(),
        await test_llm_models_real(),
        await test_integrated_workflow_real()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("=" * 80)
    if passed == total:
        print(f"🎉 ALL {total} COMPONENT TESTS PASSED! 100% SUCCESS!")
        print("✅ Core configuration system working with real credentials")
        print("✅ Logging system working with real file operations")
        print("✅ Database system working with real data models")
        print("✅ Utility functions working with real trading calculations")
        print("✅ API models working with real data structures")
        print("✅ LLM models working with real decision structures")
        print("✅ Integrated workflow working with all components")
        print("✅ ALL CREATED COMPONENTS ARE FULLY FUNCTIONAL!")
        print("=" * 80)
        return True
    else:
        print(f"❌ {total - passed} out of {total} component tests failed")
        print("❌ Some components need fixing before proceeding")
        print("=" * 80)
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
