"""
Data models for TopStep API integration.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class OrderType(int, Enum):
    """Order types."""
    UNKNOWN = 0
    LIMIT = 1
    MARKET = 2
    STOP_LIMIT = 3
    STOP = 4
    TRAILING_STOP = 5
    JOIN_BID = 6
    JOIN_ASK = 7


class OrderSide(int, Enum):
    """Order sides."""
    BID = 0  # Buy
    ASK = 1  # Sell


class OrderStatus(int, Enum):
    """Order status."""
    NONE = 0
    OPEN = 1
    FILLED = 2
    CANCELLED = 3
    EXPIRED = 4
    REJECTED = 5
    PENDING = 6


class PositionType(int, Enum):
    """Position types."""
    UNDEFINED = 0
    LONG = 1
    SHORT = 2


class AuthResponse(BaseModel):
    """Authentication response model."""
    token: str
    success: bool
    error_code: int = Field(alias="errorCode")
    error_message: Optional[str] = Field(alias="errorMessage")


class MarketDataBar(BaseModel):
    """Market data bar model."""
    timestamp: datetime = Field(alias="t")
    open_price: float = Field(alias="o")
    high_price: float = Field(alias="h")
    low_price: float = Field(alias="l")
    close_price: float = Field(alias="c")
    volume: int = Field(alias="v")


class Contract(BaseModel):
    """Contract model."""
    id: str
    symbol: str
    name: str
    tick_size: float
    multiplier: float
    currency: str
    exchange: str


class Order(BaseModel):
    """Order model."""
    id: int
    external_order_id: Optional[int] = None
    account_id: int
    contract_id: str
    symbol_id: Optional[str] = None
    creation_timestamp: datetime
    update_timestamp: Optional[datetime] = None
    status: OrderStatus
    order_type: OrderType = Field(alias="type")
    side: OrderSide
    size: int
    limit_price: Optional[float] = None
    stop_price: Optional[float] = None
    fill_volume: int = 0
    filled_price: Optional[float] = None
    custom_tag: Optional[str] = None


class Position(BaseModel):
    """Position model."""
    model_config = {"populate_by_name": True}

    id: int
    external_position_id: Optional[int] = Field(alias="externalPositionId", default=None)
    account_id: int = Field(alias="accountId")
    contract_id: str = Field(alias="contractId")
    creation_timestamp: datetime = Field(alias="creationTimestamp")
    position_type: PositionType = Field(alias="type")
    size: int
    average_price: float = Field(alias="averagePrice")
    unrealized_pnl: float = Field(alias="unrealizedPnl", default=0.0)
    realized_pnl: float = Field(alias="realizedPnl", default=0.0)


class Trade(BaseModel):
    """Trade model."""
    id: int
    external_trade_id: Optional[int] = None
    account_id: int
    contract_id: str
    creation_timestamp: datetime
    price: float
    profit_and_loss: Optional[float] = None
    fees: float = 0.0
    side: OrderSide
    size: int
    voided: bool = False
    order_id: Optional[int] = None


class Account(BaseModel):
    """Account model."""
    id: int
    name: str
    balance: float
    can_trade: bool
    is_visible: bool
    simulated: bool


class Quote(BaseModel):
    """Real-time quote model."""
    symbol: str
    symbol_name: Optional[str] = None
    last_price: float
    best_bid: float
    best_ask: float
    change: float
    change_percent: float
    open_price: float = Field(alias="open")
    high_price: float = Field(alias="high")
    low_price: float = Field(alias="low")
    volume: int
    last_updated: datetime
    timestamp: datetime


class MarketDepth(BaseModel):
    """Market depth model."""
    timestamp: datetime
    depth_type: int = Field(alias="type")
    price: float
    volume: int
    current_volume: int


class MarketTrade(BaseModel):
    """Market trade model."""
    symbol_id: str
    price: float
    timestamp: datetime
    trade_type: int = Field(alias="type")
    volume: int


class OrderRequest(BaseModel):
    """Order placement request."""
    model_config = {"populate_by_name": True}

    account_id: int = Field(alias="accountId")
    contract_id: str = Field(alias="contractId")
    order_type: OrderType = Field(alias="type")
    side: OrderSide
    size: int
    limit_price: Optional[float] = Field(alias="limitPrice", default=None)
    stop_price: Optional[float] = Field(alias="stopPrice", default=None)
    trail_price: Optional[float] = Field(alias="trailPrice", default=None)
    custom_tag: Optional[str] = Field(alias="customTag", default=None)
    linked_order_id: Optional[int] = Field(alias="linkedOrderId", default=None)


class OrderResponse(BaseModel):
    """Order placement response."""
    model_config = {"populate_by_name": True}

    order_id: int = Field(alias="orderId")
    success: bool
    error_code: int = Field(alias="errorCode")
    error_message: Optional[str] = Field(alias="errorMessage")


class MarketDataRequest(BaseModel):
    """Market data request."""
    contract_id: str
    live: bool = False
    start_time: datetime
    end_time: datetime
    unit: int  # 1=Second, 2=Minute, 3=Hour, 4=Day, 5=Week, 6=Month
    unit_number: int
    limit: int = 1000
    include_partial_bar: bool = False


class MarketDataResponse(BaseModel):
    """Market data response."""
    bars: List[MarketDataBar]
    success: bool
    error_code: int = Field(alias="errorCode")
    error_message: Optional[str] = Field(alias="errorMessage")


class APIResponse(BaseModel):
    """Generic API response."""
    success: bool
    error_code: int = Field(alias="errorCode")
    error_message: Optional[str] = Field(alias="errorMessage")
    data: Optional[Dict[str, Any]] = None
