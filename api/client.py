"""
TopStep API client implementation.
Real HTTP client for TopStep API integration using working implementation.
"""

import os
import asyncio
import ssl
import time
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
import aiohttp
import json

from core import get_logger, get_settings, retry_decorator, timing_decorator, get_performance_logger
from .models import (
    AuthResponse, MarketDataResponse, MarketDataRequest, OrderRequest, OrderResponse,
    Account, Contract, Order, Position, Trade, APIResponse, MarketDataBar
)

logger = get_logger(__name__)
perf_logger = get_performance_logger()


class TopStepAPIError(Exception):
    """TopStep API error."""
    def __init__(self, message: str, error_code: int = 0, status_code: int = 0):
        super().__init__(message)
        self.error_code = error_code
        self.status_code = status_code


class TopStepClient:
    """TopStep API client with real HTTP implementation."""

    def __init__(self, preferred_account_type: str = "PRACTICE"):
        self.settings = get_settings()
        self.base_url = os.getenv('TOPSTEP_GATEWAY_URL', 'https://api.topstepx.com')
        self.username = self.settings.topstep.username
        self.api_key = self.settings.topstep.api_key.get_secret_value()
        self.session: Optional[aiohttp.ClientSession] = None
        self.session_token: Optional[str] = None
        self.token: Optional[str] = None  # For _make_request compatibility
        self.token_expires_at: Optional[datetime] = None
        self.account_id: Optional[int] = None
        self.current_account: Optional[Dict] = None
        self.available_accounts: List[Dict] = []
        self.preferred_account_type = preferred_account_type
        self.authenticated = False
        self._auth_lock = asyncio.Lock()
    
    async def __aenter__(self):
        """Async context manager entry."""
        if not self.session:
            # Create SSL context that handles certificate verification issues
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # Create connector with SSL context
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self.session = aiohttp.ClientSession(connector=connector)

        if not self.authenticated:
            await self.authenticate()

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        # Close session to prevent leaks
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    async def close(self) -> None:
        """Explicitly close the session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            logger.info("TopStep API session closed")

    def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication token."""
        if not self.session_token:
            raise ValueError("Not authenticated - session token missing")

        return {
            'accept': 'text/plain',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.session_token}'
        }
    
    @timing_decorator
    @retry_decorator(max_retries=3, delay=1.0, exceptions=(aiohttp.ClientError, TopStepAPIError))
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        require_auth: bool = True
    ) -> Dict[str, Any]:
        """Make HTTP request to TopStep API."""
        if not self.session:
            await self._create_session()
        
        url = f"{self.settings.topstep.base_url}{endpoint}"
        headers = {}
        
        if require_auth:
            await self._ensure_authenticated()
            headers['Authorization'] = f'Bearer {self.token}'
        
        start_time = time.time()
        
        try:
            async with self.session.request(method, url, json=data, headers=headers) as response:
                duration_ms = (time.time() - start_time) * 1000
                
                # Log API call performance
                perf_logger.log_api_call(
                    endpoint=endpoint,
                    method=method,
                    duration_ms=duration_ms,
                    status_code=response.status
                )
                
                response_text = await response.text()
                
                if response.status == 401:
                    # Token expired, clear it and retry
                    self.token = None
                    self.token_expires_at = None
                    raise TopStepAPIError("Authentication failed", status_code=401)
                
                if response.status >= 400:
                    logger.error(f"API request failed: {response.status} - {response_text}")
                    raise TopStepAPIError(
                        f"API request failed: {response.status}",
                        status_code=response.status
                    )
                
                try:
                    return json.loads(response_text)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON response: {response_text}")
                    raise TopStepAPIError("Invalid JSON response")
        
        except aiohttp.ClientError as e:
            duration_ms = (time.time() - start_time) * 1000
            perf_logger.log_api_call(
                endpoint=endpoint,
                method=method,
                duration_ms=duration_ms,
                status_code=0,
                error=str(e)
            )
            logger.error(f"HTTP request failed: {e}")
            raise TopStepAPIError(f"HTTP request failed: {e}")
    
    async def authenticate(self) -> bool:
        """Authenticate with TopStep API."""
        try:
            if not self.session:
                await self._create_session()

            if not self.username or not self.api_key:
                logger.error("TopStep credentials not found in environment")
                return False

            url = f"{self.base_url}/api/Auth/loginKey"
            payload = {
                "userName": self.username,
                "apiKey": self.api_key
            }

            headers = {
                'accept': 'text/plain',
                'Content-Type': 'application/json'
            }

            async with self.session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()

                    if result.get('success') and result.get('errorCode') == 0:
                        self.session_token = result.get('token')
                        self.token = result.get('token')  # Also set token for _make_request compatibility
                        self.authenticated = True
                        logger.info("✅ TopStep authentication successful")

                        # Get account information
                        if await self._get_account_info():
                            return True
                        else:
                            logger.error("Failed to retrieve account information")
                            self.authenticated = False
                            return False
                    else:
                        error_msg = result.get('errorMessage', 'Authentication failed')
                        logger.error(f"Authentication failed: {error_msg}")
                        return False
                else:
                    logger.error(f"Authentication failed with status {response.status}")
                    return False

        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return False

    async def _create_session(self) -> None:
        """Create HTTP session if it doesn't exist."""
        if not self.session or self.session.closed:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self.session = aiohttp.ClientSession(connector=connector)

    async def _ensure_authenticated(self) -> None:
        """Ensure we have a valid authentication token."""
        if not self.token or (self.token_expires_at and datetime.now() >= self.token_expires_at):
            logger.info("Token expired or missing, re-authenticating...")
            success = await self.authenticate()
            if not success:
                raise TopStepAPIError("Failed to authenticate with TopStep API")

    @timing_decorator
    async def validate_session(self) -> bool:
        """Validate current session token."""
        try:
            response_data = await self._make_request("POST", "/api/Auth/validate")
            return response_data.get("success", False)
        except TopStepAPIError:
            return False
    
    @timing_decorator
    async def get_accounts(self) -> List[Account]:
        """Get user accounts."""
        data = {"onlyActiveAccounts": True}
        response_data = await self._make_request("POST", "/api/Account/search", data)
        
        if not response_data.get("success", False):
            raise TopStepAPIError(
                f"Failed to get accounts: {response_data.get('errorMessage', 'Unknown error')}"
            )
        
        accounts_data = response_data.get("accounts", [])
        return [Account(**account) for account in accounts_data]
    
    @timing_decorator
    async def get_market_data(
        self, 
        contract_id: str,
        start_time: datetime,
        end_time: datetime,
        timeframe: str = "1m",
        limit: int = 1000
    ) -> MarketDataResponse:
        """Get historical market data."""
        
        # Convert timeframe to API format
        unit_map = {
            "1s": (1, 1), "1m": (2, 1), "5m": (2, 5), "15m": (2, 15), "30m": (2, 30),
            "1h": (3, 1), "4h": (3, 4), "1d": (4, 1), "1w": (5, 1)
        }
        
        if timeframe not in unit_map:
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        
        unit, unit_number = unit_map[timeframe]
        
        request_data = {
            "contractId": contract_id,
            "live": False,
            "startTime": start_time.isoformat(),
            "endTime": end_time.isoformat(),
            "unit": unit,
            "unitNumber": unit_number,
            "limit": limit,
            "includePartialBar": False
        }
        
        response_data = await self._make_request("POST", "/api/History/retrieveBars", data=request_data)
        return MarketDataResponse(**response_data)
    
    @timing_decorator
    async def search_contracts(self, query: str = "", limit: int = 100) -> List[Contract]:
        """Search for contracts."""
        params = {"query": query, "limit": limit} if query else {"limit": limit}
        
        response_data = await self._make_request("GET", "/api/Contract/search", data=params)
        
        if not response_data.get("success", False):
            raise TopStepAPIError(
                f"Failed to search contracts: {response_data.get('errorMessage', 'Unknown error')}"
            )
        
        contracts_data = response_data.get("contracts", [])
        return [Contract(**contract) for contract in contracts_data]
    
    @timing_decorator
    async def get_available_contracts(self) -> List[Contract]:
        """Get all available contracts."""
        response_data = await self._make_request("GET", "/api/Contract/available")
        
        if not response_data.get("success", False):
            raise TopStepAPIError(
                f"Failed to get contracts: {response_data.get('errorMessage', 'Unknown error')}"
            )
        
        contracts_data = response_data.get("contracts", [])
        return [Contract(**contract) for contract in contracts_data]
    
    @timing_decorator
    async def place_order(self, order_request: OrderRequest) -> OrderResponse:
        """Place a trading order."""
        order_data = order_request.model_dump(by_alias=True, mode='json')

        # Debug logging to see what we're sending
        logger.info(f"Order request data: {order_data}")

        response_data = await self._make_request("POST", "/api/Order/place", data=order_data)

        # Handle None response from API
        if response_data is None:
            self.logger.warning("TopStep API returned None for order placement")
            return OrderResponse(
                order_id=0,
                success=False,
                error_code=999,
                error_message="API returned no response"
            )

        return OrderResponse(**response_data)
    
    @timing_decorator
    async def cancel_order(self, order_id: int) -> APIResponse:
        """Cancel an order."""
        response_data = await self._make_request("DELETE", f"/api/Order/{order_id}")
        return APIResponse(**response_data)
    
    @timing_decorator
    async def get_orders(self, account_id: int, open_only: bool = False) -> List[Order]:
        """Get orders for an account."""
        if open_only:
            endpoint = "/api/Order/searchOpen"
            params = {"accountId": account_id}
        else:
            endpoint = "/api/Order/search"
            # Order search requires date range with timestamp fields
            from datetime import datetime, timedelta
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)  # Last 24 hours
            params = {
                "accountId": account_id,
                "startTimestamp": start_time.isoformat(),
                "endTimestamp": end_time.isoformat()
            }

        # Use POST for search endpoints (like other TopStep search APIs)
        response_data = await self._make_request("POST", endpoint, data=params)

        if not response_data.get("success", False):
            raise TopStepAPIError(
                f"Failed to get orders: {response_data.get('errorMessage', 'Unknown error')}"
            )

        orders_data = response_data.get("orders", [])
        return [Order(**order) for order in orders_data]
    
    @timing_decorator
    async def get_positions(self, account_id: int) -> List[Position]:
        """Get positions for an account."""
        params = {"accountId": account_id}

        response_data = await self._make_request("POST", "/api/Position/searchOpen", data=params)

        if response_data is None:
            logger.warning("Position API returned None response")
            return []

        if not response_data.get("success", False):
            logger.warning(f"Position API failed: {response_data.get('errorMessage', 'Unknown error')}")
            return []
        
        positions_data = response_data.get("positions", [])
        return [Position(**position) for position in positions_data]
    
    @timing_decorator
    async def get_trades(self, account_id: int) -> List[Trade]:
        """Get trades for an account."""
        params = {"accountId": account_id}
        
        response_data = await self._make_request("GET", "/api/Trade/search", data=params)
        
        if not response_data.get("success", False):
            raise TopStepAPIError(
                f"Failed to get trades: {response_data.get('errorMessage', 'Unknown error')}"
            )
        
        trades_data = response_data.get("trades", [])
        return [Trade(**trade) for trade in trades_data]



    async def _get_account_info(self) -> bool:
        """Get account information and select preferred account"""
        try:
            data = {"onlyActiveAccounts": True}
            response = await self._make_request('POST', '/api/Account/search', data)

            if response and response.get('success'):
                accounts = response.get('accounts', [])
                self.available_accounts = accounts

                if accounts:
                    selected_account = self._select_account_by_type(accounts, self.preferred_account_type)

                    if selected_account:
                        self.account_id = selected_account.get('id')
                        self.current_account = selected_account
                        account_type = "PRACTICE" if "PRACTICE" in selected_account.get('name', '') else "LIVE"
                        logger.info(f"Selected {account_type} account: {selected_account.get('name')} (ID: {self.account_id})")
                        return True
                    else:
                        logger.error(f"No {self.preferred_account_type} account found")
                        return False
                else:
                    logger.error("No active trading accounts found")
                    return False
            else:
                logger.error("Failed to retrieve account information")
                return False

        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return False

    def _select_account_by_type(self, accounts: List[Dict], preferred_type: str) -> Optional[Dict]:
        """Select account based on preferred type"""
        if preferred_type == "PRACTICE":
            for account in accounts:
                if "PRACTICE" in account.get('name', '').upper():
                    return account
        elif preferred_type == "COMBINE_50K":
            for account in accounts:
                if "$50K COMBINE" in account.get('name', ''):
                    return account
        elif preferred_type == "COMBINE_150K":
            for account in accounts:
                if "$150K COMBINE" in account.get('name', ''):
                    return account

        return accounts[0] if accounts else None

    async def select_account(self, account_id: int) -> bool:
        """Select an account by ID."""
        try:
            # Find the account in available accounts
            for account in self.available_accounts:
                if account.get('id') == account_id:
                    self.account_id = account_id
                    self.current_account = account
                    logger.info(f"Selected account: {account_id}")
                    return True

            logger.error(f"Account {account_id} not found in available accounts")
            return False

        except Exception as e:
            logger.error(f"Error selecting account: {e}")
            return False

    async def get_account_balance(self) -> float:
        """Get current account balance."""
        try:
            if self.current_account:
                return float(self.current_account.get('balance', 0.0))
            return 0.0
        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return 0.0

    async def get_current_positions(self) -> list:
        """Get current positions for selected account."""
        try:
            if self.account_id:
                positions = await self.get_positions(self.account_id)
                return [pos.__dict__ for pos in positions] if positions else []
            return []
        except Exception as e:
            logger.error(f"Error getting current positions: {e}")
            return []

    def convert_symbol_to_contract(self, symbol: str) -> str:
        """Convert a symbol to contract ID"""
        mapping = {
            'ES': 'CON.F.US.EP.U25',   # E-Mini S&P 500
            'NQ': 'CON.F.US.ENQ.U25',  # E-mini NASDAQ-100
            'YM': 'CON.F.US.YM.U25',   # Dow Jones E-mini
            'RTY': 'CON.F.US.RTY.U25', # E-mini Russell 2000
            'CL': 'CON.F.US.CL.U25',   # Crude Oil
            'GC': 'CON.F.US.GC.U25',   # Gold
            'MES': 'CON.F.US.MES.U25', # Micro E-mini S&P 500
            'MNQ': 'CON.F.US.MNQ.U25', # Micro E-mini NASDAQ-100
        }
        return mapping.get(symbol, symbol)
