#!/usr/bin/env python3
"""
Trading Dashboard Server.
Real-time web dashboard for the agentic trading system.
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from dashboard.api import DashboardAPI
from fastapi.staticfiles import StaticFiles


async def main():
    """Run the dashboard server."""
    
    print("🎛️ TRADING DASHBOARD SERVER")
    print("=" * 50)
    print("Starting real-time trading dashboard...")
    print("Dashboard will be available at: http://localhost:8000")
    print("=" * 50)
    
    # Create dashboard API
    dashboard = DashboardAPI()
    
    # Mount static files
    static_dir = project_root / "dashboard" / "static"
    static_dir.mkdir(exist_ok=True)
    
    dashboard.app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    # Add startup event
    @dashboard.app.on_event("startup")
    async def startup_event():
        dashboard.add_log_entry(
            dashboard.models.LogLevel.INFO,
            "Dashboard server started",
            "Server"
        )
        print("✅ Dashboard server started successfully")
        print("🌐 Open http://localhost:8000 in your browser")
    
    @dashboard.app.on_event("shutdown")
    async def shutdown_event():
        dashboard.add_log_entry(
            dashboard.models.LogLevel.INFO,
            "Dashboard server shutting down",
            "Server"
        )
        
        # Stop trading system if running
        if dashboard.system_status == dashboard.models.SystemStatus.RUNNING:
            await dashboard.stop_trading_system()
    
    # Run the server
    config = uvicorn.Config(
        dashboard.app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Dashboard server stopped by user")
    except Exception as e:
        print(f"❌ Dashboard server error: {e}")
        sys.exit(1)
