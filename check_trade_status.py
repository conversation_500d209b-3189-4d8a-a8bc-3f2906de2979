#!/usr/bin/env python3
"""
Check Trade Status - Investigate approved trades and missing positions
"""

import asyncio
import sys
from datetime import datetime, timedelta

# Add project root to path
sys.path.append('.')

from api import TopStepClient


async def check_trade_status():
    """Check current orders, positions, and recent trades."""
    
    print("🔍 TRADE STATUS INVESTIGATION")
    print("=" * 60)
    print("Checking why approved trades aren't showing as positions")
    print("=" * 60)
    
    try:
        # Initialize TopStep client
        topstep_client = TopStepClient(preferred_account_type="PRACTICE")
        
        # Test authentication
        auth_success = await topstep_client.authenticate()
        if not auth_success:
            print("❌ TopStep API authentication failed")
            return
        
        print("✅ TopStep API authentication successful")
        print(f"   Account ID: {topstep_client.account_id}")
        
        # Check 1: Current Account Balance
        print("\n💰 ACCOUNT BALANCE:")
        print("-" * 30)
        balance = await topstep_client.get_account_balance()
        print(f"   Current Balance: ${balance:,.2f}")
        
        # Check 2: Current Open Positions
        print("\n📈 CURRENT OPEN POSITIONS:")
        print("-" * 30)
        positions = await topstep_client.get_positions(topstep_client.account_id)
        print(f"   Total Open Positions: {len(positions)}")
        
        if positions:
            for i, position in enumerate(positions, 1):
                print(f"\n   Position {i}:")
                print(f"     - ID: {position.id}")
                print(f"     - Symbol: {position.symbol}")
                print(f"     - Side: {position.side}")
                print(f"     - Size: {position.size}")
                print(f"     - Entry Price: ${position.entry_price:.2f}")
                print(f"     - Current Price: ${position.current_price:.2f}")
                print(f"     - Unrealized P&L: ${position.unrealized_pnl:.2f}")
        else:
            print("   ℹ️ No open positions found")
        
        # Check 3: Recent Orders (last 2 hours)
        print("\n📋 RECENT ORDERS (Last 2 Hours):")
        print("-" * 40)
        
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=2)
            
            data = {
                'accountId': topstep_client.account_id,
                'startDate': start_time.isoformat(),
                'endDate': end_time.isoformat()
            }
            response = await topstep_client._make_request('POST', '/api/Order/search', data)
            
            if response and response.get('success'):
                orders = response.get('orders', [])
                print(f"   Recent Orders Found: {len(orders)}")
                
                for i, order in enumerate(orders[:10], 1):  # Show last 10 orders
                    print(f"\n   Order {i}:")
                    print(f"     - ID: {order.get('id', 'N/A')}")
                    print(f"     - Symbol: {order.get('symbol', 'N/A')}")
                    print(f"     - Side: {order.get('side', 'N/A')}")
                    print(f"     - Size: {order.get('size', 'N/A')}")
                    print(f"     - Status: {order.get('status', 'N/A')}")
                    print(f"     - Type: {order.get('type', 'N/A')}")
                    print(f"     - Price: ${order.get('price', 0):.2f}")
                    print(f"     - Filled Size: {order.get('filledSize', 0)}")
                    print(f"     - Remaining Size: {order.get('remainingSize', 0)}")
                    print(f"     - Time: {order.get('timestamp', 'N/A')}")
                    
                    # Check order status
                    status = order.get('status', '').upper()
                    if status == 'FILLED':
                        print(f"     ✅ Order FILLED")
                    elif status == 'PENDING' or status == 'SUBMITTED':
                        print(f"     ⏳ Order PENDING")
                    elif status == 'CANCELLED':
                        print(f"     ❌ Order CANCELLED")
                    elif status == 'REJECTED':
                        print(f"     ❌ Order REJECTED")
            else:
                print("   ℹ️ No recent orders found or API error")
        except Exception as e:
            print(f"   ⚠️ Could not get order history: {e}")
        
        # Check 4: Recent Trades (last 2 hours)
        print("\n💼 RECENT TRADES (Last 2 Hours):")
        print("-" * 40)
        
        try:
            data = {
                'accountId': topstep_client.account_id,
                'startDate': start_time.isoformat(),
                'endDate': end_time.isoformat()
            }
            response = await topstep_client._make_request('POST', '/api/Trade/search', data)
            
            if response and response.get('success'):
                trades = response.get('trades', [])
                print(f"   Recent Trades Found: {len(trades)}")
                
                for i, trade in enumerate(trades[:10], 1):  # Show last 10 trades
                    print(f"\n   Trade {i}:")
                    print(f"     - ID: {trade.get('id', 'N/A')}")
                    print(f"     - Symbol: {trade.get('symbol', 'N/A')}")
                    print(f"     - Side: {trade.get('side', 'N/A')}")
                    print(f"     - Size: {trade.get('size', 'N/A')}")
                    print(f"     - Price: ${trade.get('price', 0):.2f}")
                    print(f"     - P&L: ${trade.get('pnl', 0):.2f}")
                    print(f"     - Time: {trade.get('timestamp', 'N/A')}")
            else:
                print("   ℹ️ No recent trades found or API error")
        except Exception as e:
            print(f"   ⚠️ Could not get trade history: {e}")
        
        # Check 5: Account Activity Summary
        print("\n📊 SUMMARY:")
        print("-" * 20)
        print(f"   Account Balance: ${balance:,.2f}")
        print(f"   Open Positions: {len(positions)}")
        
        if 'orders' in locals():
            filled_orders = sum(1 for o in orders if o.get('status', '').upper() == 'FILLED')
            pending_orders = sum(1 for o in orders if o.get('status', '').upper() in ['PENDING', 'SUBMITTED'])
            print(f"   Recent Orders: {len(orders)} (Filled: {filled_orders}, Pending: {pending_orders})")
        
        if 'trades' in locals():
            print(f"   Recent Trades: {len(trades)}")
        
        # Diagnosis
        print("\n🔍 DIAGNOSIS:")
        print("-" * 20)
        
        if len(positions) == 0 and 'orders' in locals() and len(orders) > 0:
            filled_orders = [o for o in orders if o.get('status', '').upper() == 'FILLED']
            pending_orders = [o for o in orders if o.get('status', '').upper() in ['PENDING', 'SUBMITTED']]
            
            if len(filled_orders) > 0:
                print("   🔄 Orders are being FILLED but positions may be closing quickly")
                print("   💡 This suggests scalping or quick profit-taking")
            elif len(pending_orders) > 0:
                print("   ⏳ Orders are PENDING - waiting for market execution")
                print("   💡 Orders may be limit orders waiting for price")
            else:
                print("   ❌ Orders may be getting REJECTED or CANCELLED")
                print("   💡 Check order rejection reasons")
        elif len(positions) > 0:
            print("   ✅ Positions are open and active")
        else:
            print("   ℹ️ No recent trading activity detected")
        
        return {
            "balance": balance,
            "positions": len(positions),
            "orders": len(orders) if 'orders' in locals() else 0,
            "trades": len(trades) if 'trades' in locals() else 0
        }
        
    except Exception as e:
        print(f"\n❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}
    
    finally:
        # Cleanup
        if 'topstep_client' in locals():
            await topstep_client.close()


if __name__ == "__main__":
    results = asyncio.run(check_trade_status())
    print(f"\n🎯 Investigation completed: {results}")
