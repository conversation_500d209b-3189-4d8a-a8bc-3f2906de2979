# Core Framework
langchain>=0.1.0
langgraph>=0.0.40
langchain-community>=0.0.20

# LLM Integration
openai>=1.0.0  # For Qwen API compatibility
anthropic>=0.8.0  # Alternative LLM support

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
ta-lib>=0.4.0
scikit-learn>=1.3.0

# API Integration
aiohttp>=3.8.0
websockets>=11.0.0
signalr-core>=0.9.0
requests>=2.31.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0  # PostgreSQL
redis>=4.5.0

# Async Support
asyncio>=3.4.3
asyncpg>=0.28.0

# Data Validation
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Configuration
python-dotenv>=1.0.0
pyyaml>=6.0

# Monitoring & Logging
prometheus-client>=0.17.0
structlog>=23.1.0
rich>=13.0.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.24.0  # For testing async HTTP

# Development Tools
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.3.0

# Visualization (Optional)
matplotlib>=3.7.0
plotly>=5.15.0
dash>=2.11.0

# Time Series Analysis
statsmodels>=0.14.0
arch>=5.5.0  # GARCH models

# Machine Learning (Optional)
xgboost>=1.7.0
lightgbm>=4.0.0

# Utilities
click>=8.1.0  # CLI interface
schedule>=1.2.0  # Task scheduling
python-dateutil>=2.8.0
pytz>=2023.3

# Security
cryptography>=41.0.0
keyring>=24.2.0

# Performance
uvloop>=0.17.0  # Fast event loop (Unix only)
orjson>=3.9.0  # Fast JSON parsing
