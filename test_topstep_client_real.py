#!/usr/bin/env python3
"""
REAL TOPSTEP API CLIENT TESTING
Test every single API call with real credentials.
"""

import os
import sys
import asyncio
import ssl
import aiohttp
from pathlib import Path
from datetime import datetime, timezone, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Your working TopStep client
class TopStepAPI:
    """Real TopStep API client - your working implementation."""
    
    def __init__(self, preferred_account_type: str = "PRACTICE"):
        self.base_url = os.getenv('TOPSTEP_GATEWAY_URL', 'https://api.topstepx.com')
        self.username = os.getenv('TOPSTEP_USERNAME')
        self.api_key = os.getenv('TOPSTEP_API_KEY')
        self.session_token = None
        self.account_id = None
        self.current_account = None
        self.available_accounts = []
        self.preferred_account_type = preferred_account_type
        self.authenticated = False
        self.session = None
        
        print(f"🔧 TopStep API initialized")
        print(f"   Base URL: {self.base_url}")
        print(f"   Username: {self.username}")
        print(f"   API Key: {self.api_key[:20]}..." if self.api_key else "None")
    
    async def __aenter__(self):
        """Async context manager entry"""
        if not self.session:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self.session = aiohttp.ClientSession(connector=connector)

        if not self.authenticated:
            await self.authenticate()

        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        pass

    async def close(self):
        """Explicitly close the session"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
    
    def _get_headers(self) -> dict:
        """Get headers with authentication token"""
        if not self.session_token:
            raise ValueError("Not authenticated - session token missing")

        return {
            'accept': 'text/plain',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.session_token}'
        }
    
    async def authenticate(self) -> bool:
        """Authenticate with TopStep API"""
        try:
            if not self.username or not self.api_key:
                print("❌ TopStep credentials not found in environment")
                return False

            url = f"{self.base_url}/api/Auth/loginKey"
            payload = {
                "userName": self.username,
                "apiKey": self.api_key
            }

            headers = {
                'accept': 'text/plain',
                'Content-Type': 'application/json'
            }
            
            print(f"🔐 Attempting authentication...")
            print(f"   URL: {url}")
            print(f"   Payload: {payload}")
            
            async with self.session.post(url, json=payload, headers=headers) as response:
                response_text = await response.text()
                print(f"   Response Status: {response.status}")
                print(f"   Response: {response_text}")
                
                if response.status == 200:
                    result = await response.json()
                    
                    if result.get('success') and result.get('errorCode') == 0:
                        self.session_token = result.get('token')
                        self.authenticated = True
                        print("✅ TopStep authentication successful")
                        print(f"   Token: {self.session_token[:30]}...")

                        # Get account information
                        if await self._get_account_info():
                            return True
                        else:
                            print("❌ Failed to retrieve account information")
                            self.authenticated = False
                            return False
                    else:
                        error_msg = result.get('errorMessage', 'Authentication failed')
                        error_code = result.get('errorCode', 'Unknown')
                        print(f"❌ Authentication failed: {error_msg} (Code: {error_code})")
                        return False
                else:
                    print(f"❌ Authentication failed with status {response.status}")
                    print(f"   Response: {response_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Error during authentication: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def _make_request(self, method: str, endpoint: str, data: dict = None) -> dict:
        """Make authenticated request to TopStep API"""
        try:
            if not self.session or self.session.closed:
                print("⚠️ Session not available, creating new session...")
                await self.__aenter__()

            url = f"{self.base_url}{endpoint}"
            headers = self._get_headers()

            print(f"📡 Making {method} request to {endpoint}")
            if data:
                print(f"   Data: {data}")

            async with self.session.request(method, url, json=data, headers=headers) as response:
                response_text = await response.text()
                print(f"   Response Status: {response.status}")
                print(f"   Response: {response_text[:500]}...")
                
                if response.status == 200:
                    return await response.json()
                elif response.status == 401:
                    print("⚠️ Session expired, attempting re-authentication...")
                    if await self.authenticate():
                        return await self._make_request(method, endpoint, data)
                else:
                    print(f"❌ API request failed with status {response.status}")
                    return None

        except Exception as e:
            print(f"❌ Error making API request: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def _get_account_info(self) -> bool:
        """Get account information and select preferred account"""
        try:
            print("📊 Getting account information...")
            data = {"onlyActiveAccounts": True}
            response = await self._make_request('POST', '/api/Account/search', data)
            
            if response and response.get('success'):
                accounts = response.get('accounts', [])
                self.available_accounts = accounts
                
                print(f"✅ Found {len(accounts)} accounts:")
                for i, account in enumerate(accounts):
                    print(f"   {i+1}. {account.get('name')} (ID: {account.get('id')}) - Balance: ${account.get('balance', 0)}")
                
                if accounts:
                    selected_account = self._select_account_by_type(accounts, self.preferred_account_type)
                    
                    if selected_account:
                        self.account_id = selected_account.get('id')
                        self.current_account = selected_account
                        account_type = "PRACTICE" if "PRACTICE" in selected_account.get('name', '') else "LIVE"
                        print(f"✅ Selected {account_type} account: {selected_account.get('name')} (ID: {self.account_id})")
                        return True
                    else:
                        print(f"❌ No {self.preferred_account_type} account found")
                        return False
                else:
                    print("❌ No active trading accounts found")
                    return False
            else:
                print("❌ Failed to retrieve account information")
                print(f"   Response: {response}")
                return False
                
        except Exception as e:
            print(f"❌ Error getting account info: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _select_account_by_type(self, accounts: list, preferred_type: str) -> dict:
        """Select account based on preferred type"""
        if preferred_type == "PRACTICE":
            for account in accounts:
                if "PRACTICE" in account.get('name', '').upper():
                    return account
        elif preferred_type == "COMBINE_50K":
            for account in accounts:
                if "$50K COMBINE" in account.get('name', ''):
                    return account
        elif preferred_type == "COMBINE_150K":
            for account in accounts:
                if "$150K COMBINE" in account.get('name', ''):
                    return account
        
        # Fallback to first available account
        return accounts[0] if accounts else None
    
    async def get_account_balance(self) -> float:
        """Get current account balance"""
        try:
            print("💰 Getting account balance...")
            if not self.authenticated or not self.account_id:
                print("❌ TopStep not authenticated or account ID not available")
                return None

            data = {"onlyActiveAccounts": True}
            response = await self._make_request('POST', '/api/Account/search', data)

            if response and response.get('success'):
                accounts = response.get('accounts', [])
                for account in accounts:
                    if account.get('id') == self.account_id:
                        balance = float(account.get('balance', 0.0))
                        print(f"✅ Account balance: ${balance}")
                        return balance

            print("❌ Failed to get account balance")
            return None

        except Exception as e:
            print(f"❌ Error getting account balance: {e}")
            return None
    
    async def get_positions(self) -> list:
        """Get current open positions"""
        try:
            print("📈 Getting open positions...")
            if not self.authenticated or not self.account_id:
                print("❌ TopStep not authenticated or account ID not available")
                return []

            data = {'accountId': self.account_id}
            response = await self._make_request('POST', '/api/Position/searchOpen', data)

            if response and response.get('success'):
                positions = response.get('positions', [])
                print(f"✅ Found {len(positions)} open positions:")
                for i, pos in enumerate(positions):
                    print(f"   {i+1}. {pos.get('contractId')} - Size: {pos.get('size')} - PnL: ${pos.get('unrealizedPnl', 0)}")
                return positions

            print("❌ Failed to get positions")
            return []

        except Exception as e:
            print(f"❌ Error getting positions: {e}")
            return []
    
    def get_available_contracts(self) -> list:
        """Get list of available contracts for trading (hardcoded mapping)"""
        print("📋 Getting available contracts (hardcoded mapping)...")

        contracts = [
            {"id": "CON.F.US.EP.U25", "symbol": "ES", "name": "E-mini S&P 500", "tickSize": 0.25, "multiplier": 50},
            {"id": "CON.F.US.ENQ.U25", "symbol": "NQ", "name": "E-mini NASDAQ-100", "tickSize": 0.25, "multiplier": 20},
            {"id": "CON.F.US.YM.U25", "symbol": "YM", "name": "Dow Jones E-mini", "tickSize": 1.0, "multiplier": 5},
            {"id": "CON.F.US.RTY.U25", "symbol": "RTY", "name": "E-mini Russell 2000", "tickSize": 0.1, "multiplier": 50},
            {"id": "CON.F.US.CL.U25", "symbol": "CL", "name": "Crude Oil", "tickSize": 0.01, "multiplier": 1000},
            {"id": "CON.F.US.GC.U25", "symbol": "GC", "name": "Gold", "tickSize": 0.1, "multiplier": 100},
            {"id": "CON.F.US.MES.U25", "symbol": "MES", "name": "Micro E-mini S&P 500", "tickSize": 0.25, "multiplier": 5},
            {"id": "CON.F.US.MNQ.U25", "symbol": "MNQ", "name": "Micro E-mini NASDAQ-100", "tickSize": 0.25, "multiplier": 2}
        ]

        print(f"✅ Found {len(contracts)} available contracts:")
        for i, contract in enumerate(contracts):
            print(f"   {i+1}. {contract['symbol']} ({contract['id']}) - {contract['name']}")

        return contracts
    
    async def get_orders(self, open_only: bool = False) -> list:
        """Get orders"""
        try:
            print(f"📋 Getting {'open' if open_only else 'all'} orders...")
            if not self.account_id:
                print("❌ Account ID not available")
                return []

            endpoint = '/api/Order/searchOpen' if open_only else '/api/Order/search'
            data = {'accountId': self.account_id}
            response = await self._make_request('POST', endpoint, data)

            if response and response.get('success'):
                orders = response.get('orders', [])
                print(f"✅ Found {len(orders)} orders:")
                for i, order in enumerate(orders):
                    print(f"   {i+1}. {order.get('contractId')} - {order.get('side')} {order.get('size')} - Status: {order.get('status')}")
                return orders

            print("❌ Failed to get orders")
            return []

        except Exception as e:
            print(f"❌ Error getting orders: {e}")
            return []


async def test_topstep_authentication():
    """Test TopStep authentication thoroughly."""
    print("🔍 Testing TopStep Authentication...")
    
    try:
        async with TopStepAPI() as client:
            # Authentication happens in __aenter__
            assert client.authenticated == True
            assert client.session_token is not None
            assert client.account_id is not None
            
            print("✅ Authentication test passed")
            return True
            
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_accounts():
    """Test TopStep account retrieval."""
    print("🔍 Testing TopStep Account Retrieval...")
    
    try:
        async with TopStepAPI() as client:
            balance = await client.get_account_balance()
            
            assert balance is not None
            assert balance >= 0
            assert len(client.available_accounts) > 0
            
            print("✅ Account retrieval test passed")
            return True
            
    except Exception as e:
        print(f"❌ Account retrieval test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_positions():
    """Test TopStep position retrieval."""
    print("🔍 Testing TopStep Position Retrieval...")
    
    try:
        async with TopStepAPI() as client:
            positions = await client.get_positions()
            
            # Positions can be empty, that's fine
            assert isinstance(positions, list)
            
            print("✅ Position retrieval test passed")
            return True
            
    except Exception as e:
        print(f"❌ Position retrieval test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_contracts():
    """Test TopStep contract retrieval."""
    print("🔍 Testing TopStep Contract Retrieval...")

    try:
        async with TopStepAPI() as client:
            contracts = client.get_available_contracts()  # No await needed - it's now synchronous

            assert isinstance(contracts, list)
            assert len(contracts) > 0

            # Check if ES contract exists
            es_found = any(contract.get('symbol') == 'ES' for contract in contracts)
            nq_found = any(contract.get('symbol') == 'NQ' for contract in contracts)

            print(f"   ES contract found: {es_found}")
            print(f"   NQ contract found: {nq_found}")

            # Verify contract structure
            es_contract = next((c for c in contracts if c.get('symbol') == 'ES'), None)
            if es_contract:
                print(f"   ES contract ID: {es_contract.get('id')}")
                print(f"   ES tick size: {es_contract.get('tickSize')}")
                print(f"   ES multiplier: {es_contract.get('multiplier')}")

            print("✅ Contract retrieval test passed")
            return True

    except Exception as e:
        print(f"❌ Contract retrieval test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_topstep_orders():
    """Test TopStep order retrieval."""
    print("🔍 Testing TopStep Order Retrieval...")
    
    try:
        async with TopStepAPI() as client:
            orders = await client.get_orders(open_only=True)
            
            # Orders can be empty, that's fine
            assert isinstance(orders, list)
            
            print("✅ Order retrieval test passed")
            return True
            
    except Exception as e:
        print(f"❌ Order retrieval test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all TopStep API client tests."""
    print("🚀 TESTING TOPSTEP API CLIENT WITH REAL CREDENTIALS")
    print("=" * 70)
    
    tests = [
        await test_topstep_authentication(),
        await test_topstep_accounts(),
        await test_topstep_positions(),
        await test_topstep_contracts(),
        await test_topstep_orders()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("=" * 70)
    if passed == total:
        print(f"🎉 ALL {total} TOPSTEP API TESTS PASSED! 100% SUCCESS!")
        print("✅ Authentication working with real credentials")
        print("✅ Account retrieval working")
        print("✅ Position retrieval working")
        print("✅ Contract retrieval working")
        print("✅ Order retrieval working")
        print("✅ TOPSTEP API CLIENT IS FULLY FUNCTIONAL!")
        print("=" * 70)
        return True
    else:
        print(f"❌ {total - passed} out of {total} API tests failed")
        print("❌ TopStep API client needs fixing")
        print("=" * 70)
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
