{"timestamp": "2025-07-31T09:38:46.219928+00:00", "summary": {"total_categories": 10, "passed_categories": 0, "success_rate": 0.0, "status": "❌ NOT READY"}, "results": {"Complete User Flow": {"success": false, "error": "No module named 'tests.test_authentication_flow'"}, "API Integration": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}, "Strategy Detection": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}, "Risk Management": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}, "LLM Integration": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}, "Workflow Orchestration": {"success": false, "error": "No module named 'langgraph'"}, "Performance & Scalability": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}, "Error Handling": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}, "Security & Validation": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}, "Production Readiness": {"success": false, "error": "17 validation errors for DatabaseConfig\nenvironment\n  Extra inputs are not permitted [type=extra_forbidden, input_value='development', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_username\n  Extra inputs are not permitted [type=extra_forbidden, input_value='mrrain', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_gateway_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://api.topstepx.com', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_websocket_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='wss://api.topstepx.com/ws', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\ntopstep_timeout\n  Extra inputs are not permitted [type=extra_forbidden, input_value='30', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_api_key\n  Extra inputs are not permitted [type=extra_forbidden, input_value='sk-be8c17f1999b46f380e1a1e2dc687b53', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_base_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='https://dashscope.aliyun....com/compatible-mode/v1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_model\n  Extra inputs are not permitted [type=extra_forbidden, input_value='qwen-turbo', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_max_tokens\n  Extra inputs are not permitted [type=extra_forbidden, input_value='2000', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nqwen_temperature\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0.1', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_url\n  Extra inputs are not permitted [type=extra_forbidden, input_value='redis://localhost:6379/0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nredis_db\n  Extra inputs are not permitted [type=extra_forbidden, input_value='0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_daily_loss\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1000.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_position_size\n  Extra inputs are not permitted [type=extra_forbidden, input_value='5', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_default_stop_loss_pct\n  Extra inputs are not permitted [type=extra_forbidden, input_value='1.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden\nrisk_max_portfolio_heat\n  Extra inputs are not permitted [type=extra_forbidden, input_value='10.0', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden"}}}