#!/usr/bin/env python3
"""
Simple test of created components without complex configuration.
"""

import os
import sys
import asyncio
import tempfile
import sqlite3
from pathlib import Path
from datetime import datetime, timezone

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_utility_functions():
    """Test utility functions directly."""
    print("🔍 Testing Utility Functions...")
    
    try:
        from core.utils import (
            round_price, calculate_position_size, calculate_kelly_fraction,
            moving_average, correlation
        )
        
        # Test price rounding
        assert round_price(4500.123, 0.25) == 4500.00
        assert round_price(4500.88, 0.25) == 4501.00
        
        # Test position sizing
        size = calculate_position_size(50000, 1.0, 4500, 4480, 20)
        assert size > 0
        
        # Test Kelly criterion
        kelly = calculate_kelly_fraction(0.6, 150, 100)
        assert 0 <= kelly <= 0.25
        
        # Test moving average
        prices = [4500, 4505, 4510, 4508, 4512]
        ma = moving_average(prices, 3)
        assert len(ma) == 3
        assert ma[0] == 4505.0  # (4500+4505+4510)/3
        
        # Test correlation
        x = [1, 2, 3, 4, 5]
        y = [2, 4, 6, 8, 10]
        corr = correlation(x, y)
        assert abs(corr - 1.0) < 1e-10  # Perfect correlation
        
        print("✅ Utility functions working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Utility functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_models():
    """Test database models directly."""
    print("🔍 Testing Database Models...")
    
    try:
        from core.database import Base, MarketData, TradingSignals, Orders
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create in-memory database
            engine = create_engine(f"sqlite:///{temp_dir}/test.db")
            Base.metadata.create_all(engine)
            
            Session = sessionmaker(bind=engine)
            session = Session()
            
            # Test MarketData model
            market_data = MarketData(
                contract_id="CON.F.US.EP.U25",
                timestamp=datetime.now(timezone.utc),
                open_price=4500.0,
                high_price=4505.0,
                low_price=4498.0,
                close_price=4502.0,
                volume=1000,
                timeframe="1m"
            )
            session.add(market_data)
            
            # Test TradingSignals model
            signal = TradingSignals(
                strategy_name="fvg",
                contract_id="CON.F.US.EP.U25",
                signal_type="BUY",
                confidence=0.75,
                price=4502.0
            )
            session.add(signal)
            
            # Test Orders model
            order = Orders(
                account_id=123,
                contract_id="CON.F.US.EP.U25",
                order_type="MARKET",
                side="BUY",
                size=1,
                status="PENDING"
            )
            session.add(order)
            
            session.commit()
            
            # Verify data
            assert session.query(MarketData).count() == 1
            assert session.query(TradingSignals).count() == 1
            assert session.query(Orders).count() == 1
            
            session.close()
        
        print("✅ Database models working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Database models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_models():
    """Test API models directly."""
    print("🔍 Testing API Models...")
    
    try:
        from api.models import (
            MarketDataBar, Order, OrderRequest, OrderType, OrderSide, OrderStatus
        )
        
        # Test MarketDataBar
        bar_data = {
            "t": "2024-01-01T10:00:00Z",
            "o": 4500.0,
            "h": 4505.0,
            "l": 4498.0,
            "c": 4502.0,
            "v": 1000
        }
        bar = MarketDataBar(**bar_data)
        assert bar.open_price == 4500.0
        assert bar.volume == 1000
        
        # Test Order
        order_data = {
            "id": 123,
            "account_id": 456,
            "contract_id": "CON.F.US.EP.U25",
            "creation_timestamp": datetime.now(timezone.utc),
            "status": OrderStatus.OPEN,
            "type": OrderType.MARKET,
            "side": OrderSide.BID,
            "size": 1
        }
        order = Order(**order_data)
        assert order.id == 123
        assert order.order_type == OrderType.MARKET
        
        # Test OrderRequest
        request_data = {
            "account_id": 456,
            "contract_id": "CON.F.US.EP.U25",
            "type": OrderType.MARKET,
            "side": OrderSide.BID,
            "size": 1
        }
        order_request = OrderRequest(**request_data)
        assert order_request.account_id == 456
        
        print("✅ API models working correctly")
        return True
        
    except Exception as e:
        print(f"❌ API models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_llm_models():
    """Test LLM models directly."""
    print("🔍 Testing LLM Models...")
    
    try:
        from llm.models import (
            TradingDecision, MarketAnalysis, LLMResponse, LLMPrompt,
            TradingAction, MarketSentiment
        )
        
        # Test LLMResponse
        response = LLMResponse(
            content="This is a test response",
            usage_tokens=50,
            model="qwen-turbo"
        )
        assert response.content == "This is a test response"
        assert response.usage_tokens == 50
        
        # Test TradingDecision
        decision = TradingDecision(
            action=TradingAction.BUY,
            confidence=0.85,
            reasoning="Strong bullish signals detected",
            entry_price=4500.0,
            stop_loss=4480.0,
            take_profit=4520.0
        )
        assert decision.action == TradingAction.BUY
        assert decision.confidence == 0.85
        
        # Test MarketAnalysis
        analysis = MarketAnalysis(
            symbol="ES",
            sentiment=MarketSentiment.BULLISH,
            trend_direction="UP",
            confidence=0.8,
            analysis_text="Market showing strong bullish momentum"
        )
        assert analysis.symbol == "ES"
        assert analysis.sentiment == MarketSentiment.BULLISH
        
        # Test LLMPrompt
        prompt = LLMPrompt(
            system_prompt="You are a trading assistant",
            user_prompt="Analyze this market data",
            max_tokens=1000,
            temperature=0.1
        )
        assert prompt.system_prompt == "You are a trading assistant"
        assert prompt.max_tokens == 1000
        
        print("✅ LLM models working correctly")
        return True
        
    except Exception as e:
        print(f"❌ LLM models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_trading_calculations():
    """Test real trading calculations."""
    print("🔍 Testing Trading Calculations...")
    
    try:
        from core.utils import round_price, calculate_position_size, calculate_kelly_fraction
        
        # Real ES futures scenario
        account_balance = 50000.0
        risk_percentage = 1.0  # 1% risk
        entry_price = 4500.0
        stop_loss = 4480.0  # 20 point stop
        es_multiplier = 50.0  # ES multiplier
        
        # Calculate position size
        position_size = calculate_position_size(
            account_balance, risk_percentage, entry_price, stop_loss, es_multiplier
        )
        
        # Expected: $500 risk / ($20 * $50) = $500 / $1000 = 0.5 -> 0 contracts (rounded down)
        # Let's use a smaller stop for a realistic test
        stop_loss_small = 4490.0  # 10 point stop
        position_size_small = calculate_position_size(
            account_balance, risk_percentage, entry_price, stop_loss_small, es_multiplier
        )
        
        # Expected: $500 risk / ($10 * $50) = $500 / $500 = 1 contract
        assert position_size_small == 1
        
        # Test price rounding for ES (0.25 tick size)
        rounded_entry = round_price(4500.13, 0.25)
        assert rounded_entry == 4500.00
        
        rounded_entry2 = round_price(4500.88, 0.25)
        assert rounded_entry2 == 4501.00
        
        # Test Kelly criterion with realistic trading stats
        kelly = calculate_kelly_fraction(0.55, 200, 100)  # 55% win rate, 2:1 RR
        assert 0 < kelly <= 0.25
        
        print("✅ Trading calculations working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Trading calculations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_technical_indicators():
    """Test technical indicator calculations."""
    print("🔍 Testing Technical Indicators...")
    
    try:
        from core.utils import moving_average, exponential_moving_average, standard_deviation
        
        # Real ES price data
        prices = [4500, 4505, 4510, 4508, 4512, 4515, 4518, 4520, 4522, 4525]
        
        # Test Simple Moving Average
        sma_5 = moving_average(prices, 5)
        assert len(sma_5) == 6  # 10 - 5 + 1
        assert sma_5[0] == 4507.0  # (4500+4505+4510+4508+4512)/5
        
        # Test Exponential Moving Average
        ema_5 = exponential_moving_average(prices, 0.4)  # Alpha = 0.4
        assert len(ema_5) == len(prices)
        assert ema_5[0] == prices[0]  # First value equals first price
        
        # Test Standard Deviation
        std_dev = standard_deviation(prices)
        assert std_dev > 0
        
        print("✅ Technical indicators working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Technical indicators test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_data_structures():
    """Test data structure handling."""
    print("🔍 Testing Data Structures...")
    
    try:
        import json
        from datetime import datetime, timezone
        
        # Test market data structure
        market_data = {
            'symbol': 'ES',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'open': 4500.0,
            'high': 4505.0,
            'low': 4498.0,
            'close': 4502.0,
            'volume': 1000
        }
        
        # Test JSON serialization
        json_str = json.dumps(market_data, default=str)
        parsed_data = json.loads(json_str)
        assert parsed_data['symbol'] == 'ES'
        assert parsed_data['volume'] == 1000
        
        # Test trading signal structure
        signal = {
            'strategy': 'fvg',
            'symbol': 'ES',
            'signal_type': 'BUY',
            'confidence': 0.85,
            'entry_price': 4502.0,
            'stop_loss': 4490.0,
            'take_profit': 4520.0,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        # Validate signal structure
        assert 0 <= signal['confidence'] <= 1
        assert signal['signal_type'] in ['BUY', 'SELL']
        assert signal['entry_price'] > signal['stop_loss']
        assert signal['take_profit'] > signal['entry_price']
        
        print("✅ Data structures working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Data structures test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all simple component tests."""
    print("🚀 TESTING CREATED COMPONENTS (SIMPLIFIED)")
    print("=" * 60)
    
    tests = [
        await test_utility_functions(),
        await test_database_models(),
        await test_api_models(),
        await test_llm_models(),
        await test_trading_calculations(),
        await test_technical_indicators(),
        await test_data_structures()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("=" * 60)
    if passed == total:
        print(f"🎉 ALL {total} COMPONENT TESTS PASSED! 100% SUCCESS!")
        print("✅ Utility functions working with real trading math")
        print("✅ Database models working with real data structures")
        print("✅ API models working with real TopStep data formats")
        print("✅ LLM models working with real decision structures")
        print("✅ Trading calculations working with real scenarios")
        print("✅ Technical indicators working with real price data")
        print("✅ Data structures working with real market data")
        print("✅ ALL CREATED COMPONENTS ARE FULLY FUNCTIONAL!")
        print("=" * 60)
        return True
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("❌ Some components need fixing")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
