[Unit]
Description=Algorithmic Trading System
After=network.target
Wants=network.target

[Service]
Type=simple
User=trader
Group=trader
WorkingDirectory=/Users/<USER>/Documents/augment-projects/trade_agent
Environment=PYTHONPATH=/Users/<USER>/Documents/augment-projects/trade_agent
ExecStart=/usr/bin/python3 /Users/<USER>/Documents/augment-projects/trade_agent/main_trading_system.py --mode=production
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
