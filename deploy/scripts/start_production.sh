#!/bin/bash
# Production Trading System Startup Script

set -e

echo "🚀 Starting Trading System in Production Mode"
echo "=============================================="

# Set environment
export ENVIRONMENT=production
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Load production environment
if [ -f "deploy/configs/.env.production" ]; then
    echo "📋 Loading production configuration..."
    export $(cat deploy/configs/.env.production | grep -v '^#' | xargs)
else
    echo "❌ Production environment file not found!"
    exit 1
fi

# Create logs directory
mkdir -p logs

# Start the trading system
echo "🔄 Starting trading workflow..."
python3 -m main_trading_system --mode=production --log-level=INFO

echo "✅ Trading system started successfully"
