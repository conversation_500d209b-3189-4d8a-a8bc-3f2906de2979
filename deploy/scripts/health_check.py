#!/usr/bin/env python3
"""
Production Monitoring and Health Check Script.
"""

import asyncio
import sys
import json
from datetime import datetime, timezone
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

async def health_check():
    """Perform comprehensive health check."""
    
    print("🏥 TRADING SYSTEM HEALTH CHECK")
    print("=" * 50)
    
    health_status = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "overall_status": "HEALTHY",
        "components": {}
    }
    
    try:
        # Check TopStep API
        print("\n📡 Checking TopStep API...")
        from api import TopStepClient
        
        async with TopStepClient() as client:
            balance = await client.get_account_balance()
            positions = await client.get_positions()
            
            health_status["components"]["topstep"] = {
                "status": "HEALTHY",
                "account_balance": balance,
                "open_positions": len(positions),
                "last_check": datetime.now(timezone.utc).isoformat()
            }
            print(f"   ✅ TopStep API: Connected (Balance: ${balance:,.2f})")
        
        # Check Database
        print("\n🗄️ Checking Database...")
        from core import get_db_session
        
        async with get_db_session() as session:
            # Simple query to test connection
            result = await session.execute("SELECT 1")
            health_status["components"]["database"] = {
                "status": "HEALTHY",
                "last_check": datetime.now(timezone.utc).isoformat()
            }
            print("   ✅ Database: Connected")
        
        # Check LLM
        print("\n🧠 Checking Qwen LLM...")
        from llm import QwenClient
        
        qwen_client = QwenClient()
        response = await qwen_client.generate_response("Health check test")
        
        health_status["components"]["llm"] = {
            "status": "HEALTHY" if response else "WARNING",
            "last_check": datetime.now(timezone.utc).isoformat()
        }
        print(f"   ✅ Qwen LLM: {'Connected' if response else 'Warning'}")
        
        print("\n" + "=" * 50)
        print("🎉 SYSTEM HEALTH: ALL COMPONENTS OPERATIONAL")
        
        return health_status
        
    except Exception as e:
        print(f"\n❌ HEALTH CHECK FAILED: {e}")
        health_status["overall_status"] = "UNHEALTHY"
        health_status["error"] = str(e)
        return health_status

async def main():
    """Run health check."""
    health_status = await health_check()
    
    # Save health report
    health_file = Path("logs/health_check.json")
    with open(health_file, 'w') as f:
        json.dump(health_status, f, indent=2)
    
    print(f"\n📋 Health report saved: {health_file}")
    
    # Exit with appropriate code
    sys.exit(0 if health_status["overall_status"] == "HEALTHY" else 1)

if __name__ == "__main__":
    asyncio.run(main())
