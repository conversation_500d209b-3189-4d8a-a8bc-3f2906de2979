#!/usr/bin/env python3
"""
Production Setup and Deployment Script.
Sets up the trading system for live production use.
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime, timezone

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class ProductionSetup:
    """Production setup and deployment manager."""
    
    def __init__(self):
        self.project_root = project_root
        self.deploy_dir = self.project_root / "deploy"
        self.logs_dir = self.project_root / "logs"
        self.data_dir = self.project_root / "data"
        
    def create_directories(self):
        """Create necessary directories for production."""
        print("📁 Creating production directories...")
        
        directories = [
            self.logs_dir,
            self.data_dir,
            self.data_dir / "backups",
            self.data_dir / "exports",
            self.deploy_dir / "scripts",
            self.deploy_dir / "configs"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ {directory}")
    
    def create_production_env(self):
        """Create production environment file."""
        print("⚙️ Creating production environment configuration...")
        
        prod_env_content = """# Production Environment Configuration
ENVIRONMENT=production

# TopStep API Configuration
TOPSTEP_USERNAME=mrrain
TOPSTEP_API_KEY=FaZ4ZhFiqGQwZHVbre0NrAJIAKokYlOfY2GrND4aqWs=
TOPSTEP_GATEWAY_URL=https://api.topstepx.com
TOPSTEP_WEBSOCKET_URL=wss://api.topstepx.com/ws
TOPSTEP_TIMEOUT=30

# Qwen LLM Configuration
QWEN_API_KEY=sk-be8c17f1999b46f380e1a1e2dc687b53
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
QWEN_MODEL=qwen-turbo
QWEN_MAX_TOKENS=2000
QWEN_TEMPERATURE=0.1

# Database Configuration
DB_URL=sqlite:///./data/trading_production.db
DB_ECHO=false
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# Risk Management Configuration
RISK_MAX_DAILY_LOSS=1000.0
RISK_MAX_POSITION_SIZE=3
RISK_DEFAULT_STOP_LOSS_PCT=1.0
RISK_MAX_PORTFOLIO_HEAT=8.0
"""
        
        prod_env_path = self.deploy_dir / "configs" / ".env.production"
        with open(prod_env_path, 'w') as f:
            f.write(prod_env_content)
        
        print(f"   ✅ Production environment: {prod_env_path}")
    
    def create_startup_script(self):
        """Create production startup script."""
        print("🚀 Creating startup script...")
        
        startup_script = """#!/bin/bash
# Production Trading System Startup Script

set -e

echo "🚀 Starting Trading System in Production Mode"
echo "=============================================="

# Set environment
export ENVIRONMENT=production
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Load production environment
if [ -f "deploy/configs/.env.production" ]; then
    echo "📋 Loading production configuration..."
    export $(cat deploy/configs/.env.production | grep -v '^#' | xargs)
else
    echo "❌ Production environment file not found!"
    exit 1
fi

# Create logs directory
mkdir -p logs

# Start the trading system
echo "🔄 Starting trading workflow..."
python3 -m main_trading_system --mode=production --log-level=INFO

echo "✅ Trading system started successfully"
"""
        
        startup_path = self.deploy_dir / "scripts" / "start_production.sh"
        with open(startup_path, 'w') as f:
            f.write(startup_script)
        
        # Make executable
        os.chmod(startup_path, 0o755)
        print(f"   ✅ Startup script: {startup_path}")
    
    def create_monitoring_script(self):
        """Create monitoring and health check script."""
        print("📊 Creating monitoring script...")
        
        monitoring_script = """#!/usr/bin/env python3
\"\"\"
Production Monitoring and Health Check Script.
\"\"\"

import asyncio
import sys
import json
from datetime import datetime, timezone
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

async def health_check():
    \"\"\"Perform comprehensive health check.\"\"\"
    
    print("🏥 TRADING SYSTEM HEALTH CHECK")
    print("=" * 50)
    
    health_status = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "overall_status": "HEALTHY",
        "components": {}
    }
    
    try:
        # Check TopStep API
        print("\\n📡 Checking TopStep API...")
        from api import TopStepClient
        
        async with TopStepClient() as client:
            balance = await client.get_account_balance()
            positions = await client.get_positions()
            
            health_status["components"]["topstep"] = {
                "status": "HEALTHY",
                "account_balance": balance,
                "open_positions": len(positions),
                "last_check": datetime.now(timezone.utc).isoformat()
            }
            print(f"   ✅ TopStep API: Connected (Balance: ${balance:,.2f})")
        
        # Check Database
        print("\\n🗄️ Checking Database...")
        from core import get_db_session
        
        async with get_db_session() as session:
            # Simple query to test connection
            result = await session.execute("SELECT 1")
            health_status["components"]["database"] = {
                "status": "HEALTHY",
                "last_check": datetime.now(timezone.utc).isoformat()
            }
            print("   ✅ Database: Connected")
        
        # Check LLM
        print("\\n🧠 Checking Qwen LLM...")
        from llm import QwenClient
        
        qwen_client = QwenClient()
        response = await qwen_client.generate_response("Health check test")
        
        health_status["components"]["llm"] = {
            "status": "HEALTHY" if response else "WARNING",
            "last_check": datetime.now(timezone.utc).isoformat()
        }
        print(f"   ✅ Qwen LLM: {'Connected' if response else 'Warning'}")
        
        print("\\n" + "=" * 50)
        print("🎉 SYSTEM HEALTH: ALL COMPONENTS OPERATIONAL")
        
        return health_status
        
    except Exception as e:
        print(f"\\n❌ HEALTH CHECK FAILED: {e}")
        health_status["overall_status"] = "UNHEALTHY"
        health_status["error"] = str(e)
        return health_status

async def main():
    \"\"\"Run health check.\"\"\"
    health_status = await health_check()
    
    # Save health report
    health_file = Path("logs/health_check.json")
    with open(health_file, 'w') as f:
        json.dump(health_status, f, indent=2)
    
    print(f"\\n📋 Health report saved: {health_file}")
    
    # Exit with appropriate code
    sys.exit(0 if health_status["overall_status"] == "HEALTHY" else 1)

if __name__ == "__main__":
    asyncio.run(main())
"""
        
        monitoring_path = self.deploy_dir / "scripts" / "health_check.py"
        with open(monitoring_path, 'w') as f:
            f.write(monitoring_script)
        
        os.chmod(monitoring_path, 0o755)
        print(f"   ✅ Monitoring script: {monitoring_path}")
    
    def create_main_trading_system(self):
        """Create main trading system entry point."""
        print("🎯 Creating main trading system...")
        
        main_system = """#!/usr/bin/env python3
\"\"\"
Main Trading System Entry Point.
Production-ready trading system with full workflow.
\"\"\"

import os
import sys
import asyncio
import argparse
import signal
from pathlib import Path
from datetime import datetime, timezone

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

class TradingSystemManager:
    \"\"\"Main trading system manager.\"\"\"
    
    def __init__(self, mode="development"):
        self.mode = mode
        self.running = False
        self.workflow = None
        
    async def initialize(self):
        \"\"\"Initialize the trading system.\"\"\"
        print(f"🚀 Initializing Trading System ({self.mode} mode)")
        print("=" * 60)
        
        # Initialize core systems
        from core import configure_logging, init_database, get_logger
        
        configure_logging()
        self.logger = get_logger("TradingSystem")
        
        await init_database()
        self.logger.info(f"Trading system initialized in {self.mode} mode")
        
        # Initialize API clients
        from api import TopStepClient
        from llm import QwenClient
        
        self.topstep_client = TopStepClient(preferred_account_type="PRACTICE")
        self.qwen_client = QwenClient()
        
        # Initialize workflow
        from workflow import TradingWorkflow
        
        self.workflow = TradingWorkflow(
            topstep_client=self.topstep_client,
            qwen_client=self.qwen_client
        )
        
        print("✅ Trading system initialized successfully")
    
    async def run_trading_loop(self):
        \"\"\"Run the main trading loop.\"\"\"
        self.running = True
        cycle_count = 0
        
        print("🔄 Starting trading loop...")
        
        try:
            while self.running:
                cycle_count += 1
                self.logger.info(f"Starting trading cycle {cycle_count}")
                
                # Run trading workflow
                result = await self.workflow.run_trading_cycle(
                    symbol="ES"  # Start with ES futures
                )
                
                if result["success"]:
                    self.logger.info(
                        f"Trading cycle {cycle_count} completed",
                        should_trade=result["should_trade"],
                        signals=result["signals_detected"]
                    )
                else:
                    self.logger.error(
                        f"Trading cycle {cycle_count} failed",
                        error=result.get("error_message")
                    )
                
                # Wait before next cycle (60 seconds)
                if self.running:
                    await asyncio.sleep(60)
                
        except KeyboardInterrupt:
            print("\\n🛑 Shutdown signal received")
        except Exception as e:
            self.logger.error(f"Trading loop error: {e}", exc_info=True)
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        \"\"\"Shutdown the trading system gracefully.\"\"\"
        print("🛑 Shutting down trading system...")
        self.running = False
        
        if self.topstep_client:
            await self.topstep_client.close()
        
        print("✅ Trading system shutdown complete")
    
    def signal_handler(self, signum, frame):
        \"\"\"Handle shutdown signals.\"\"\"
        print(f"\\n🛑 Received signal {signum}")
        self.running = False

async def main():
    \"\"\"Main entry point.\"\"\"
    parser = argparse.ArgumentParser(description="Trading System")
    parser.add_argument("--mode", choices=["development", "production"], 
                       default="development", help="Running mode")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="Log level")
    
    args = parser.parse_args()
    
    # Set log level
    os.environ["LOG_LEVEL"] = args.log_level
    
    # Create and run trading system
    system = TradingSystemManager(mode=args.mode)
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, system.signal_handler)
    signal.signal(signal.SIGTERM, system.signal_handler)
    
    try:
        await system.initialize()
        await system.run_trading_loop()
    except Exception as e:
        print(f"❌ System error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
"""
        
        main_path = self.project_root / "main_trading_system.py"
        with open(main_path, 'w') as f:
            f.write(main_system)
        
        os.chmod(main_path, 0o755)
        print(f"   ✅ Main trading system: {main_path}")
    
    def create_systemd_service(self):
        """Create systemd service file for production deployment."""
        print("🔧 Creating systemd service...")
        
        service_content = f"""[Unit]
Description=Algorithmic Trading System
After=network.target
Wants=network.target

[Service]
Type=simple
User=trader
Group=trader
WorkingDirectory={self.project_root}
Environment=PYTHONPATH={self.project_root}
ExecStart=/usr/bin/python3 {self.project_root}/main_trading_system.py --mode=production
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
        
        service_path = self.deploy_dir / "configs" / "trading-system.service"
        with open(service_path, 'w') as f:
            f.write(service_content)
        
        print(f"   ✅ Systemd service: {service_path}")
        print("   📋 To install: sudo cp deploy/configs/trading-system.service /etc/systemd/system/")
        print("   📋 To enable: sudo systemctl enable trading-system")
        print("   📋 To start: sudo systemctl start trading-system")
    
    def run_setup(self):
        """Run complete production setup."""
        print("🏗️ PRODUCTION SETUP STARTING")
        print("=" * 60)
        
        self.create_directories()
        self.create_production_env()
        self.create_startup_script()
        self.create_monitoring_script()
        self.create_main_trading_system()
        self.create_systemd_service()
        
        print("\n" + "=" * 60)
        print("🎉 PRODUCTION SETUP COMPLETE!")
        print("=" * 60)
        
        print("\n📋 DEPLOYMENT CHECKLIST:")
        print("   ✅ Production directories created")
        print("   ✅ Environment configuration ready")
        print("   ✅ Startup scripts created")
        print("   ✅ Monitoring system ready")
        print("   ✅ Main trading system ready")
        print("   ✅ Systemd service configured")
        
        print("\n🚀 NEXT STEPS:")
        print("   1. Review production configuration in deploy/configs/")
        print("   2. Test with: ./deploy/scripts/start_production.sh")
        print("   3. Monitor with: python3 deploy/scripts/health_check.py")
        print("   4. Deploy systemd service for auto-start")
        print("   5. Set up log rotation and monitoring")
        
        print("\n⚠️ IMPORTANT:")
        print("   - Start with PRACTICE account for testing")
        print("   - Monitor system closely for first 24 hours")
        print("   - Set up alerts for system failures")
        print("   - Regular backup of trading database")

def main():
    """Run production setup."""
    setup = ProductionSetup()
    setup.run_setup()

if __name__ == "__main__":
    main()
