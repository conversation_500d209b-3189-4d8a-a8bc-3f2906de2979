"""
Tests for core functionality.
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import patch

from core.config import Settings, TopStepEnvironment, LogLevel
from core.logging import configure_logging, get_logger
from core.database import DatabaseManager, init_database
from core.utils import (
    round_price, calculate_position_size, calculate_kelly_fraction,
    normalize_contract_id, parse_timeframe, generate_order_tag,
    moving_average, correlation, RateLimiter
)


class TestConfig:
    """Test configuration management."""
    
    def test_default_settings(self):
        """Test default settings creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {
                'TOPSTEP_USERNAME': 'test_user',
                'TOPSTEP_API_KEY': 'test_key',
                'QWEN_API_KEY': 'test_qwen_key'
            }):
                settings = Settings()
                
                assert settings.app_name == "Trading Agent"
                assert settings.version == "1.0.0"
                assert settings.topstep.username == "test_user"
                assert settings.topstep.api_key.get_secret_value() == "test_key"
                assert settings.qwen.api_key.get_secret_value() == "test_qwen_key"
    
    def test_topstep_urls(self):
        """Test TopStep URL generation."""
        with patch.dict(os.environ, {
            'TOPSTEP_USERNAME': 'test_user',
            'TOPSTEP_API_KEY': 'test_key',
            'QWEN_API_KEY': 'test_qwen_key'
        }):
            settings = Settings()
            
            # Test demo environment
            settings.topstep.environment = TopStepEnvironment.DEMO
            assert "demo" in settings.topstep.base_url
            assert "demo" in settings.topstep.websocket_url
            
            # Test live environment
            settings.topstep.environment = TopStepEnvironment.LIVE
            assert "live" in settings.topstep.base_url
            assert "live" in settings.topstep.websocket_url
    
    def test_risk_config_validation(self):
        """Test risk configuration validation."""
        with patch.dict(os.environ, {
            'TOPSTEP_USERNAME': 'test_user',
            'TOPSTEP_API_KEY': 'test_key',
            'QWEN_API_KEY': 'test_qwen_key',
            'RISK_DEFAULT_STOP_LOSS_PCT': '150'  # Invalid percentage
        }):
            with pytest.raises(ValueError):
                Settings()


class TestLogging:
    """Test logging functionality."""
    
    def test_logger_creation(self):
        """Test logger creation."""
        logger = get_logger("test_logger")
        assert logger is not None
        assert logger.name == "test_logger"
    
    def test_configure_logging(self):
        """Test logging configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {
                'TOPSTEP_USERNAME': 'test_user',
                'TOPSTEP_API_KEY': 'test_key',
                'QWEN_API_KEY': 'test_qwen_key',
                'LOGS_DIR': temp_dir
            }):
                configure_logging()
                logger = get_logger("test")
                logger.info("Test log message")
                
                # Check if log files are created
                logs_path = Path(temp_dir)
                assert any(logs_path.glob("*.log"))


class TestDatabase:
    """Test database functionality."""
    
    @pytest.mark.asyncio
    async def test_database_initialization(self):
        """Test database initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = Path(temp_dir) / "test.db"
            
            with patch.dict(os.environ, {
                'TOPSTEP_USERNAME': 'test_user',
                'TOPSTEP_API_KEY': 'test_key',
                'QWEN_API_KEY': 'test_qwen_key',
                'DB_URL': f'sqlite:///{db_path}'
            }):
                db_manager = DatabaseManager()
                db_manager.initialize()
                await db_manager.create_tables_async()
                
                assert db_path.exists()
                await db_manager.close()
    
    @pytest.mark.asyncio
    async def test_database_session(self):
        """Test database session management."""
        with tempfile.TemporaryDirectory() as temp_dir:
            db_path = Path(temp_dir) / "test.db"
            
            with patch.dict(os.environ, {
                'TOPSTEP_USERNAME': 'test_user',
                'TOPSTEP_API_KEY': 'test_key',
                'QWEN_API_KEY': 'test_qwen_key',
                'DB_URL': f'sqlite:///{db_path}'
            }):
                db_manager = DatabaseManager()
                db_manager.initialize()
                await db_manager.create_tables_async()
                
                async with db_manager.get_async_session() as session:
                    assert session is not None
                
                await db_manager.close()


class TestUtils:
    """Test utility functions."""
    
    def test_round_price(self):
        """Test price rounding."""
        assert round_price(100.123, 0.01) == 100.12
        assert round_price(100.126, 0.01) == 100.13
        assert round_price(100.5, 0.25) == 100.5
        assert round_price(100.6, 0.25) == 100.75
        
        with pytest.raises(ValueError):
            round_price(100.0, 0.0)
    
    def test_calculate_position_size(self):
        """Test position size calculation."""
        # Test normal case
        size = calculate_position_size(
            account_balance=10000,
            risk_percentage=2.0,
            entry_price=100.0,
            stop_loss_price=98.0,
            contract_multiplier=1.0
        )
        assert size == 100  # $200 risk / $2 per contract
        
        # Test edge cases
        with pytest.raises(ValueError):
            calculate_position_size(10000, 0, 100, 98)  # Invalid risk percentage
        
        with pytest.raises(ValueError):
            calculate_position_size(10000, 2, 0, 98)  # Invalid entry price
        
        with pytest.raises(ValueError):
            calculate_position_size(10000, 2, 100, 100)  # Same entry and stop price
    
    def test_calculate_kelly_fraction(self):
        """Test Kelly criterion calculation."""
        kelly = calculate_kelly_fraction(0.6, 150, 100)
        assert 0 < kelly <= 0.25  # Should be capped at 0.25
        
        # Test edge cases
        with pytest.raises(ValueError):
            calculate_kelly_fraction(1.5, 150, 100)  # Invalid win rate
        
        with pytest.raises(ValueError):
            calculate_kelly_fraction(0.6, -150, 100)  # Invalid avg win
    
    def test_normalize_contract_id(self):
        """Test contract ID normalization."""
        assert normalize_contract_id("con.f.us.ep.u25") == "CON.F.US.EP.U25"
        assert normalize_contract_id("  CON.F.US.EP.U25  ") == "CON.F.US.EP.U25"
    
    def test_parse_timeframe(self):
        """Test timeframe parsing."""
        assert parse_timeframe("1m") == 1
        assert parse_timeframe("5m") == 5
        assert parse_timeframe("1h") == 60
        assert parse_timeframe("1d") == 1440
        assert parse_timeframe("1w") == 10080
        assert parse_timeframe("60") == 60  # Already in minutes
    
    def test_generate_order_tag(self):
        """Test order tag generation."""
        tag1 = generate_order_tag("fvg", "buy")
        tag2 = generate_order_tag("fvg", "buy")
        
        assert tag1.startswith("fvg_")
        assert tag2.startswith("fvg_")
        assert tag1 != tag2  # Should be unique
    
    def test_moving_average(self):
        """Test moving average calculation."""
        values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        ma = moving_average(values, 3)
        
        assert len(ma) == 8  # 10 - 3 + 1
        assert ma[0] == 2.0  # (1+2+3)/3
        assert ma[-1] == 9.0  # (8+9+10)/3
    
    def test_correlation(self):
        """Test correlation calculation."""
        x = [1, 2, 3, 4, 5]
        y = [2, 4, 6, 8, 10]  # Perfect positive correlation
        
        corr = correlation(x, y)
        assert abs(corr - 1.0) < 1e-10  # Should be 1.0
        
        # Test with no correlation
        y_random = [5, 2, 8, 1, 9]
        corr_random = correlation(x, y_random)
        assert -1 <= corr_random <= 1
    
    @pytest.mark.asyncio
    async def test_rate_limiter(self):
        """Test rate limiter functionality."""
        limiter = RateLimiter(max_calls=2, time_window=1.0)
        
        # First two calls should succeed
        assert await limiter.acquire() == True
        assert await limiter.acquire() == True
        
        # Third call should fail
        assert await limiter.acquire() == False
        
        # Wait and try again
        await asyncio.sleep(1.1)
        assert await limiter.acquire() == True


if __name__ == "__main__":
    pytest.main([__file__])
