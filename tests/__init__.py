"""
Comprehensive test suite for the agentic trading system.
Tests complete user flow from authentication to execution.
"""

from .test_complete_user_flow import CompleteUserFlowTest
from .test_authentication_flow import AuthenticationFlowTest
from .test_market_data_flow import MarketDataFlowTest
from .test_strategy_detection_flow import StrategyDetectionFlowTest
from .test_risk_management_flow import RiskManagementFlowTest
from .test_decision_making_flow import DecisionMakingFlowTest
from .test_execution_flow import ExecutionFlowTest
from .test_monitoring_flow import MonitoringFlowTest

__all__ = [
    "CompleteUserFlowTest",
    "AuthenticationFlowTest", 
    "MarketDataFlowTest",
    "StrategyDetectionFlowTest",
    "RiskManagementFlowTest",
    "DecisionMakingFlowTest",
    "ExecutionFlowTest",
    "MonitoringFlowTest"
]
