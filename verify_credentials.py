#!/usr/bin/env python3
"""
Verify API credentials and endpoints.
"""

import os
import asyncio
import ssl
import aiohttp
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

TOPSTEP_USERNAME = os.getenv("TOPSTEP_USERNAME")
TOPSTEP_API_KEY = os.getenv("TOPSTEP_API_KEY")
QWEN_API_KEY = os.getenv("QWEN_API_KEY")

def create_ssl_session():
    """Create aiohttp session with SSL verification disabled for testing."""
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    return aiohttp.ClientSession(connector=connector)

async def test_topstep_endpoints():
    """Test different TopStep API endpoints to find the correct one."""
    print("🔍 Testing TopStep API Endpoints...")
    
    # Try different possible endpoints
    endpoints = [
        "https://gateway-api-live.s2f.projectx.com",
        "https://gateway-api-demo.s2f.projectx.com", 
        "https://api.topstep.com",
        "https://gateway.topstep.com",
        "https://api-live.topstep.com",
        "https://gateway-live.topstep.com"
    ]
    
    auth_data = {
        "userName": TOPSTEP_USERNAME,
        "apiKey": TOPSTEP_API_KEY
    }
    
    for endpoint in endpoints:
        try:
            print(f"  Testing: {endpoint}")
            async with create_ssl_session() as session:
                url = f"{endpoint}/api/Auth/loginKey"
                
                async with session.post(url, json=auth_data, timeout=10) as response:
                    response_text = await response.text()
                    print(f"    Status: {response.status}")
                    
                    if response.status == 200:
                        data = json.loads(response_text)
                        if data.get("success"):
                            print(f"    ✅ SUCCESS! Valid endpoint: {endpoint}")
                            return endpoint
                        else:
                            print(f"    ❌ Auth failed: {data.get('errorMessage')}")
                    elif response.status == 404:
                        print(f"    ❌ Endpoint not found")
                    else:
                        print(f"    ❌ HTTP {response.status}: {response_text[:100]}")
                        
        except Exception as e:
            print(f"    ❌ Connection failed: {str(e)[:100]}")
    
    return None

async def test_qwen_api_key():
    """Test Qwen API key with different formats."""
    print("🔍 Testing Qwen API Key...")
    
    if not QWEN_API_KEY:
        print("  ❌ No Qwen API key found")
        return False
    
    print(f"  API Key: {QWEN_API_KEY[:20]}...")
    print(f"  Length: {len(QWEN_API_KEY)}")
    print(f"  Starts with 'sk-': {QWEN_API_KEY.startswith('sk-')}")
    
    # Test the API key
    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {QWEN_API_KEY}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "qwen-turbo",
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 10
            }
            
            url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
            
            async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                response_text = await response.text()
                print(f"  Status: {response.status}")
                
                if response.status == 200:
                    print("  ✅ Qwen API key is valid!")
                    return True
                elif response.status == 401:
                    print(f"  ❌ Invalid API key: {response_text}")
                    return False
                else:
                    print(f"  ❌ HTTP {response.status}: {response_text}")
                    return False
                    
    except Exception as e:
        print(f"  ❌ Qwen API test failed: {e}")
        return False

async def test_alternative_qwen_endpoints():
    """Test alternative Qwen API endpoints."""
    print("🔍 Testing Alternative Qwen Endpoints...")
    
    endpoints = [
        "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
        "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
        "https://api.qwen.com/v1/chat/completions"
    ]
    
    for endpoint in endpoints:
        try:
            print(f"  Testing: {endpoint}")
            async with create_ssl_session() as session:
                headers = {
                    "Authorization": f"Bearer {QWEN_API_KEY}",
                    "Content-Type": "application/json"
                }
                
                if "compatible-mode" in endpoint:
                    payload = {
                        "model": "qwen-turbo",
                        "messages": [{"role": "user", "content": "test"}],
                        "max_tokens": 5
                    }
                else:
                    payload = {
                        "model": "qwen-turbo",
                        "input": {"messages": [{"role": "user", "content": "test"}]},
                        "parameters": {"max_tokens": 5}
                    }
                
                async with session.post(endpoint, json=payload, headers=headers, timeout=15) as response:
                    response_text = await response.text()
                    print(f"    Status: {response.status}")
                    
                    if response.status == 200:
                        print(f"    ✅ Working endpoint: {endpoint}")
                        return endpoint
                    else:
                        print(f"    ❌ Failed: {response_text[:100]}")
                        
        except Exception as e:
            print(f"    ❌ Connection failed: {str(e)[:100]}")
    
    return None

async def main():
    """Run credential verification tests."""
    print("🚀 VERIFYING API CREDENTIALS AND ENDPOINTS")
    print("=" * 60)
    
    print(f"📋 Credentials Summary:")
    print(f"  TopStep Username: {TOPSTEP_USERNAME}")
    print(f"  TopStep API Key: {TOPSTEP_API_KEY[:20]}..." if TOPSTEP_API_KEY else "None")
    print(f"  Qwen API Key: {QWEN_API_KEY[:20]}..." if QWEN_API_KEY else "None")
    print()
    
    # Test TopStep endpoints
    valid_topstep = await test_topstep_endpoints()
    print()
    
    # Test Qwen API key
    qwen_valid = await test_qwen_api_key()
    print()
    
    # Test alternative Qwen endpoints if needed
    if not qwen_valid:
        valid_qwen_endpoint = await test_alternative_qwen_endpoints()
    else:
        valid_qwen_endpoint = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    
    print()
    print("=" * 60)
    print("📊 VERIFICATION RESULTS:")
    
    if valid_topstep:
        print(f"✅ TopStep API: Working endpoint found - {valid_topstep}")
    else:
        print("❌ TopStep API: No working endpoint found")
        print("   Check if credentials are correct or if there's a different API URL")
    
    if qwen_valid:
        print(f"✅ Qwen API: Working with endpoint - {valid_qwen_endpoint}")
    else:
        print("❌ Qwen API: Invalid API key or endpoint issue")
        print("   Check if the API key is correct and has proper permissions")
    
    print("=" * 60)
    
    return bool(valid_topstep) and bool(qwen_valid)

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("🎉 All credentials verified successfully!")
    else:
        print("❌ Some credentials need to be fixed")
