"""
Main Trading Workflow using LangGraph.
Real implementation of agentic trading workflow.
"""

from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, TypedDict
from dataclasses import dataclass, field

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from core import get_logger, timing_decorator
from agents import (
    MarketDataAgent, FVGDetectionAgent, OrderBlocksAgent, 
    LiquiditySweepsAgent, RiskManagementAgent, ExecutionAgent
)
from api import TopStepClient
from llm import QwenClient
from llm.models import TradingDecision

logger = get_logger(__name__)


class TradingState(TypedDict):
    """State for the trading workflow."""
    # Market data
    current_market_data: Optional[Dict]
    market_data_history: List[Dict]
    
    # Strategy signals
    fvg_signals: List[Dict]
    order_block_signals: List[Dict]
    liquidity_sweep_signals: List[Dict]
    
    # Decision making
    trading_decisions: List[TradingDecision]
    selected_decision: Optional[TradingDecision]
    
    # Risk management
    risk_assessment: Optional[Dict]
    position_size: int
    
    # Execution
    execution_results: List[Dict]
    
    # Context
    account_balance: float
    current_positions: List[Dict]
    symbol: str
    timestamp: datetime
    
    # Workflow control
    should_trade: bool
    error_message: Optional[str]
    workflow_step: str


@dataclass
class TradingWorkflow:
    """Main trading workflow orchestrator."""
    
    topstep_client: TopStepClient
    qwen_client: QwenClient
    
    # Agents
    market_data_agent: MarketDataAgent = field(init=False)
    fvg_agent: FVGDetectionAgent = field(init=False)
    order_blocks_agent: OrderBlocksAgent = field(init=False)
    liquidity_sweeps_agent: LiquiditySweepsAgent = field(init=False)
    risk_agent: RiskManagementAgent = field(init=False)
    execution_agent: ExecutionAgent = field(init=False)
    
    # Workflow
    graph: StateGraph = field(init=False)
    memory: MemorySaver = field(init=False)
    
    def __post_init__(self):
        """Initialize agents and workflow graph."""
        self.logger = get_logger(self.__class__.__name__)
        
        # Initialize agents
        self.market_data_agent = MarketDataAgent(self.topstep_client)
        self.fvg_agent = FVGDetectionAgent()
        self.order_blocks_agent = OrderBlocksAgent()
        self.liquidity_sweeps_agent = LiquiditySweepsAgent()
        self.risk_agent = RiskManagementAgent()
        self.execution_agent = ExecutionAgent(self.topstep_client)
        
        # Initialize workflow
        self.memory = MemorySaver()
        self._build_workflow_graph()
        
        self.logger.info("Trading workflow initialized")
    
    def _build_workflow_graph(self) -> None:
        """Build the LangGraph workflow."""
        
        # Create state graph
        self.graph = StateGraph(TradingState)
        
        # Add nodes
        self.graph.add_node("market_data", self._market_data_node)
        self.graph.add_node("strategy_analysis", self._strategy_analysis_node)
        self.graph.add_node("decision_making", self._decision_making_node)
        self.graph.add_node("risk_assessment", self._risk_assessment_node)
        self.graph.add_node("execution", self._execution_node)
        
        # Define edges
        self.graph.add_edge("market_data", "strategy_analysis")
        self.graph.add_edge("strategy_analysis", "decision_making")
        self.graph.add_edge("decision_making", "risk_assessment")
        
        # Conditional edge from risk assessment
        self.graph.add_conditional_edges(
            "risk_assessment",
            self._should_execute_trade,
            {
                "execute": "execution",
                "reject": END
            }
        )
        
        self.graph.add_edge("execution", END)
        
        # Set entry point
        self.graph.set_entry_point("market_data")
        
        # Compile graph
        self.graph = self.graph.compile(checkpointer=self.memory)
    
    @timing_decorator
    async def _market_data_node(self, state: TradingState) -> TradingState:
        """Market data processing node."""
        
        try:
            self.logger.info("Processing market data", symbol=state["symbol"])
            
            # Fetch latest market data
            market_bars = await self.market_data_agent.fetch_market_data(
                contract_id=self.market_data_agent.topstep_client.convert_symbol_to_contract(state["symbol"]),
                timeframe="1m",
                limit=100
            )
            
            if not market_bars:
                state["error_message"] = "Failed to fetch market data"
                state["should_trade"] = False
                return state
            
            # Process market data with technical indicators
            processed_data = await self.market_data_agent.process_market_data(
                market_bars, 
                self.market_data_agent.topstep_client.convert_symbol_to_contract(state["symbol"])
            )
            
            if not processed_data:
                state["error_message"] = "Failed to process market data"
                state["should_trade"] = False
                return state
            
            # Update state
            state["current_market_data"] = {
                "latest_bar": processed_data[-1].__dict__ if processed_data and processed_data[-1] is not None else None,
                "bar_count": len(processed_data)
            }
            state["market_data_history"] = [bar.__dict__ for bar in processed_data]
            state["workflow_step"] = "market_data_complete"
            
            # Store in database
            await self.market_data_agent.store_market_data(processed_data)
            
            self.logger.info(f"Market data processed: {len(processed_data)} bars")
            return state
            
        except Exception as e:
            self.logger.error(f"Error in market data node: {e}", exc_info=True)
            state["error_message"] = str(e)
            state["should_trade"] = False
            return state
    
    @timing_decorator
    async def _strategy_analysis_node(self, state: TradingState) -> TradingState:
        """Strategy analysis node - run all strategy agents."""
        
        try:
            self.logger.info("Running strategy analysis")
            
            # Convert market data back to objects
            from agents.market_data_agent import ProcessedMarketData
            market_data = []
            for bar_dict in state["market_data_history"]:
                bar = ProcessedMarketData(**bar_dict)
                market_data.append(bar)
            
            if not market_data:
                state["error_message"] = "No market data for strategy analysis"
                return state
            
            # Run FVG detection
            fvg_signals = self.fvg_agent.detect_fvgs(market_data)
            # Add detected FVGs to active list for signal generation
            for fvg in fvg_signals:
                if fvg not in self.fvg_agent.active_fvgs:
                    self.fvg_agent.active_fvgs.append(fvg)

            self.fvg_agent.update_fvg_status(
                market_data[-1].close_price,
                market_data[-1].timestamp
            )
            fvg_trading_signals = self.fvg_agent.generate_trading_signals(market_data[-1])
            
            # Run Order Blocks detection
            ob_signals = self.order_blocks_agent.detect_order_blocks(market_data)
            # Add detected Order Blocks to active list for signal generation
            for ob in ob_signals:
                if ob not in self.order_blocks_agent.active_order_blocks:
                    self.order_blocks_agent.active_order_blocks.append(ob)

            self.order_blocks_agent.update_order_block_status(
                market_data[-1].close_price,
                market_data[-1].timestamp
            )
            ob_trading_signals = self.order_blocks_agent.generate_trading_signals(market_data[-1])
            
            # Run Liquidity Sweeps detection
            self.liquidity_sweeps_agent.update_liquidity_levels(market_data)
            ls_signals = self.liquidity_sweeps_agent.detect_liquidity_sweeps(market_data[-1])
            # Add detected Liquidity Sweeps to active list for signal generation
            for ls in ls_signals:
                if ls not in self.liquidity_sweeps_agent.active_sweeps:
                    self.liquidity_sweeps_agent.active_sweeps.append(ls)

            ls_trading_signals = self.liquidity_sweeps_agent.generate_trading_signals(market_data[-1])
            
            # Update state with signals
            state["fvg_signals"] = [signal.__dict__ for signal in fvg_signals]
            state["order_block_signals"] = [signal.__dict__ for signal in ob_signals]
            state["liquidity_sweep_signals"] = [signal.__dict__ for signal in ls_signals]
            
            # Combine all trading signals
            all_trading_signals = fvg_trading_signals + ob_trading_signals + ls_trading_signals
            state["trading_decisions"] = all_trading_signals
            state["workflow_step"] = "strategy_analysis_complete"
            
            self.logger.info(
                f"Strategy analysis complete",
                fvg_signals=len(fvg_signals),
                ob_signals=len(ob_signals),
                ls_signals=len(ls_signals),
                trading_signals=len(all_trading_signals)
            )
            
            return state
            
        except Exception as e:
            self.logger.error(f"Error in strategy analysis node: {e}", exc_info=True)
            state["error_message"] = str(e)
            return state
    
    @timing_decorator
    async def _decision_making_node(self, state: TradingState) -> TradingState:
        """Decision making node using Qwen LLM."""
        
        try:
            self.logger.info("Making trading decision with LLM")
            
            trading_decisions = state["trading_decisions"]
            
            if not trading_decisions:
                self.logger.info("No trading signals to evaluate")
                state["should_trade"] = False
                state["workflow_step"] = "no_signals"
                return state
            
            # Prepare market context for LLM
            latest_bar = state.get("current_market_data", {}).get("latest_bar")
            current_price = latest_bar.get("close_price", 0) if latest_bar else 0

            market_context = {
                "symbol": state["symbol"],
                "current_price": current_price,
                "timestamp": state["timestamp"].isoformat(),
                "account_balance": state["account_balance"],
                "open_positions": len(state["current_positions"]),
                "signals_count": len(trading_decisions)
            }
            
            # Use Qwen to analyze and select best signal
            best_decision = await self.qwen_client.analyze_trading_signals(
                trading_decisions, market_context
            )
            
            if best_decision:
                state["selected_decision"] = best_decision
                state["should_trade"] = True
                self.logger.info(
                    f"LLM selected trading decision",
                    strategy=best_decision.strategy_name,
                    action=best_decision.action.value,
                    confidence=best_decision.confidence
                )
            else:
                state["should_trade"] = False
                self.logger.info("LLM rejected all trading signals")
            
            state["workflow_step"] = "decision_making_complete"
            return state
            
        except Exception as e:
            self.logger.error(f"Error in decision making node: {e}", exc_info=True)
            state["error_message"] = str(e)
            state["should_trade"] = False
            return state
    
    @timing_decorator
    async def _risk_assessment_node(self, state: TradingState) -> TradingState:
        """Risk assessment node."""
        
        try:
            self.logger.info("Assessing trade risk")
            
            selected_decision = state["selected_decision"]
            if not selected_decision:
                state["should_trade"] = False
                return state
            
            # Perform risk assessment
            risk_assessment = self.risk_agent.assess_trade_risk(
                selected_decision,
                state["account_balance"],
                state["current_positions"]
            )
            
            state["risk_assessment"] = risk_assessment
            state["position_size"] = risk_assessment.get("position_size", 1)
            state["should_trade"] = risk_assessment.get("approved", False)
            state["workflow_step"] = "risk_assessment_complete"
            
            self.logger.info(
                f"Risk assessment complete",
                approved=state["should_trade"],
                position_size=state["position_size"],
                risk_level=risk_assessment.get("risk_level", "UNKNOWN")
            )
            
            return state
            
        except Exception as e:
            self.logger.error(f"Error in risk assessment node: {e}", exc_info=True)
            state["error_message"] = str(e)
            state["should_trade"] = False
            return state
    
    @timing_decorator
    async def _execution_node(self, state: TradingState) -> TradingState:
        """Trade execution node."""
        
        try:
            self.logger.info("Executing trade")
            
            selected_decision = state["selected_decision"]
            position_size = state["position_size"]
            symbol = state["symbol"]
            
            # Execute the trade
            execution_result = await self.execution_agent.execute_trading_decision(
                selected_decision, position_size, symbol
            )
            
            # Update state
            state["execution_results"] = [execution_result.__dict__ if execution_result is not None else None]
            state["workflow_step"] = "execution_complete"
            
            if execution_result is not None:
                self.logger.info(
                    f"Trade execution complete",
                    success=execution_result.success,
                    order_id=execution_result.order_id,
                    status=execution_result.execution_status.value
                )
            else:
                self.logger.warning("Trade execution completed but no result returned")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Error in execution node: {e}", exc_info=True)
            state["error_message"] = str(e)
            return state
    
    def _should_execute_trade(self, state: TradingState) -> str:
        """Conditional edge function to determine if trade should be executed."""
        
        if state.get("should_trade", False) and state.get("selected_decision"):
            return "execute"
        else:
            return "reject"
    
    @timing_decorator
    async def run_trading_cycle(
        self, 
        symbol: str = "ES",
        account_balance: Optional[float] = None,
        current_positions: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Run a complete trading cycle."""
        
        try:
            self.logger.info(f"Starting trading cycle for {symbol}")
            
            # Get account balance if not provided
            if account_balance is None:
                account_balance = await self.topstep_client.get_account_balance()
                if account_balance is None:
                    account_balance = 50000.0  # Default fallback
            
            # Get current positions if not provided
            if current_positions is None:
                if hasattr(self.topstep_client, 'account_id') and self.topstep_client.account_id:
                    current_positions = await self.topstep_client.get_positions(self.topstep_client.account_id)
                else:
                    current_positions = []
                if current_positions is None:
                    current_positions = []
            
            # Initialize state
            initial_state = TradingState(
                current_market_data=None,
                market_data_history=[],
                fvg_signals=[],
                order_block_signals=[],
                liquidity_sweep_signals=[],
                trading_decisions=[],
                selected_decision=None,
                risk_assessment=None,
                position_size=1,
                execution_results=[],
                account_balance=account_balance,
                current_positions=current_positions,
                symbol=symbol,
                timestamp=datetime.now(timezone.utc),
                should_trade=False,
                error_message=None,
                workflow_step="initialized"
            )
            
            # Run the workflow
            config = {"configurable": {"thread_id": f"trading_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"}}
            
            final_state = await self.graph.ainvoke(initial_state, config)
            
            # Prepare result summary
            result = {
                "success": final_state.get("error_message") is None,
                "symbol": symbol,
                "workflow_step": final_state.get("workflow_step", "unknown"),
                "should_trade": final_state.get("should_trade", False),
                "error_message": final_state.get("error_message"),
                "signals_detected": {
                    "fvg": len(final_state.get("fvg_signals", [])),
                    "order_blocks": len(final_state.get("order_block_signals", [])),
                    "liquidity_sweeps": len(final_state.get("liquidity_sweep_signals", []))
                },
                "trading_decisions_count": len(final_state.get("trading_decisions", [])),
                "selected_decision": final_state.get("selected_decision").__dict__ if final_state.get("selected_decision") is not None else None,
                "risk_assessment": final_state.get("risk_assessment"),
                "execution_results": final_state.get("execution_results", []),
                "timestamp": final_state.get("timestamp").isoformat() if final_state.get("timestamp") else None
            }
            
            self.logger.info(
                f"Trading cycle complete for {symbol}",
                success=result["success"],
                should_trade=result["should_trade"],
                workflow_step=result["workflow_step"]
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in trading cycle: {e}", exc_info=True)
            return {
                "success": False,
                "symbol": symbol,
                "error_message": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status and statistics."""
        
        return {
            "agents_status": {
                "market_data": "active",
                "fvg": f"{len(self.fvg_agent.get_active_fvgs())} active FVGs",
                "order_blocks": f"{len(self.order_blocks_agent.get_active_order_blocks())} active OBs",
                "liquidity_sweeps": f"{len(self.liquidity_sweeps_agent.get_active_sweeps())} active sweeps",
                "risk_management": "active",
                "execution": f"{len(self.execution_agent.get_pending_orders())} pending orders"
            },
            "statistics": {
                "fvg_stats": self.fvg_agent.get_fvg_statistics(),
                "ob_stats": self.order_blocks_agent.get_order_block_statistics(),
                "sweep_stats": self.liquidity_sweeps_agent.get_sweep_statistics(),
                "execution_stats": self.execution_agent.get_execution_statistics()
            }
        }
