"""
Risk Management Agent.
Real implementation for managing trading risk and position sizing.
"""

from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from core import get_logger, timing_decorator, get_settings
from core.utils import calculate_position_size, calculate_kelly_fraction
from llm.models import TradingDecision, TradingAction

logger = get_logger(__name__)


class RiskLevel(Enum):
    """Risk level classifications."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    EXTREME = "EXTREME"


@dataclass
class RiskMetrics:
    """Risk metrics for current portfolio state."""
    total_exposure: float
    daily_pnl: float
    max_drawdown: float
    open_positions_count: int
    risk_per_trade: float
    portfolio_heat: float  # Percentage of account at risk
    risk_level: RiskLevel


@dataclass
class PositionRisk:
    """Risk assessment for a specific position."""
    symbol: str
    position_size: int
    entry_price: float
    current_price: float
    stop_loss: float
    risk_amount: float
    risk_percentage: float
    unrealized_pnl: float


class RiskManagementAgent:
    """Real risk management agent for trading operations."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.settings = get_settings()

        # Static risk parameters from settings
        self.max_portfolio_heat = self.settings.risk.max_portfolio_heat
        self.max_correlation_exposure = self.settings.risk.max_correlation_exposure

        # Tracking
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.position_risks: List[PositionRisk] = []

        # Register for dynamic settings updates
        self._register_settings_callbacks()

    def _register_settings_callbacks(self):
        """Register callbacks for dynamic settings updates."""
        try:
            from core.settings_manager import settings_manager
            settings_manager.register_callback("max_daily_loss", self._on_max_daily_loss_changed)
            settings_manager.register_callback("max_position_size", self._on_max_position_size_changed)
            settings_manager.register_callback("stop_loss_pct", self._on_stop_loss_pct_changed)
        except Exception as e:
            self.logger.warning(f"Could not register settings callbacks: {e}")

    def _on_max_daily_loss_changed(self, new_value: float):
        """Callback for max daily loss changes."""
        self.logger.info(f"Risk Manager: Max daily loss updated to ${new_value:.2f}")

    def _on_max_position_size_changed(self, new_value: int):
        """Callback for max position size changes."""
        self.logger.info(f"Risk Manager: Max position size updated to {new_value} contracts")

    def _on_stop_loss_pct_changed(self, new_value: float):
        """Callback for stop loss percentage changes."""
        self.logger.info(f"Risk Manager: Stop loss percentage updated to {new_value:.1f}%")

    def _get_dynamic_settings(self):
        """Get current dynamic settings."""
        try:
            from core.settings_manager import settings_manager
            return {
                "max_daily_loss": settings_manager.get_max_daily_loss(),
                "max_position_size": settings_manager.get_max_position_size(),
                "default_stop_loss_pct": settings_manager.get_stop_loss_pct()
            }
        except Exception as e:
            self.logger.warning(f"Could not get dynamic settings, using defaults: {e}")
            return {
                "max_daily_loss": self.settings.risk.max_daily_loss,
                "max_position_size": self.settings.risk.max_position_size,
                "default_stop_loss_pct": self.settings.risk.default_stop_loss_pct
            }
    
    @timing_decorator
    def assess_trade_risk(
        self,
        trading_decision: TradingDecision,
        account_balance: float,
        current_positions: List[Dict]
    ) -> Dict[str, Any]:
        """Assess risk for a proposed trading decision."""

        try:
            # Get current dynamic settings
            dynamic_settings = self._get_dynamic_settings()

            # Calculate position size
            position_size = self._calculate_position_size(
                trading_decision, account_balance, dynamic_settings
            )
            
            # Calculate risk metrics
            risk_amount = self._calculate_risk_amount(trading_decision, position_size, dynamic_settings)
            risk_percentage = (risk_amount / account_balance) * 100 if account_balance > 0 else 0.0

            # Check risk limits
            risk_checks = self._perform_risk_checks(
                trading_decision, position_size, risk_amount,
                risk_percentage, current_positions, account_balance, dynamic_settings
            )
            
            # Calculate portfolio heat
            portfolio_heat = self._calculate_portfolio_heat(
                current_positions, account_balance, risk_amount
            )
            
            # Determine overall risk level
            risk_level = self._determine_risk_level(
                risk_percentage, portfolio_heat, len(current_positions)
            )
            
            assessment = {
                "approved": risk_checks["all_passed"],
                "position_size": position_size,
                "risk_amount": risk_amount,
                "risk_percentage": risk_percentage,
                "portfolio_heat": portfolio_heat,
                "risk_level": risk_level.value,
                "risk_checks": risk_checks,
                "max_position_size": dynamic_settings["max_position_size"],
                "recommended_stop_loss": trading_decision.stop_loss,
                "risk_reward_ratio": self._calculate_risk_reward_ratio(trading_decision)
            }
            
            # Add system message for risk decision
            from core.settings_manager import settings_manager

            if assessment["approved"]:
                settings_manager.add_system_message(
                    component="RiskManager",
                    message=f"Trade approved: {trading_decision.strategy_name} {trading_decision.action.value} (Risk: {risk_percentage:.1f}%, Size: {position_size})",
                    level="INFO",
                    metadata={"risk_percentage": risk_percentage, "position_size": position_size}
                )
            else:
                failed_checks = [check for check, passed in risk_checks.items() if check != "all_passed" and not passed]
                settings_manager.add_system_message(
                    component="RiskManager",
                    message=f"Trade rejected: {trading_decision.strategy_name} {trading_decision.action.value} (Failed: {', '.join(failed_checks)})",
                    level="WARNING",
                    metadata={"failed_checks": failed_checks, "risk_percentage": risk_percentage}
                )

            self.logger.info(
                f"Risk assessment completed for {trading_decision.strategy_name}",
                approved=assessment["approved"],
                risk_percentage=risk_percentage,
                position_size=position_size
            )

            return assessment
            
        except Exception as e:
            self.logger.error(f"Error in risk assessment: {e}", exc_info=True)
            return {
                "approved": False,
                "error": str(e),
                "risk_level": RiskLevel.EXTREME.value
            }
    
    def _calculate_position_size(
        self,
        trading_decision: TradingDecision,
        account_balance: float,
        dynamic_settings: Dict[str, Any]
    ) -> int:
        """Calculate appropriate position size based on risk parameters."""
        
        # Calculate position size based on FIXED DOLLAR RISK, not percentage
        # This prevents excessive risk when account balance is large

        if trading_decision.stop_loss and trading_decision.entry_price:
            stop_distance = abs(trading_decision.entry_price - trading_decision.stop_loss)

            # Get contract multiplier (default to 50 for ES)
            contract_multiplier = 50  # This should come from contract specifications

            # Calculate risk per contract
            risk_per_contract = stop_distance * contract_multiplier

            # Use a reasonable fixed dollar risk amount instead of percentage
            # This prevents huge position sizes with large accounts
            max_risk_per_trade = min(
                dynamic_settings["max_daily_loss"] * 0.2,  # 20% of daily loss limit
                500.0  # Or $500 max per trade, whichever is smaller
            )

            # Calculate position size based on fixed dollar risk
            if risk_per_contract > 0:
                position_size = int(max_risk_per_trade / risk_per_contract)
            else:
                position_size = 1

            # Apply maximum position size limit
            position_size = min(position_size, dynamic_settings["max_position_size"])

            # Ensure at least 1 contract but not more than what makes sense
            position_size = max(1, min(position_size, 5))  # Cap at 5 contracts for safety

            return position_size

        return 1  # Default to 1 contract if no stop loss specified
    
    def _calculate_risk_amount(
        self,
        trading_decision: TradingDecision,
        position_size: int,
        dynamic_settings: Dict[str, Any]
    ) -> float:
        """Calculate the dollar amount at risk for this trade."""
        
        if not trading_decision.stop_loss or not trading_decision.entry_price:
            return 0.0
        
        stop_distance = abs(trading_decision.entry_price - trading_decision.stop_loss)
        contract_multiplier = 50  # ES multiplier
        
        risk_amount = stop_distance * position_size * contract_multiplier
        return risk_amount
    
    def _perform_risk_checks(
        self,
        trading_decision: TradingDecision,
        position_size: int,
        risk_amount: float,
        risk_percentage: float,
        current_positions: List[Dict],
        account_balance: float,
        dynamic_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Perform comprehensive risk checks."""
        
        checks = {}
        
        # Check 1: Position size limit
        checks["position_size_ok"] = position_size <= dynamic_settings["max_position_size"]

        # Check 2: Risk per trade limit
        max_risk_per_trade = dynamic_settings["default_stop_loss_pct"]
        checks["risk_per_trade_ok"] = risk_percentage <= max_risk_per_trade

        # Add detailed logging for risk per trade check
        self.logger.info(
            f"Risk Per Trade Check: Risk%={risk_percentage:.2f}%, "
            f"Max Risk%={max_risk_per_trade:.2f}%, "
            f"Check Passed={checks['risk_per_trade_ok']}"
        )

        # Check 3: Daily loss limit
        # If we're already at a loss, check if adding this risk would exceed the limit
        # If we're profitable, we can risk up to the full daily loss limit
        current_loss = min(0, self.daily_pnl)  # Only count losses, not profits
        projected_total_loss = abs(current_loss) + risk_amount
        max_daily_loss = dynamic_settings["max_daily_loss"]
        checks["daily_loss_ok"] = projected_total_loss <= max_daily_loss

        # Add detailed logging for daily loss check
        self.logger.info(
            f"Daily Loss Check: Current P&L=${self.daily_pnl:.2f}, "
            f"Current Loss=${current_loss:.2f}, Risk Amount=${risk_amount:.2f}, "
            f"Projected Total Loss=${projected_total_loss:.2f}, "
            f"Max Daily Loss=${max_daily_loss:.2f}, "
            f"Check Passed={checks['daily_loss_ok']}"
        )
        
        # Check 4: Portfolio heat limit
        current_heat = self._calculate_portfolio_heat(current_positions, account_balance, 0)
        projected_heat = current_heat + (risk_percentage)
        checks["portfolio_heat_ok"] = projected_heat <= self.max_portfolio_heat
        
        # Check 5: Maximum open positions
        max_positions = 5  # Configuration parameter
        checks["max_positions_ok"] = len(current_positions) < max_positions
        
        # Check 6: Correlation check (simplified)
        checks["correlation_ok"] = self._check_correlation_risk(
            trading_decision, current_positions
        )
        
        # Check 7: Market hours check
        checks["market_hours_ok"] = self._check_market_hours()
        
        # Overall approval
        checks["all_passed"] = all(checks.values())
        
        # Add failure reasons
        if not checks["all_passed"]:
            failures = [key for key, value in checks.items() if not value and key != "all_passed"]
            checks["failure_reasons"] = failures
        
        return checks
    
    def _calculate_portfolio_heat(
        self, 
        current_positions: List[Dict], 
        account_balance: float,
        additional_risk: float = 0.0
    ) -> float:
        """Calculate current portfolio heat (percentage of account at risk)."""
        
        total_risk = additional_risk
        
        for position in current_positions:
            # Calculate risk for each open position
            position_risk = self._calculate_position_risk_amount(position)
            total_risk += position_risk
        
        portfolio_heat = (total_risk / account_balance) * 100 if account_balance > 0 else 0.0
        return portfolio_heat
    
    def _calculate_position_risk_amount(self, position) -> float:
        """Calculate risk amount for an existing position."""
        try:
            # Handle both dict and Position object
            if hasattr(position, 'size'):
                size = position.size
                entry_price = position.average_price
            else:
                size = position.get('size', 0)
                entry_price = position.get('averagePrice', 0)
            
            # Estimate stop loss if not provided (use 2% default)
            stop_loss = entry_price * 0.98 if size > 0 else entry_price * 1.02
            
            stop_distance = abs(entry_price - stop_loss)
            contract_multiplier = 50  # ES multiplier
            
            risk_amount = stop_distance * abs(size) * contract_multiplier
            return risk_amount
            
        except Exception as e:
            self.logger.error(f"Error calculating position risk: {e}")
            return 0.0
    
    def _determine_risk_level(
        self, 
        risk_percentage: float, 
        portfolio_heat: float, 
        position_count: int
    ) -> RiskLevel:
        """Determine overall risk level based on metrics."""
        
        if (risk_percentage > 3.0 or 
            portfolio_heat > 15.0 or 
            position_count > 4):
            return RiskLevel.EXTREME
        elif (risk_percentage > 2.0 or 
              portfolio_heat > 10.0 or 
              position_count > 3):
            return RiskLevel.HIGH
        elif (risk_percentage > 1.0 or 
              portfolio_heat > 5.0 or 
              position_count > 2):
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _check_correlation_risk(
        self, 
        trading_decision: TradingDecision, 
        current_positions: List[Dict]
    ) -> bool:
        """Check for correlation risk between positions."""
        
        # Simplified correlation check
        # In a real implementation, you'd have a correlation matrix
        
        if not current_positions:
            return True
        
        # For now, just check if we're not over-concentrated in one direction
        long_positions = sum(1 for pos in current_positions if pos.size > 0)
        short_positions = sum(1 for pos in current_positions if pos.size < 0)
        
        if trading_decision.action == TradingAction.BUY:
            return long_positions < 3  # Max 3 long positions
        else:
            return short_positions < 3  # Max 3 short positions
    
    def _check_market_hours(self) -> bool:
        """Check if market is open for trading."""
        # Simplified market hours check
        # In a real implementation, you'd check actual market hours
        current_time = datetime.now(timezone.utc)
        hour = current_time.hour
        
        # Assume futures market is open 23/5
        # Closed from 22:00 Friday to 17:00 Sunday UTC
        weekday = current_time.weekday()
        
        if weekday == 4 and hour >= 22:  # Friday after 22:00
            return False
        elif weekday == 6 and hour < 17:  # Sunday before 17:00
            return False
        elif weekday == 5:  # Saturday
            return False
        
        return True
    
    def _calculate_risk_reward_ratio(self, trading_decision: TradingDecision) -> float:
        """Calculate risk-reward ratio for the trade."""
        
        if (not trading_decision.entry_price or 
            not trading_decision.stop_loss or 
            not trading_decision.take_profit):
            return 0.0
        
        risk = abs(trading_decision.entry_price - trading_decision.stop_loss)
        reward = abs(trading_decision.take_profit - trading_decision.entry_price)
        
        if risk == 0:
            return 0.0
        
        return reward / risk
    
    def update_daily_pnl(self, pnl_change: float) -> None:
        """Update daily P&L tracking."""
        self.daily_pnl += pnl_change
        
        # Update max drawdown if necessary
        if self.daily_pnl < self.max_drawdown:
            self.max_drawdown = self.daily_pnl
    
    def get_risk_metrics(self, account_balance: float, current_positions: List) -> RiskMetrics:
        """Get current risk metrics for the portfolio."""

        total_exposure = sum(
            abs(pos.size if hasattr(pos, 'size') else pos.get('size', 0)) *
            (pos.average_price if hasattr(pos, 'average_price') else pos.get('averagePrice', 0)) * 50
            for pos in current_positions
        )
        
        portfolio_heat = self._calculate_portfolio_heat(current_positions, account_balance)
        
        risk_level = self._determine_risk_level(
            portfolio_heat / len(current_positions) if current_positions else 0,
            portfolio_heat,
            len(current_positions)
        )
        
        return RiskMetrics(
            total_exposure=total_exposure,
            daily_pnl=self.daily_pnl,
            max_drawdown=self.max_drawdown,
            open_positions_count=len(current_positions),
            risk_per_trade=self._get_dynamic_settings()["default_stop_loss_pct"],
            portfolio_heat=portfolio_heat,
            risk_level=risk_level
        )
    
    def should_close_all_positions(self, account_balance: float) -> bool:
        """Determine if all positions should be closed due to risk limits."""
        
        # Get current dynamic settings
        dynamic_settings = self._get_dynamic_settings()

        # Close all if daily loss limit exceeded
        if self.daily_pnl <= -dynamic_settings["max_daily_loss"]:
            self.logger.warning(f"Daily loss limit exceeded: {self.daily_pnl}")
            return True

        # Close all if max drawdown exceeded
        if self.max_drawdown <= -(dynamic_settings["max_daily_loss"] * 2):
            self.logger.warning(f"Max drawdown exceeded: {self.max_drawdown}")
            return True
        
        return False
    
    def reset_daily_metrics(self) -> None:
        """Reset daily tracking metrics (call at start of each trading day)."""
        self.daily_pnl = 0.0
        self.position_risks.clear()
        self.logger.info("Daily risk metrics reset")
