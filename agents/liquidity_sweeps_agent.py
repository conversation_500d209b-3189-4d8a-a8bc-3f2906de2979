"""
Liquidity Sweeps Detection Agent.
Real implementation for detecting and trading liquidity sweeps.
"""

from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from core import get_logger, timing_decorator
from agents.market_data_agent import ProcessedMarketData
from llm.models import TradingDecision, TradingAction

logger = get_logger(__name__)


class SweepType(Enum):
    """Liquidity sweep types."""
    BUY_SIDE = "BUY_SIDE"    # Sweep above highs (false breakout up)
    SELL_SIDE = "SELL_SIDE"  # Sweep below lows (false breakout down)


class SweepStatus(Enum):
    """Liquidity sweep status."""
    DETECTED = "DETECTED"
    CONFIRMED = "CONFIRMED"
    TRADED = "TRADED"
    EXPIRED = "EXPIRED"


@dataclass
class LiquidityLevel:
    """Liquidity level data structure."""
    price: float
    timestamp: datetime
    strength: float  # Based on volume and touches
    touches: int = 0


@dataclass
class LiquiditySweep:
    """Liquidity sweep data structure."""
    id: str
    sweep_type: SweepType
    status: SweepStatus
    
    # Sweep details
    liquidity_level: LiquidityLevel
    sweep_high: float
    sweep_low: float
    sweep_distance: float
    
    # Formation details
    detection_time: datetime
    sweep_bar: ProcessedMarketData
    reversal_bar: Optional[ProcessedMarketData] = None
    timeframe: str = "1m"
    
    # Trading levels
    entry_price: float = 0.0
    stop_loss: float = 0.0
    take_profit: float = 0.0
    
    # Validation metrics
    volume_spike: bool = False
    reversal_strength: float = 0.0
    confluence_score: float = 0.0
    
    # Tracking
    max_favorable_move: float = 0.0
    max_adverse_move: float = 0.0


class LiquiditySweepsAgent:
    """Real liquidity sweeps detection and trading agent."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.active_sweeps: List[LiquiditySweep] = []
        self.sweep_history: List[LiquiditySweep] = []
        self.liquidity_levels: List[LiquidityLevel] = []
        
        # Configuration
        self.min_sweep_distance = 2.0  # Minimum distance for valid sweep
        self.max_sweep_age_hours = 4   # Maximum age before expiring
        self.lookback_periods = 50     # Periods to look back for levels
        self.level_touch_threshold = 1.0  # Distance to consider a "touch"
    
    @timing_decorator
    def update_liquidity_levels(self, market_data: List[ProcessedMarketData]) -> None:
        """Update liquidity levels based on market data."""
        if len(market_data) < self.lookback_periods:
            return
        
        # Clear old levels
        self.liquidity_levels.clear()
        
        # Find significant highs and lows
        recent_data = market_data[-self.lookback_periods:]
        
        # Detect swing highs and lows
        swing_highs = self._find_swing_highs(recent_data)
        swing_lows = self._find_swing_lows(recent_data)
        
        # Create liquidity levels from swing points
        for high_data in swing_highs:
            level = LiquidityLevel(
                price=high_data['price'],
                timestamp=high_data['timestamp'],
                strength=high_data['strength']
            )
            self.liquidity_levels.append(level)
        
        for low_data in swing_lows:
            level = LiquidityLevel(
                price=low_data['price'],
                timestamp=low_data['timestamp'],
                strength=low_data['strength']
            )
            self.liquidity_levels.append(level)
        
        # Update touch counts
        self._update_level_touches(recent_data)
        
        self.logger.info(f"Updated {len(self.liquidity_levels)} liquidity levels")
    
    def _find_swing_highs(self, data: List[ProcessedMarketData]) -> List[Dict]:
        """Find swing high points in the data."""
        swing_highs = []
        
        for i in range(2, len(data) - 2):
            current = data[i]
            
            # Check if current bar is higher than surrounding bars
            if (current.high_price > data[i-1].high_price and 
                current.high_price > data[i-2].high_price and
                current.high_price > data[i+1].high_price and 
                current.high_price > data[i+2].high_price):
                
                # Calculate strength based on volume and price action
                strength = self._calculate_level_strength(data[i-2:i+3], current.high_price, True)
                
                swing_highs.append({
                    'price': current.high_price,
                    'timestamp': current.timestamp,
                    'strength': strength
                })
        
        return swing_highs
    
    def _find_swing_lows(self, data: List[ProcessedMarketData]) -> List[Dict]:
        """Find swing low points in the data."""
        swing_lows = []
        
        for i in range(2, len(data) - 2):
            current = data[i]
            
            # Check if current bar is lower than surrounding bars
            if (current.low_price < data[i-1].low_price and 
                current.low_price < data[i-2].low_price and
                current.low_price < data[i+1].low_price and 
                current.low_price < data[i+2].low_price):
                
                # Calculate strength based on volume and price action
                strength = self._calculate_level_strength(data[i-2:i+3], current.low_price, False)
                
                swing_lows.append({
                    'price': current.low_price,
                    'timestamp': current.timestamp,
                    'strength': strength
                })
        
        return swing_lows
    
    def _calculate_level_strength(
        self, 
        surrounding_bars: List[ProcessedMarketData], 
        level_price: float, 
        is_high: bool
    ) -> float:
        """Calculate the strength of a liquidity level."""
        if not surrounding_bars:
            return 0.0
        
        # Volume factor
        total_volume = sum(bar.volume for bar in surrounding_bars)
        avg_volume = total_volume / len(surrounding_bars)
        volume_factor = min(avg_volume / 1000, 2.0)  # Normalize volume
        
        # Price rejection factor
        center_bar = surrounding_bars[len(surrounding_bars) // 2]
        if is_high:
            rejection = (center_bar.high_price - center_bar.close_price) / (center_bar.high_price - center_bar.low_price)
        else:
            rejection = (center_bar.close_price - center_bar.low_price) / (center_bar.high_price - center_bar.low_price)
        
        rejection_factor = rejection * 2.0
        
        # Combine factors
        strength = (volume_factor + rejection_factor) * 50
        return min(strength, 100.0)
    
    def _update_level_touches(self, data: List[ProcessedMarketData]) -> None:
        """Update touch counts for liquidity levels."""
        for level in self.liquidity_levels:
            touches = 0
            for bar in data:
                # Check if price touched the level
                if (abs(bar.high_price - level.price) <= self.level_touch_threshold or
                    abs(bar.low_price - level.price) <= self.level_touch_threshold):
                    touches += 1
            
            level.touches = touches
    
    @timing_decorator
    def detect_liquidity_sweeps(self, current_bar: ProcessedMarketData) -> List[LiquiditySweep]:
        """Detect liquidity sweeps in current market data."""
        detected_sweeps = []
        
        # Check for buy-side liquidity sweeps (false breakout above highs)
        buy_side_sweep = self._detect_buy_side_sweep(current_bar)
        if buy_side_sweep:
            detected_sweeps.append(buy_side_sweep)
        
        # Check for sell-side liquidity sweeps (false breakout below lows)
        sell_side_sweep = self._detect_sell_side_sweep(current_bar)
        if sell_side_sweep:
            detected_sweeps.append(sell_side_sweep)
        
        if detected_sweeps:
            self.logger.info(f"Detected {len(detected_sweeps)} liquidity sweeps")
        
        return detected_sweeps
    
    def _detect_buy_side_sweep(self, current_bar: ProcessedMarketData) -> Optional[LiquiditySweep]:
        """Detect buy-side liquidity sweep (sweep above highs)."""
        
        # Find the highest liquidity level that might be swept
        high_levels = [level for level in self.liquidity_levels 
                      if level.price > current_bar.close_price - 10]  # Within reasonable range
        
        if not high_levels:
            return None
        
        # Sort by price (highest first)
        high_levels.sort(key=lambda x: x.price, reverse=True)
        
        for level in high_levels:
            # Check if current bar swept above the level but closed below
            if (current_bar.high_price > level.price and 
                current_bar.close_price < level.price):
                
                sweep_distance = current_bar.high_price - level.price
                
                # Validate minimum sweep distance
                if sweep_distance < self.min_sweep_distance:
                    continue
                
                # Check for reversal characteristics
                reversal_strength = self._calculate_reversal_strength(current_bar, SweepType.BUY_SIDE)
                
                # Check for volume spike
                volume_spike = self._check_volume_spike(current_bar)
                
                # Calculate confluence score
                confluence_score = self._calculate_sweep_confluence_score(
                    level, current_bar, SweepType.BUY_SIDE
                )
                
                # Generate trading levels
                entry_price = level.price - (sweep_distance * 0.2)  # Enter below the level
                stop_loss = current_bar.high_price + (sweep_distance * 0.3)
                take_profit = level.price - (sweep_distance * 2.0)
                
                sweep_id = f"BUY_SWEEP_{current_bar.timestamp.strftime('%Y%m%d_%H%M%S')}"
                
                return LiquiditySweep(
                    id=sweep_id,
                    sweep_type=SweepType.BUY_SIDE,
                    status=SweepStatus.DETECTED,
                    liquidity_level=level,
                    sweep_high=current_bar.high_price,
                    sweep_low=current_bar.low_price,
                    sweep_distance=sweep_distance,
                    detection_time=current_bar.timestamp,
                    sweep_bar=current_bar,
                    timeframe=current_bar.timeframe,
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    volume_spike=volume_spike,
                    reversal_strength=reversal_strength,
                    confluence_score=confluence_score
                )
        
        return None
    
    def _detect_sell_side_sweep(self, current_bar: ProcessedMarketData) -> Optional[LiquiditySweep]:
        """Detect sell-side liquidity sweep (sweep below lows)."""
        
        # Find the lowest liquidity level that might be swept
        low_levels = [level for level in self.liquidity_levels 
                     if level.price < current_bar.close_price + 10]  # Within reasonable range
        
        if not low_levels:
            return None
        
        # Sort by price (lowest first)
        low_levels.sort(key=lambda x: x.price)
        
        for level in low_levels:
            # Check if current bar swept below the level but closed above
            if (current_bar.low_price < level.price and 
                current_bar.close_price > level.price):
                
                sweep_distance = level.price - current_bar.low_price
                
                # Validate minimum sweep distance
                if sweep_distance < self.min_sweep_distance:
                    continue
                
                # Check for reversal characteristics
                reversal_strength = self._calculate_reversal_strength(current_bar, SweepType.SELL_SIDE)
                
                # Check for volume spike
                volume_spike = self._check_volume_spike(current_bar)
                
                # Calculate confluence score
                confluence_score = self._calculate_sweep_confluence_score(
                    level, current_bar, SweepType.SELL_SIDE
                )
                
                # Generate trading levels
                entry_price = level.price + (sweep_distance * 0.2)  # Enter above the level
                stop_loss = current_bar.low_price - (sweep_distance * 0.3)
                take_profit = level.price + (sweep_distance * 2.0)
                
                sweep_id = f"SELL_SWEEP_{current_bar.timestamp.strftime('%Y%m%d_%H%M%S')}"
                
                return LiquiditySweep(
                    id=sweep_id,
                    sweep_type=SweepType.SELL_SIDE,
                    status=SweepStatus.DETECTED,
                    liquidity_level=level,
                    sweep_high=current_bar.high_price,
                    sweep_low=current_bar.low_price,
                    sweep_distance=sweep_distance,
                    detection_time=current_bar.timestamp,
                    sweep_bar=current_bar,
                    timeframe=current_bar.timeframe,
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    volume_spike=volume_spike,
                    reversal_strength=reversal_strength,
                    confluence_score=confluence_score
                )
        
        return None
    
    def _calculate_reversal_strength(self, bar: ProcessedMarketData, sweep_type: SweepType) -> float:
        """Calculate the strength of the reversal after sweep."""
        
        if sweep_type == SweepType.BUY_SIDE:
            # For buy-side sweep, look for bearish reversal
            body_size = abs(bar.close_price - bar.open_price)
            upper_wick = bar.high_price - max(bar.open_price, bar.close_price)
            total_range = bar.high_price - bar.low_price
            
            if total_range == 0:
                return 0.0
            
            # Strong reversal has large upper wick and bearish body
            wick_ratio = upper_wick / total_range
            body_ratio = body_size / total_range if bar.close_price < bar.open_price else 0
            
        else:  # SELL_SIDE
            # For sell-side sweep, look for bullish reversal
            body_size = abs(bar.close_price - bar.open_price)
            lower_wick = min(bar.open_price, bar.close_price) - bar.low_price
            total_range = bar.high_price - bar.low_price
            
            if total_range == 0:
                return 0.0
            
            # Strong reversal has large lower wick and bullish body
            wick_ratio = lower_wick / total_range
            body_ratio = body_size / total_range if bar.close_price > bar.open_price else 0
        
        strength = (wick_ratio * 60) + (body_ratio * 40)
        return min(strength, 100.0)
    
    def _check_volume_spike(self, bar: ProcessedMarketData) -> bool:
        """Check if the bar has a volume spike."""
        # Simple volume spike detection
        # In a real implementation, you'd compare with recent average volume
        return bar.volume > 1000  # Placeholder threshold
    
    def _calculate_sweep_confluence_score(
        self, 
        level: LiquidityLevel, 
        sweep_bar: ProcessedMarketData,
        sweep_type: SweepType
    ) -> float:
        """Calculate confluence score for liquidity sweep quality."""
        score = 0.0
        
        # Level strength (0-30 points)
        score += min(level.strength, 30.0)
        
        # Level touches (0-20 points)
        touch_score = min(level.touches * 5, 20.0)
        score += touch_score
        
        # Volume analysis (0-25 points)
        if sweep_bar.volume > 500:
            score += 20
        elif sweep_bar.volume > 200:
            score += 10
        
        # Technical indicator confluence (0-25 points)
        if sweep_bar.rsi:
            if sweep_type == SweepType.BUY_SIDE and sweep_bar.rsi > 70:
                score += 20  # Overbought condition supports bearish reversal
            elif sweep_type == SweepType.SELL_SIDE and sweep_bar.rsi < 30:
                score += 20  # Oversold condition supports bullish reversal
        
        return min(score, 100.0)
    
    def generate_trading_signals(self, current_data: ProcessedMarketData) -> List[TradingDecision]:
        """Generate trading signals based on liquidity sweep analysis."""
        signals = []
        
        for sweep in self.active_sweeps:
            if self._should_enter_trade(sweep, current_data):
                signal = self._create_trading_signal(sweep, current_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _should_enter_trade(self, sweep: LiquiditySweep, current_data: ProcessedMarketData) -> bool:
        """Determine if we should enter a trade based on liquidity sweep."""
        current_price = current_data.close_price
        
        # Check if price is near entry level (made more lenient)
        entry_distance = abs(current_price - sweep.entry_price)
        max_entry_distance = sweep.sweep_distance * 0.6  # Increased from 0.3 to 0.6
        
        if entry_distance > max_entry_distance:
            return False
        
        # Quality checks (made more lenient for signal generation)
        quality_checks = [
            sweep.confluence_score >= 40,    # Lowered from 60 to 40
            sweep.reversal_strength >= 25,   # Lowered from 40 to 25
            sweep.status == SweepStatus.DETECTED  # Not already traded
        ]
        
        return all(quality_checks)
    
    def _create_trading_signal(self, sweep: LiquiditySweep, current_data: ProcessedMarketData) -> Optional[TradingDecision]:
        """Create trading signal from liquidity sweep."""
        try:
            # Determine action based on sweep type
            action = TradingAction.SELL if sweep.sweep_type == SweepType.BUY_SIDE else TradingAction.BUY
            
            # Calculate confidence
            base_confidence = (sweep.confluence_score + sweep.reversal_strength) / 200.0
            confidence = min(base_confidence, 0.90)  # Cap at 90%
            
            reasoning = (
                f"Liquidity Sweep {sweep.sweep_type.value} detected. "
                f"Sweep distance: {sweep.sweep_distance:.2f}, "
                f"Confluence score: {sweep.confluence_score:.1f}, "
                f"Reversal strength: {sweep.reversal_strength:.1f}, "
                f"Volume spike: {sweep.volume_spike}"
            )
            
            # Mark as traded
            sweep.status = SweepStatus.TRADED
            
            return TradingDecision(
                action=action,
                confidence=confidence,
                reasoning=reasoning,
                entry_price=sweep.entry_price,
                stop_loss=sweep.stop_loss,
                take_profit=sweep.take_profit,
                strategy_name="LiquiditySweeps",
                metadata={
                    "sweep_id": sweep.id,
                    "sweep_type": sweep.sweep_type.value,
                    "sweep_distance": sweep.sweep_distance,
                    "confluence_score": sweep.confluence_score,
                    "reversal_strength": sweep.reversal_strength,
                    "detection_time": sweep.detection_time.isoformat()
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error creating trading signal: {e}")
            return None
    
    def update_sweep_status(self, current_time: datetime) -> None:
        """Update status of active sweeps."""
        for sweep in self.active_sweeps[:]:
            # Check if sweep has expired
            age_hours = (current_time - sweep.detection_time).total_seconds() / 3600
            if age_hours > self.max_sweep_age_hours:
                sweep.status = SweepStatus.EXPIRED
                self.active_sweeps.remove(sweep)
                self.sweep_history.append(sweep)
    
    def get_active_sweeps(self) -> List[LiquiditySweep]:
        """Get list of active liquidity sweeps."""
        return self.active_sweeps.copy()
    
    def get_liquidity_levels(self) -> List[LiquidityLevel]:
        """Get current liquidity levels."""
        return self.liquidity_levels.copy()
    
    def get_sweep_statistics(self) -> Dict[str, Any]:
        """Get liquidity sweep detection and performance statistics."""
        total_sweeps = len(self.active_sweeps) + len(self.sweep_history)
        traded_sweeps = len([s for s in self.sweep_history if s.status == SweepStatus.TRADED])
        
        return {
            "total_detected": total_sweeps,
            "active_count": len(self.active_sweeps),
            "traded_count": traded_sweeps,
            "expired_count": len([s for s in self.sweep_history if s.status == SweepStatus.EXPIRED]),
            "liquidity_levels_count": len(self.liquidity_levels),
            "trade_rate": (traded_sweeps / total_sweeps * 100) if total_sweeps > 0 else 0,
            "avg_confluence_score": sum(s.confluence_score for s in self.active_sweeps) / len(self.active_sweeps) if self.active_sweeps else 0
        }
