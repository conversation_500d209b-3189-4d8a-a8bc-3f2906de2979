"""
Market Data Processing Agent.
Real implementation for processing and analyzing market data.
"""

import asyncio
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from core import get_logger, get_db_session, timing_decorator
from core.database import MarketData, TechnicalIndicators
from core.utils import moving_average, exponential_moving_average, standard_deviation
from api import TopStepClient
from api.models import MarketDataBar

logger = get_logger(__name__)


@dataclass
class ProcessedMarketData:
    """Processed market data with technical indicators."""
    contract_id: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    timeframe: str
    
    # Technical indicators
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_20: Optional[float] = None
    rsi: Optional[float] = None
    atr: Optional[float] = None
    volatility: Optional[float] = None


class MarketDataAgent:
    """Real market data processing agent."""
    
    def __init__(self, topstep_client: TopStepClient):
        self.topstep_client = topstep_client
        self.logger = get_logger(self.__class__.__name__)
        self.cache = {}  # Price history cache
    
    @timing_decorator
    async def fetch_market_data(
        self, 
        contract_id: str, 
        timeframe: str = "1m", 
        limit: int = 100
    ) -> List[MarketDataBar]:
        """Fetch real market data from TopStep API."""
        try:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=24)  # Last 24 hours
            
            self.logger.info(f"Fetching market data for {contract_id}", 
                           timeframe=timeframe, limit=limit)
            
            market_data_response = await self.topstep_client.get_market_data(
                contract_id=contract_id,
                start_time=start_time,
                end_time=end_time,
                timeframe=timeframe,
                limit=limit
            )
            
            if market_data_response and market_data_response.success:
                bars = market_data_response.bars
                self.logger.info(f"Retrieved {len(bars)} bars for {contract_id}")
                return bars
            else:
                self.logger.error(f"Failed to fetch market data for {contract_id}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error fetching market data: {e}", exc_info=True)
            return []
    
    @timing_decorator
    async def process_market_data(
        self, 
        bars: List[MarketDataBar], 
        contract_id: str
    ) -> List[ProcessedMarketData]:
        """Process raw market data and calculate technical indicators."""
        if not bars:
            return []
        
        try:
            # Extract price data
            closes = [bar.close_price for bar in bars]
            highs = [bar.high_price for bar in bars]
            lows = [bar.low_price for bar in bars]
            volumes = [bar.volume for bar in bars]
            
            # Calculate technical indicators
            sma_20_values = moving_average(closes, 20) if len(closes) >= 20 else []
            sma_50_values = moving_average(closes, 50) if len(closes) >= 50 else []
            ema_20_values = exponential_moving_average(closes, 0.1) if len(closes) >= 20 else []
            rsi_values = self._calculate_rsi(closes, 14) if len(closes) >= 14 else []
            atr_values = self._calculate_atr(highs, lows, closes, 14) if len(closes) >= 14 else []
            
            processed_data = []
            
            for i, bar in enumerate(bars):
                # Get indicator values for this bar (if available)
                sma_20 = sma_20_values[i - (len(bars) - len(sma_20_values))] if i >= (len(bars) - len(sma_20_values)) else None
                sma_50 = sma_50_values[i - (len(bars) - len(sma_50_values))] if i >= (len(bars) - len(sma_50_values)) else None
                ema_20 = ema_20_values[i] if i < len(ema_20_values) else None
                rsi = rsi_values[i - (len(bars) - len(rsi_values))] if i >= (len(bars) - len(rsi_values)) else None
                atr = atr_values[i - (len(bars) - len(atr_values))] if i >= (len(bars) - len(atr_values)) else None
                
                # Calculate volatility for this period
                if i >= 20:
                    recent_closes = closes[max(0, i-19):i+1]
                    volatility = standard_deviation(recent_closes)
                else:
                    volatility = None
                
                processed_bar = ProcessedMarketData(
                    contract_id=contract_id,
                    timestamp=bar.timestamp,
                    open_price=bar.open_price,
                    high_price=bar.high_price,
                    low_price=bar.low_price,
                    close_price=bar.close_price,
                    volume=bar.volume,
                    timeframe="1m",  # Default timeframe
                    sma_20=sma_20,
                    sma_50=sma_50,
                    ema_20=ema_20,
                    rsi=rsi,
                    atr=atr,
                    volatility=volatility
                )
                
                processed_data.append(processed_bar)
            
            self.logger.info(f"Processed {len(processed_data)} bars with technical indicators")
            return processed_data
            
        except Exception as e:
            self.logger.error(f"Error processing market data: {e}", exc_info=True)
            return []
    
    async def store_market_data(self, processed_data: List[ProcessedMarketData]) -> bool:
        """Store processed market data in database."""
        try:
            async with get_db_session() as session:
                for data in processed_data:
                    # Store market data
                    market_data = MarketData(
                        contract_id=data.contract_id,
                        timestamp=data.timestamp,
                        open_price=data.open_price,
                        high_price=data.high_price,
                        low_price=data.low_price,
                        close_price=data.close_price,
                        volume=data.volume,
                        timeframe=data.timeframe
                    )
                    session.add(market_data)
                    
                    # Store technical indicators as separate records
                    indicators_to_store = [
                        ('sma_20', data.sma_20),
                        ('sma_50', data.sma_50),
                        ('ema_20', data.ema_20),
                        ('rsi', data.rsi),
                        ('atr', data.atr),
                        ('volatility', data.volatility)
                    ]

                    for indicator_name, value in indicators_to_store:
                        if value is not None:
                            indicator = TechnicalIndicators(
                                contract_id=data.contract_id,
                                timestamp=data.timestamp,
                                timeframe=data.timeframe,
                                indicator_name=indicator_name,
                                value=value
                            )
                            session.add(indicator)
                
                await session.commit()
                self.logger.info(f"Stored {len(processed_data)} market data records")
                return True
                
        except Exception as e:
            self.logger.error(f"Error storing market data: {e}", exc_info=True)
            return False
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> List[float]:
        """Calculate RSI indicator."""
        if len(prices) < period + 1:
            return []
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            gains.append(max(change, 0))
            losses.append(max(-change, 0))
        
        if len(gains) < period:
            return []
        
        # Calculate initial averages
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        rsi_values = []
        
        for i in range(period, len(gains)):
            if avg_loss == 0:
                rsi_val = 100
            else:
                rs = avg_gain / avg_loss
                rsi_val = 100 - (100 / (1 + rs))
            
            rsi_values.append(round(rsi_val, 2))
            
            # Update averages for next iteration
            if i < len(gains) - 1:
                avg_gain = (avg_gain * (period - 1) + gains[i]) / period
                avg_loss = (avg_loss * (period - 1) + losses[i]) / period
        
        return rsi_values
    
    def _calculate_atr(self, highs: List[float], lows: List[float], closes: List[float], period: int = 14) -> List[float]:
        """Calculate Average True Range."""
        if len(highs) < period + 1:
            return []
        
        true_ranges = []
        
        for i in range(1, len(highs)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        if len(true_ranges) < period:
            return []
        
        atr_values = []
        
        # Calculate initial ATR
        initial_atr = sum(true_ranges[:period]) / period
        atr_values.append(initial_atr)
        
        # Calculate subsequent ATR values using smoothing
        for i in range(period, len(true_ranges)):
            atr = (atr_values[-1] * (period - 1) + true_ranges[i]) / period
            atr_values.append(atr)
        
        return atr_values
    
    async def get_latest_data(self, contract_id: str, timeframe: str = "1m") -> Optional[ProcessedMarketData]:
        """Get the latest processed market data for a contract."""
        try:
            bars = await self.fetch_market_data(contract_id, timeframe, limit=50)
            if not bars:
                return None
            
            processed_data = await self.process_market_data(bars, contract_id)
            if not processed_data:
                return None
            
            return processed_data[-1]  # Return latest bar
            
        except Exception as e:
            self.logger.error(f"Error getting latest data: {e}", exc_info=True)
            return None
