"""
Order Blocks Detection Agent.
Real implementation for detecting and trading Order Blocks.
"""

from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from core import get_logger, timing_decorator
from agents.market_data_agent import ProcessedMarketData
from llm.models import TradingDecision, TradingAction

logger = get_logger(__name__)


class OrderBlockType(Enum):
    """Order Block types."""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"


class OrderBlockStatus(Enum):
    """Order Block status."""
    ACTIVE = "ACTIVE"
    TESTED = "TESTED"
    BROKEN = "BROKEN"
    EXPIRED = "EXPIRED"


@dataclass
class OrderBlock:
    """Order Block data structure."""
    id: str
    ob_type: OrderBlockType
    status: OrderBlockStatus
    
    # Block boundaries
    high_price: float
    low_price: float
    block_size: float
    
    # Formation details
    formation_time: datetime
    formation_bar: ProcessedMarketData  # The bar that created the order block
    breakout_bar: ProcessedMarketData   # The bar that broke structure
    timeframe: str
    
    # Trading levels
    entry_zone_high: float
    entry_zone_low: float
    stop_loss: float
    take_profit: float
    
    # Validation metrics
    volume_profile: float
    structure_strength: float
    confluence_score: float
    
    # Tracking
    test_count: int = 0
    last_test_time: Optional[datetime] = None
    max_penetration: float = 0.0


class OrderBlocksAgent:
    """Real Order Blocks detection and trading agent."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.active_order_blocks: List[OrderBlock] = []
        self.order_block_history: List[OrderBlock] = []
        self.min_block_size = 5.0  # Minimum block size in ticks
        self.max_block_age_hours = 48  # Maximum age before expiring
        self.lookback_periods = 20  # Periods to look back for structure
    
    @timing_decorator
    def detect_order_blocks(self, market_data: List[ProcessedMarketData]) -> List[OrderBlock]:
        """Detect Order Blocks in market data."""
        if len(market_data) < self.lookback_periods:
            return []
        
        detected_blocks = []
        
        # Look for structure breaks that create order blocks
        for i in range(self.lookback_periods, len(market_data)):
            current_bar = market_data[i]
            previous_bars = market_data[i-self.lookback_periods:i]
            
            # Check for bullish order block (break of structure to upside)
            bullish_ob = self._detect_bullish_order_block(previous_bars, current_bar)
            if bullish_ob:
                detected_blocks.append(bullish_ob)
            
            # Check for bearish order block (break of structure to downside)
            bearish_ob = self._detect_bearish_order_block(previous_bars, current_bar)
            if bearish_ob:
                detected_blocks.append(bearish_ob)
        
        self.logger.info(f"Detected {len(detected_blocks)} new Order Blocks")
        return detected_blocks
    
    def _detect_bullish_order_block(
        self, 
        previous_bars: List[ProcessedMarketData], 
        breakout_bar: ProcessedMarketData
    ) -> Optional[OrderBlock]:
        """Detect bullish order block formation."""
        
        # Find the last bearish bar before the breakout
        # This represents institutional accumulation before the move
        
        # Look for the highest high in recent bars
        recent_high = max(bar.high_price for bar in previous_bars[-10:])
        
        # Check if current bar breaks above recent high with strong momentum
        if breakout_bar.close_price <= recent_high:
            return None
        
        # Find the last down bar before the breakout (order block bar)
        order_block_bar = None
        for i in range(len(previous_bars) - 1, -1, -1):
            bar = previous_bars[i]
            if bar.close_price < bar.open_price:  # Bearish bar
                order_block_bar = bar
                break
        
        if not order_block_bar:
            return None
        
        # Validate order block criteria
        block_size = order_block_bar.high_price - order_block_bar.low_price
        if block_size < self.min_block_size:
            return None
        
        # Check volume profile - order block should have decent volume
        avg_volume = sum(bar.volume for bar in previous_bars[-5:]) / 5
        volume_profile = order_block_bar.volume / avg_volume if avg_volume > 0 else 1.0
        
        # Check structure strength
        structure_strength = self._calculate_structure_strength(
            previous_bars, breakout_bar, OrderBlockType.BULLISH
        )
        
        # Calculate confluence score
        confluence_score = self._calculate_confluence_score(
            order_block_bar, breakout_bar, OrderBlockType.BULLISH
        )
        
        # Define trading levels
        entry_zone_high = order_block_bar.high_price
        entry_zone_low = order_block_bar.low_price
        stop_loss = order_block_bar.low_price - (block_size * 0.2)
        take_profit = breakout_bar.high_price + (block_size * 2.0)
        
        ob_id = f"BULL_OB_{order_block_bar.timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        return OrderBlock(
            id=ob_id,
            ob_type=OrderBlockType.BULLISH,
            status=OrderBlockStatus.ACTIVE,
            high_price=order_block_bar.high_price,
            low_price=order_block_bar.low_price,
            block_size=block_size,
            formation_time=order_block_bar.timestamp,
            formation_bar=order_block_bar,
            breakout_bar=breakout_bar,
            timeframe=order_block_bar.timeframe,
            entry_zone_high=entry_zone_high,
            entry_zone_low=entry_zone_low,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume_profile=volume_profile,
            structure_strength=structure_strength,
            confluence_score=confluence_score
        )
    
    def _detect_bearish_order_block(
        self, 
        previous_bars: List[ProcessedMarketData], 
        breakout_bar: ProcessedMarketData
    ) -> Optional[OrderBlock]:
        """Detect bearish order block formation."""
        
        # Find the lowest low in recent bars
        recent_low = min(bar.low_price for bar in previous_bars[-10:])
        
        # Check if current bar breaks below recent low with strong momentum
        if breakout_bar.close_price >= recent_low:
            return None
        
        # Find the last up bar before the breakout (order block bar)
        order_block_bar = None
        for i in range(len(previous_bars) - 1, -1, -1):
            bar = previous_bars[i]
            if bar.close_price > bar.open_price:  # Bullish bar
                order_block_bar = bar
                break
        
        if not order_block_bar:
            return None
        
        # Validate order block criteria
        block_size = order_block_bar.high_price - order_block_bar.low_price
        if block_size < self.min_block_size:
            return None
        
        # Check volume profile
        avg_volume = sum(bar.volume for bar in previous_bars[-5:]) / 5
        volume_profile = order_block_bar.volume / avg_volume if avg_volume > 0 else 1.0
        
        # Check structure strength
        structure_strength = self._calculate_structure_strength(
            previous_bars, breakout_bar, OrderBlockType.BEARISH
        )
        
        # Calculate confluence score
        confluence_score = self._calculate_confluence_score(
            order_block_bar, breakout_bar, OrderBlockType.BEARISH
        )
        
        # Define trading levels
        entry_zone_high = order_block_bar.high_price
        entry_zone_low = order_block_bar.low_price
        stop_loss = order_block_bar.high_price + (block_size * 0.2)
        take_profit = breakout_bar.low_price - (block_size * 2.0)
        
        ob_id = f"BEAR_OB_{order_block_bar.timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        return OrderBlock(
            id=ob_id,
            ob_type=OrderBlockType.BEARISH,
            status=OrderBlockStatus.ACTIVE,
            high_price=order_block_bar.high_price,
            low_price=order_block_bar.low_price,
            block_size=block_size,
            formation_time=order_block_bar.timestamp,
            formation_bar=order_block_bar,
            breakout_bar=breakout_bar,
            timeframe=order_block_bar.timeframe,
            entry_zone_high=entry_zone_high,
            entry_zone_low=entry_zone_low,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume_profile=volume_profile,
            structure_strength=structure_strength,
            confluence_score=confluence_score
        )
    
    def _calculate_structure_strength(
        self, 
        previous_bars: List[ProcessedMarketData], 
        breakout_bar: ProcessedMarketData,
        ob_type: OrderBlockType
    ) -> float:
        """Calculate the strength of the structure break."""
        
        # Measure the momentum of the breakout
        if ob_type == OrderBlockType.BULLISH:
            # For bullish OB, measure upward momentum
            breakout_size = breakout_bar.close_price - breakout_bar.open_price
            recent_range = max(bar.high_price for bar in previous_bars[-5:]) - min(bar.low_price for bar in previous_bars[-5:])
        else:
            # For bearish OB, measure downward momentum
            breakout_size = breakout_bar.open_price - breakout_bar.close_price
            recent_range = max(bar.high_price for bar in previous_bars[-5:]) - min(bar.low_price for bar in previous_bars[-5:])
        
        if recent_range == 0:
            return 0.0
        
        # Normalize strength (0-100)
        strength = min((breakout_size / recent_range) * 100, 100.0)
        return max(strength, 0.0)
    
    def _calculate_confluence_score(
        self, 
        order_block_bar: ProcessedMarketData, 
        breakout_bar: ProcessedMarketData,
        ob_type: OrderBlockType
    ) -> float:
        """Calculate confluence score for order block quality."""
        score = 0.0
        
        # Volume analysis (0-30 points)
        if order_block_bar.volume > 0 and breakout_bar.volume > 0:
            volume_ratio = breakout_bar.volume / order_block_bar.volume
            if volume_ratio > 1.5:  # Breakout has higher volume
                score += 25
            elif volume_ratio > 1.0:
                score += 15
        
        # Bar size analysis (0-25 points)
        ob_bar_size = abs(order_block_bar.close_price - order_block_bar.open_price)
        breakout_bar_size = abs(breakout_bar.close_price - breakout_bar.open_price)
        
        if breakout_bar_size > ob_bar_size * 1.2:  # Strong breakout bar
            score += 20
        elif breakout_bar_size > ob_bar_size:
            score += 10
        
        # Technical indicator confluence (0-25 points)
        if breakout_bar.rsi:
            if ob_type == OrderBlockType.BULLISH and 30 < breakout_bar.rsi < 70:
                score += 15  # Good momentum, not overbought
            elif ob_type == OrderBlockType.BEARISH and 30 < breakout_bar.rsi < 70:
                score += 15  # Good momentum, not oversold
        
        # Moving average alignment (0-20 points)
        if breakout_bar.sma_20 and breakout_bar.sma_50:
            if ob_type == OrderBlockType.BULLISH and breakout_bar.close_price > breakout_bar.sma_20:
                score += 15  # Above short-term MA
            elif ob_type == OrderBlockType.BEARISH and breakout_bar.close_price < breakout_bar.sma_20:
                score += 15  # Below short-term MA
        
        return min(score, 100.0)
    
    @timing_decorator
    def update_order_block_status(self, current_price: float, current_time: datetime) -> None:
        """Update status of active order blocks."""
        
        for ob in self.active_order_blocks[:]:  # Copy to allow modification
            # Check if order block has been broken
            if self._is_order_block_broken(ob, current_price):
                ob.status = OrderBlockStatus.BROKEN
                self.active_order_blocks.remove(ob)
                self.order_block_history.append(ob)
                self.logger.info(f"Order Block {ob.id} has been broken")
                continue
            
            # Check if order block has expired
            age_hours = (current_time - ob.formation_time).total_seconds() / 3600
            if age_hours > self.max_block_age_hours:
                ob.status = OrderBlockStatus.EXPIRED
                self.active_order_blocks.remove(ob)
                self.order_block_history.append(ob)
                self.logger.info(f"Order Block {ob.id} has expired")
                continue
            
            # Check if price is testing the order block
            if self._is_price_testing_order_block(ob, current_price):
                ob.last_test_time = current_time
                ob.test_count += 1
                ob.status = OrderBlockStatus.TESTED
                
                # Track maximum penetration
                penetration = self._calculate_penetration(ob, current_price)
                ob.max_penetration = max(ob.max_penetration, penetration)
    
    def _is_order_block_broken(self, ob: OrderBlock, current_price: float) -> bool:
        """Check if order block has been broken."""
        if ob.ob_type == OrderBlockType.BULLISH:
            # Bullish OB is broken if price closes below the low
            return current_price < ob.low_price
        else:
            # Bearish OB is broken if price closes above the high
            return current_price > ob.high_price
    
    def _is_price_testing_order_block(self, ob: OrderBlock, current_price: float) -> bool:
        """Check if price is testing the order block zone."""
        return ob.entry_zone_low <= current_price <= ob.entry_zone_high
    
    def _calculate_penetration(self, ob: OrderBlock, current_price: float) -> float:
        """Calculate how much price has penetrated into the order block."""
        if ob.ob_type == OrderBlockType.BULLISH:
            if current_price >= ob.entry_zone_high:
                return 0.0
            elif current_price <= ob.entry_zone_low:
                return 100.0
            else:
                penetration = (ob.entry_zone_high - current_price) / ob.block_size
                return penetration * 100.0
        else:  # BEARISH
            if current_price <= ob.entry_zone_low:
                return 0.0
            elif current_price >= ob.entry_zone_high:
                return 100.0
            else:
                penetration = (current_price - ob.entry_zone_low) / ob.block_size
                return penetration * 100.0
    
    def generate_trading_signals(self, current_data: ProcessedMarketData) -> List[TradingDecision]:
        """Generate trading signals based on order block analysis."""
        signals = []

        self.logger.info(f"Checking {len(self.active_order_blocks)} active Order Blocks for trading signals")

        for i, ob in enumerate(self.active_order_blocks):
            should_enter = self._should_enter_trade(ob, current_data)
            self.logger.info(f"OB {i+1}: should_enter={should_enter}, confluence={ob.confluence_score}, tests={ob.test_count}")

            if should_enter:
                signal = self._create_trading_signal(ob, current_data)
                if signal:
                    signals.append(signal)
                    self.logger.info(f"Generated OB trading signal: {signal.action.value} at {signal.entry_price}")

        self.logger.info(f"Generated {len(signals)} OB trading signals from {len(self.active_order_blocks)} active OBs")
        return signals
    
    def _should_enter_trade(self, ob: OrderBlock, current_data: ProcessedMarketData) -> bool:
        """Determine if we should enter a trade based on order block."""
        current_price = current_data.close_price
        
        # Check if price is in entry zone
        in_entry_zone = self._is_price_testing_order_block(ob, current_price)
        if not in_entry_zone:
            return False
        
        # Quality checks (made very lenient for live signal generation)
        quality_checks = [
            ob.confluence_score >= 20,    # Lowered from 30 to 20
            ob.structure_strength >= 15,  # Lowered from 20 to 15
            ob.test_count <= 8,           # Increased from 4 to 8
            ob.volume_profile >= 0.3      # Lowered from 0.5 to 0.3
        ]

        return all(quality_checks)
    
    def _create_trading_signal(self, ob: OrderBlock, current_data: ProcessedMarketData) -> Optional[TradingDecision]:
        """Create trading signal from order block."""
        try:
            action = TradingAction.BUY if ob.ob_type == OrderBlockType.BULLISH else TradingAction.SELL
            
            # Calculate confidence
            base_confidence = (ob.confluence_score + ob.structure_strength) / 200.0
            
            # Adjust for market conditions
            if current_data.rsi:
                if action == TradingAction.BUY and current_data.rsi < 60:
                    base_confidence += 0.1
                elif action == TradingAction.SELL and current_data.rsi > 40:
                    base_confidence += 0.1
            
            confidence = min(base_confidence, 0.95)
            
            # Use middle of entry zone as entry price
            entry_price = (ob.entry_zone_high + ob.entry_zone_low) / 2
            
            reasoning = (
                f"Order Block {ob.ob_type.value} signal detected. "
                f"Block size: {ob.block_size:.2f}, "
                f"Confluence score: {ob.confluence_score:.1f}, "
                f"Structure strength: {ob.structure_strength:.1f}, "
                f"Volume profile: {ob.volume_profile:.2f}"
            )
            
            return TradingDecision(
                action=action,
                confidence=confidence,
                reasoning=reasoning,
                entry_price=entry_price,
                stop_loss=ob.stop_loss,
                take_profit=ob.take_profit,
                strategy_name="OrderBlocks",
                metadata={
                    "ob_id": ob.id,
                    "ob_type": ob.ob_type.value,
                    "block_size": ob.block_size,
                    "confluence_score": ob.confluence_score,
                    "structure_strength": ob.structure_strength,
                    "formation_time": ob.formation_time.isoformat()
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error creating trading signal: {e}")
            return None
    
    def get_active_order_blocks(self) -> List[OrderBlock]:
        """Get list of active order blocks."""
        return self.active_order_blocks.copy()
    
    def get_order_block_statistics(self) -> Dict[str, Any]:
        """Get order block detection and performance statistics."""
        total_obs = len(self.active_order_blocks) + len(self.order_block_history)
        tested_obs = len([ob for ob in self.order_block_history if ob.status == OrderBlockStatus.TESTED])
        
        return {
            "total_detected": total_obs,
            "active_count": len(self.active_order_blocks),
            "tested_count": tested_obs,
            "broken_count": len([ob for ob in self.order_block_history if ob.status == OrderBlockStatus.BROKEN]),
            "expired_count": len([ob for ob in self.order_block_history if ob.status == OrderBlockStatus.EXPIRED]),
            "test_rate": (tested_obs / total_obs * 100) if total_obs > 0 else 0,
            "avg_confluence_score": sum(ob.confluence_score for ob in self.active_order_blocks) / len(self.active_order_blocks) if self.active_order_blocks else 0
        }
