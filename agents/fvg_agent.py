"""
Fair Value Gap (FVG) Detection Agent.
Real implementation for detecting and trading Fair Value Gaps.
"""

from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from core import get_logger, timing_decorator
from agents.market_data_agent import ProcessedMarketData
from llm.models import TradingDecision, TradingAction

logger = get_logger(__name__)


class FVGType(Enum):
    """Fair Value Gap types."""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"


class FVGStatus(Enum):
    """Fair Value Gap status."""
    ACTIVE = "ACTIVE"
    FILLED = "FILLED"
    EXPIRED = "EXPIRED"


@dataclass
class FairValueGap:
    """Fair Value Gap data structure."""
    id: str
    fvg_type: FVGType
    status: FVGStatus
    
    # Gap boundaries
    gap_high: float
    gap_low: float
    gap_size: float
    
    # Formation details
    formation_time: datetime
    formation_bars: List[ProcessedMarketData]  # The 3 bars that formed the gap
    timeframe: str
    
    # Trading levels
    entry_level: float
    stop_loss: float
    take_profit: float
    
    # Validation metrics
    volume_confirmation: bool
    structure_confirmation: bool
    confluence_score: float
    
    # Tracking
    fill_percentage: float = 0.0
    last_test_time: Optional[datetime] = None
    test_count: int = 0


class FVGDetectionAgent:
    """Real Fair Value Gap detection and trading agent."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.active_fvgs: List[FairValueGap] = []
        self.fvg_history: List[FairValueGap] = []
        self.min_gap_size = 2.0  # Minimum gap size in ticks
        self.max_gap_age_hours = 24  # Maximum age before expiring
    
    @timing_decorator
    def detect_fvgs(self, market_data: List[ProcessedMarketData]) -> List[FairValueGap]:
        """Detect Fair Value Gaps in market data."""
        if len(market_data) < 3:
            return []
        
        detected_fvgs = []
        
        # Scan through data looking for 3-bar FVG patterns
        for i in range(len(market_data) - 2):
            bar1 = market_data[i]      # First bar
            bar2 = market_data[i + 1]  # Middle bar (gap bar)
            bar3 = market_data[i + 2]  # Third bar
            
            # Check for bullish FVG
            bullish_fvg = self._check_bullish_fvg(bar1, bar2, bar3)
            if bullish_fvg:
                detected_fvgs.append(bullish_fvg)
            
            # Check for bearish FVG
            bearish_fvg = self._check_bearish_fvg(bar1, bar2, bar3)
            if bearish_fvg:
                detected_fvgs.append(bearish_fvg)
        
        self.logger.info(f"Detected {len(detected_fvgs)} new FVGs")
        return detected_fvgs
    
    def _check_bullish_fvg(
        self, 
        bar1: ProcessedMarketData, 
        bar2: ProcessedMarketData, 
        bar3: ProcessedMarketData
    ) -> Optional[FairValueGap]:
        """Check for bullish Fair Value Gap pattern."""
        
        # Bullish FVG: bar1.high_price < bar3.low_price (gap between them)
        # bar2 is the gap bar that creates the imbalance

        if bar1.high_price < bar3.low_price:
            gap_low = bar1.high_price
            gap_high = bar3.low_price
            gap_size = gap_high - gap_low
            
            # Validate minimum gap size
            if gap_size < self.min_gap_size:
                return None
            
            # Volume confirmation - gap bar should have decent volume
            avg_volume = (bar1.volume + bar2.volume + bar3.volume) / 3
            volume_confirmation = bar2.volume >= avg_volume * 0.8
            
            # Structure confirmation - bars should show momentum
            structure_confirmation = (
                bar3.close_price > bar2.close_price and  # Continuation of bullish move
                bar2.close_price > bar1.close_price      # Initial bullish move
            )
            
            # Calculate confluence score
            confluence_score = self._calculate_confluence_score(
                [bar1, bar2, bar3], FVGType.BULLISH
            )
            
            # Generate trading levels
            entry_level = gap_low + (gap_size * 0.5)  # Middle of gap
            stop_loss = gap_low - (gap_size * 0.5)    # Below gap
            take_profit = gap_high + (gap_size * 2.0)  # 2:1 RR
            
            fvg_id = f"BULL_FVG_{bar3.timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            return FairValueGap(
                id=fvg_id,
                fvg_type=FVGType.BULLISH,
                status=FVGStatus.ACTIVE,
                gap_high=gap_high,
                gap_low=gap_low,
                gap_size=gap_size,
                formation_time=bar3.timestamp,
                formation_bars=[bar1, bar2, bar3],
                timeframe=bar3.timeframe,
                entry_level=entry_level,
                stop_loss=stop_loss,
                take_profit=take_profit,
                volume_confirmation=volume_confirmation,
                structure_confirmation=structure_confirmation,
                confluence_score=confluence_score
            )
        
        return None
    
    def _check_bearish_fvg(
        self, 
        bar1: ProcessedMarketData, 
        bar2: ProcessedMarketData, 
        bar3: ProcessedMarketData
    ) -> Optional[FairValueGap]:
        """Check for bearish Fair Value Gap pattern."""
        
        # Bearish FVG: bar1.low_price > bar3.high_price (gap between them)
        # bar2 is the gap bar that creates the imbalance

        if bar1.low_price > bar3.high_price:
            gap_high = bar1.low_price
            gap_low = bar3.high_price
            gap_size = gap_high - gap_low
            
            # Validate minimum gap size
            if gap_size < self.min_gap_size:
                return None
            
            # Volume confirmation
            avg_volume = (bar1.volume + bar2.volume + bar3.volume) / 3
            volume_confirmation = bar2.volume >= avg_volume * 0.8
            
            # Structure confirmation
            structure_confirmation = (
                bar3.close_price < bar2.close_price and  # Continuation of bearish move
                bar2.close_price < bar1.close_price      # Initial bearish move
            )
            
            # Calculate confluence score
            confluence_score = self._calculate_confluence_score(
                [bar1, bar2, bar3], FVGType.BEARISH
            )
            
            # Generate trading levels
            entry_level = gap_high - (gap_size * 0.5)  # Middle of gap
            stop_loss = gap_high + (gap_size * 0.5)    # Above gap
            take_profit = gap_low - (gap_size * 2.0)   # 2:1 RR
            
            fvg_id = f"BEAR_FVG_{bar3.timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            return FairValueGap(
                id=fvg_id,
                fvg_type=FVGType.BEARISH,
                status=FVGStatus.ACTIVE,
                gap_high=gap_high,
                gap_low=gap_low,
                gap_size=gap_size,
                formation_time=bar3.timestamp,
                formation_bars=[bar1, bar2, bar3],
                timeframe=bar3.timeframe,
                entry_level=entry_level,
                stop_loss=stop_loss,
                take_profit=take_profit,
                volume_confirmation=volume_confirmation,
                structure_confirmation=structure_confirmation,
                confluence_score=confluence_score
            )
        
        return None
    
    def _calculate_confluence_score(
        self, 
        bars: List[ProcessedMarketData], 
        fvg_type: FVGType
    ) -> float:
        """Calculate confluence score for FVG quality."""
        score = 0.0
        
        # Volume analysis (0-30 points)
        volumes = [bar.volume for bar in bars]
        avg_volume = sum(volumes) / len(volumes)
        if volumes[1] > avg_volume * 1.2:  # Gap bar has high volume
            score += 20
        elif volumes[1] > avg_volume:
            score += 10
        
        # Price momentum (0-25 points)
        if fvg_type == FVGType.BULLISH:
            if bars[2].close_price > bars[1].close_price > bars[0].close_price:
                score += 25
            elif bars[2].close_price > bars[0].close_price:
                score += 15
        else:  # BEARISH
            if bars[2].close_price < bars[1].close_price < bars[0].close_price:
                score += 25
            elif bars[2].close_price < bars[0].close_price:
                score += 15
        
        # Technical indicator confluence (0-25 points)
        latest_bar = bars[-1]
        if latest_bar.rsi:
            if fvg_type == FVGType.BULLISH and latest_bar.rsi < 70:
                score += 15  # Not overbought
            elif fvg_type == FVGType.BEARISH and latest_bar.rsi > 30:
                score += 15  # Not oversold
        
        # Moving average alignment (0-20 points)
        if latest_bar.sma_20 and latest_bar.sma_50:
            if fvg_type == FVGType.BULLISH and latest_bar.sma_20 > latest_bar.sma_50:
                score += 20  # Bullish MA alignment
            elif fvg_type == FVGType.BEARISH and latest_bar.sma_20 < latest_bar.sma_50:
                score += 20  # Bearish MA alignment
        
        return min(score, 100.0)  # Cap at 100
    
    @timing_decorator
    def update_fvg_status(self, current_price: float, current_time: datetime) -> None:
        """Update status of active FVGs based on current market conditions."""
        
        for fvg in self.active_fvgs[:]:  # Copy list to allow modification
            # Check if FVG has been filled
            fill_percentage = self._calculate_fill_percentage(fvg, current_price)
            fvg.fill_percentage = fill_percentage
            
            if fill_percentage >= 100.0:
                fvg.status = FVGStatus.FILLED
                self.active_fvgs.remove(fvg)
                self.fvg_history.append(fvg)
                self.logger.info(f"FVG {fvg.id} has been filled")
                continue
            
            # Check if FVG has expired
            age_hours = (current_time - fvg.formation_time).total_seconds() / 3600
            if age_hours > self.max_gap_age_hours:
                fvg.status = FVGStatus.EXPIRED
                self.active_fvgs.remove(fvg)
                self.fvg_history.append(fvg)
                self.logger.info(f"FVG {fvg.id} has expired")
                continue
            
            # Check if price is testing the FVG
            if self._is_price_testing_fvg(fvg, current_price):
                fvg.last_test_time = current_time
                fvg.test_count += 1
    
    def _calculate_fill_percentage(self, fvg: FairValueGap, current_price: float) -> float:
        """Calculate how much of the FVG has been filled."""
        if fvg.fvg_type == FVGType.BULLISH:
            # For bullish FVG, filling means price moving down into the gap
            if current_price <= fvg.gap_low:
                return 100.0
            elif current_price >= fvg.gap_high:
                return 0.0
            else:
                filled_amount = fvg.gap_high - current_price
                return (filled_amount / fvg.gap_size) * 100.0
        else:  # BEARISH
            # For bearish FVG, filling means price moving up into the gap
            if current_price >= fvg.gap_high:
                return 100.0
            elif current_price <= fvg.gap_low:
                return 0.0
            else:
                filled_amount = current_price - fvg.gap_low
                return (filled_amount / fvg.gap_size) * 100.0
    
    def _is_price_testing_fvg(self, fvg: FairValueGap, current_price: float) -> bool:
        """Check if current price is testing the FVG zone."""
        buffer = fvg.gap_size * 0.1  # 10% buffer
        
        return (fvg.gap_low - buffer) <= current_price <= (fvg.gap_high + buffer)
    
    def generate_trading_signals(self, current_data: ProcessedMarketData) -> List[TradingDecision]:
        """Generate trading signals based on FVG analysis."""
        signals = []

        self.logger.info(f"Checking {len(self.active_fvgs)} active FVGs for trading signals")

        for i, fvg in enumerate(self.active_fvgs):
            # Check if price is near entry level
            should_enter = self._should_enter_trade(fvg, current_data)
            self.logger.info(f"FVG {i+1}: should_enter={should_enter}, confluence={fvg.confluence_score}, test_count={fvg.test_count}")

            if should_enter:
                signal = self._create_trading_signal(fvg, current_data)
                if signal:
                    signals.append(signal)
                    self.logger.info(f"Generated FVG trading signal: {signal.action.value} at {signal.entry_price}")

        self.logger.info(f"Generated {len(signals)} FVG trading signals from {len(self.active_fvgs)} active FVGs")
        return signals
    
    def _should_enter_trade(self, fvg: FairValueGap, current_data: ProcessedMarketData) -> bool:
        """Determine if we should enter a trade based on FVG."""
        current_price = current_data.close_price

        # Check if price is within entry zone (made much more lenient for live trading)
        entry_buffer = max(fvg.gap_size * 2.0, 50.0)  # 200% buffer or 50 points minimum
        near_entry = abs(current_price - fvg.entry_level) <= entry_buffer

        self.logger.info(f"FVG Entry Check: price={current_price:.2f}, entry={fvg.entry_level:.2f}, buffer={entry_buffer:.2f}, near_entry={near_entry}")

        if not near_entry:
            return False

        # Additional confluence checks (made very lenient for live signal generation)
        confluence_score_ok = fvg.confluence_score >= 25  # Lowered from 40 to 25
        test_count_ok = fvg.test_count <= 10             # Increased from 5 to 10

        self.logger.info(f"FVG Confluence Check: score={fvg.confluence_score} (>= 40: {confluence_score_ok}), tests={fvg.test_count} (<= 5: {test_count_ok})")

        confluence_checks = [confluence_score_ok, test_count_ok]

        return all(confluence_checks)
    
    def _create_trading_signal(self, fvg: FairValueGap, current_data: ProcessedMarketData) -> Optional[TradingDecision]:
        """Create trading signal from FVG."""
        try:
            action = TradingAction.BUY if fvg.fvg_type == FVGType.BULLISH else TradingAction.SELL
            
            # Calculate confidence based on confluence score and market conditions
            base_confidence = fvg.confluence_score / 100.0
            
            # Adjust confidence based on current market conditions
            if current_data.rsi:
                if action == TradingAction.BUY and current_data.rsi < 50:
                    base_confidence += 0.1
                elif action == TradingAction.SELL and current_data.rsi > 50:
                    base_confidence += 0.1
            
            confidence = min(base_confidence, 0.95)  # Cap at 95%
            
            reasoning = (
                f"FVG {fvg.fvg_type.value} signal detected. "
                f"Gap size: {fvg.gap_size:.2f}, "
                f"Confluence score: {fvg.confluence_score:.1f}, "
                f"Volume confirmed: {fvg.volume_confirmation}, "
                f"Structure confirmed: {fvg.structure_confirmation}"
            )
            
            return TradingDecision(
                action=action,
                confidence=confidence,
                reasoning=reasoning,
                entry_price=fvg.entry_level,
                stop_loss=fvg.stop_loss,
                take_profit=fvg.take_profit,
                strategy_name="FVG",
                metadata={
                    "fvg_id": fvg.id,
                    "fvg_type": fvg.fvg_type.value,
                    "gap_size": fvg.gap_size,
                    "confluence_score": fvg.confluence_score,
                    "formation_time": fvg.formation_time.isoformat()
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error creating trading signal: {e}")
            return None
    
    def get_active_fvgs(self) -> List[FairValueGap]:
        """Get list of active FVGs."""
        return self.active_fvgs.copy()
    
    def get_fvg_statistics(self) -> Dict[str, Any]:
        """Get FVG detection and performance statistics."""
        total_fvgs = len(self.active_fvgs) + len(self.fvg_history)
        filled_fvgs = len([fvg for fvg in self.fvg_history if fvg.status == FVGStatus.FILLED])
        
        return {
            "total_detected": total_fvgs,
            "active_count": len(self.active_fvgs),
            "filled_count": filled_fvgs,
            "expired_count": len(self.fvg_history) - filled_fvgs,
            "fill_rate": (filled_fvgs / total_fvgs * 100) if total_fvgs > 0 else 0,
            "avg_confluence_score": sum(fvg.confluence_score for fvg in self.active_fvgs) / len(self.active_fvgs) if self.active_fvgs else 0
        }
