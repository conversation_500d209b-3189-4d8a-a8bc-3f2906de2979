# Trading Agent - Agentic Trading System

An advanced trading agent built with <PERSON><PERSON>hai<PERSON>, LangGraph, and <PERSON>wen LLM for automated trading using TopStep API.

## Features

- **Agentic Workflow**: Multi-agent system orchestrated with <PERSON><PERSON><PERSON>h
- **Advanced Trading Strategies**: FVG, IFVG, Order Blocks, Liquidity Sweeps, Market Structure Analysis
- **AI-Powered Decisions**: Qwen LLM for intelligent trading decisions
- **Real-time Data**: TopStep API integration for live market data and execution
- **Sentiment Analysis**: Market sentiment analysis from multiple sources
- **Risk Management**: Comprehensive risk controls and position management
- **Monitoring**: Real-time monitoring and alerting system

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Market Data   │    │   Sentiment     │    │   Strategy      │
│   Processing    │    │   Analysis      │    │   Agents        │
│   Agent         │    │   Agent         │    │   (FVG, OB,     │
└─────────────────┘    └─────────────────┘    │   LS, MS)       │
         │                       │             └─────────────────┘
         │                       │                      │
         └───────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Decision      │
                    │   Making Agent  │
                    │   (Qwen LLM)    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Risk          │
                    │   Management    │
                    │   System        │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Execution     │
                    │   Agent         │
                    │   (TopStep API) │
                    └─────────────────┘
```

## Project Structure

```
trade_agent/
├── agents/                 # Individual agent implementations
│   ├── market_data/       # Market data processing
│   ├── strategies/        # Trading strategy agents
│   ├── sentiment/         # Sentiment analysis
│   ├── decision/          # Decision making with <PERSON>wen
│   ├── risk/              # Risk management
│   └── execution/         # Trade execution
├── api/                   # TopStep API integration
│   ├── client.py          # API client
│   ├── websocket.py       # Real-time data
│   └── models.py          # Data models
├── core/                  # Core system components
│   ├── config.py          # Configuration
│   ├── database.py        # Database connections
│   ├── logging.py         # Logging setup
│   └── utils.py           # Utility functions
├── data/                  # Data storage and processing
│   ├── storage/           # Data storage layer
│   ├── indicators/        # Technical indicators
│   └── patterns/          # Pattern recognition
├── workflows/             # LangGraph workflows
│   ├── trading_flow.py    # Main trading workflow
│   └── monitoring_flow.py # Monitoring workflow
├── tests/                 # Test suite
├── docs/                  # Documentation
├── requirements.txt       # Dependencies
├── docker-compose.yml     # Docker setup
└── main.py               # Application entry point
```

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository>
   cd trade_agent
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your TopStep API credentials and other settings
   ```

3. **Run the Agent**
   ```bash
   python main.py
   ```

## Configuration

Create a `.env` file with the following variables:

```env
# TopStep API Configuration
TOPSTEP_API_KEY=your_api_key
TOPSTEP_USERNAME=your_username
TOPSTEP_ENVIRONMENT=demo  # or 'live'

# Qwen LLM Configuration
QWEN_API_KEY=your_qwen_api_key
QWEN_MODEL=qwen-turbo

# Database Configuration
DATABASE_URL=sqlite:///trading_agent.db

# Risk Management
MAX_DAILY_LOSS=1000
MAX_POSITION_SIZE=10
DEFAULT_STOP_LOSS_PCT=2.0

# Monitoring
ENABLE_MONITORING=true
ALERT_WEBHOOK_URL=your_webhook_url
```

## Trading Strategies

### Fair Value Gaps (FVG)
- Detects price imbalances in 3-candle patterns
- Validates with volume and market structure
- Generates high-probability entry signals

### Inverse Fair Value Gaps (IFVG)
- Identifies FVG reversals and rejections
- Monitors existing gap zones for failure
- Provides counter-trend opportunities

### Order Blocks
- Finds institutional order zones
- Identifies high-volume consolidation areas
- Provides precision entry points

### Liquidity Sweeps
- Detects false breakouts and stop hunts
- Identifies liquidity pool formations
- Generates reversal signals post-sweep

### Market Structure Analysis
- Comprehensive trend analysis
- Support/resistance identification
- Market phase classification

## Development

### Running Tests
```bash
pytest tests/
```

### Code Quality
```bash
black .
flake8 .
mypy .
```

### Docker Development
```bash
docker-compose up -d
```

## Monitoring

The system includes comprehensive monitoring:

- **Performance Metrics**: Win rate, profit factor, Sharpe ratio
- **Risk Metrics**: Drawdown, position sizes, exposure
- **System Metrics**: API latency, uptime, error rates
- **Alerts**: Risk breaches, system errors, performance issues

## Documentation

- [TopStep API Documentation](docs/topstep_api_documentation.md)
- [Development Strategy](docs/development_strategy.md)
- [Agent Architecture](docs/agent_architecture.md)
- [Trading Strategies](docs/trading_strategies.md)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This trading agent is for educational and research purposes. Trading involves substantial risk of loss. Past performance does not guarantee future results. Use at your own risk.
