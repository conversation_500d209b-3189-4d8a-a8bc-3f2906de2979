#!/usr/bin/env python3
"""
Main Trading System Entry Point.
Production-ready trading system with full workflow.
"""

import os
import sys
import asyncio
import argparse
import signal
from pathlib import Path
from datetime import datetime, timezone

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

class TradingSystemManager:
    """Main trading system manager."""
    
    def __init__(self, mode="development"):
        self.mode = mode
        self.running = False
        self.workflow = None
        
    async def initialize(self):
        """Initialize the trading system."""
        print(f"🚀 Initializing Trading System ({self.mode} mode)")
        print("=" * 60)
        
        # Initialize core systems
        from core import configure_logging, init_database, get_logger
        
        configure_logging()
        self.logger = get_logger("TradingSystem")
        
        await init_database()
        self.logger.info(f"Trading system initialized in {self.mode} mode")
        
        # Initialize API clients
        from api import TopStepClient
        from llm import QwenClient
        
        self.topstep_client = TopStepClient(preferred_account_type="PRACTICE")
        self.qwen_client = QwenClient()
        
        # Initialize workflow
        from workflow import TradingWorkflow
        
        self.workflow = TradingWorkflow(
            topstep_client=self.topstep_client,
            qwen_client=self.qwen_client
        )
        
        print("✅ Trading system initialized successfully")
    
    async def run_trading_loop(self):
        """Run the main trading loop."""
        self.running = True
        cycle_count = 0
        
        print("🔄 Starting trading loop...")
        
        try:
            while self.running:
                cycle_count += 1
                self.logger.info(f"Starting trading cycle {cycle_count}")
                
                # Run trading workflow
                result = await self.workflow.run_trading_cycle(
                    symbol="ES"  # Start with ES futures
                )
                
                if result["success"]:
                    self.logger.info(
                        f"Trading cycle {cycle_count} completed",
                        should_trade=result["should_trade"],
                        signals=result["signals_detected"]
                    )
                else:
                    self.logger.error(
                        f"Trading cycle {cycle_count} failed",
                        error=result.get("error_message")
                    )
                
                # Wait before next cycle (60 seconds)
                if self.running:
                    await asyncio.sleep(60)
                
        except KeyboardInterrupt:
            print("\n🛑 Shutdown signal received")
        except Exception as e:
            self.logger.error(f"Trading loop error: {e}", exc_info=True)
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown the trading system gracefully."""
        print("🛑 Shutting down trading system...")
        self.running = False
        
        if self.topstep_client:
            await self.topstep_client.close()
        
        print("✅ Trading system shutdown complete")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        print(f"\n🛑 Received signal {signum}")
        self.running = False

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Trading System")
    parser.add_argument("--mode", choices=["development", "production"], 
                       default="development", help="Running mode")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="Log level")
    
    args = parser.parse_args()
    
    # Set log level
    os.environ["LOG_LEVEL"] = args.log_level
    
    # Create and run trading system
    system = TradingSystemManager(mode=args.mode)
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, system.signal_handler)
    signal.signal(signal.SIGTERM, system.signal_handler)
    
    try:
        await system.initialize()
        await system.run_trading_loop()
    except Exception as e:
        print(f"❌ System error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
