"""
Global Settings Manager for Real-time Configuration Updates
"""

import asyncio
from typing import Dict, Any, List, Callable
from dataclasses import dataclass
from core.config import Settings
from core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class SystemMessage:
    """System behavior message."""
    timestamp: str
    component: str
    message: str
    level: str  # INFO, WARNING, ERROR
    metadata: Dict[str, Any] = None


class SettingsManager:
    """Global settings manager for real-time configuration updates."""
    
    def __init__(self):
        self.settings = Settings()
        self._callbacks: Dict[str, List[Callable]] = {}
        self._system_messages: List[SystemMessage] = []
        self._max_messages = 100
        
    def get_confidence_threshold(self) -> float:
        """Get current confidence threshold."""
        return self.settings.trading.min_confidence_threshold

    def set_confidence_threshold(self, threshold: float) -> None:
        """Set confidence threshold and notify all components."""
        if 0.0 <= threshold <= 1.0:
            old_threshold = self.settings.trading.min_confidence_threshold
            self.settings.trading.min_confidence_threshold = threshold
            
            # Add system message
            self.add_system_message(
                component="SettingsManager",
                message=f"Confidence threshold changed from {old_threshold:.1%} to {threshold:.1%}",
                level="INFO"
            )
            
            # Notify all registered callbacks
            self._notify_callbacks("confidence_threshold", threshold)
            
            logger.info(f"Confidence threshold updated to {threshold:.1%}")
        else:
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")
    
    def get_selected_contracts(self) -> List[str]:
        """Get currently selected contracts."""
        return self.settings.trading.contracts

    def set_selected_contracts(self, contracts: List[str]) -> None:
        """Set selected contracts and notify all components."""
        old_contracts = self.settings.trading.contracts.copy()
        self.settings.trading.contracts = contracts
        
        # Add system message
        self.add_system_message(
            component="SettingsManager",
            message=f"Trading contracts changed from {old_contracts} to {contracts}",
            level="INFO"
        )
        
        # Notify all registered callbacks
        self._notify_callbacks("contracts", contracts)
        
        logger.info(f"Trading contracts updated to {contracts}")
    
    def add_system_message(self, component: str, message: str, level: str = "INFO", metadata: Dict[str, Any] = None) -> None:
        """Add a system behavior message."""
        from datetime import datetime
        
        system_message = SystemMessage(
            timestamp=datetime.now().strftime("%I:%M:%S %p"),
            component=component,
            message=message,
            level=level,
            metadata=metadata or {}
        )
        
        self._system_messages.append(system_message)
        
        # Keep only the latest messages
        if len(self._system_messages) > self._max_messages:
            self._system_messages = self._system_messages[-self._max_messages:]
        
        logger.info(f"[{component}] {message}")
    
    def get_system_messages(self, limit: int = 50) -> List[SystemMessage]:
        """Get recent system messages."""
        return self._system_messages[-limit:]
    
    def register_callback(self, setting_name: str, callback: Callable) -> None:
        """Register a callback for setting changes."""
        if setting_name not in self._callbacks:
            self._callbacks[setting_name] = []
        self._callbacks[setting_name].append(callback)
    
    def _notify_callbacks(self, setting_name: str, value: Any) -> None:
        """Notify all callbacks for a setting change."""
        if setting_name in self._callbacks:
            for callback in self._callbacks[setting_name]:
                try:
                    callback(value)
                except Exception as e:
                    logger.error(f"Error in callback for {setting_name}: {e}")
    
    def get_max_daily_loss(self) -> float:
        """Get current max daily loss."""
        return self.settings.risk.max_daily_loss

    def set_max_daily_loss(self, amount: float) -> None:
        """Set max daily loss and notify all components."""
        if amount > 0:
            old_amount = self.settings.risk.max_daily_loss
            self.settings.risk.max_daily_loss = amount

            self.add_system_message(
                component="SettingsManager",
                message=f"Max daily loss changed from ${old_amount:.2f} to ${amount:.2f}",
                level="INFO"
            )

            self._notify_callbacks("max_daily_loss", amount)
            logger.info(f"Max daily loss updated to ${amount:.2f}")
        else:
            raise ValueError("Max daily loss must be greater than 0")

    def get_max_position_size(self) -> int:
        """Get current max position size."""
        return self.settings.risk.max_position_size

    def set_max_position_size(self, size: int) -> None:
        """Set max position size and notify all components."""
        if size > 0:
            old_size = self.settings.risk.max_position_size
            self.settings.risk.max_position_size = size

            self.add_system_message(
                component="SettingsManager",
                message=f"Max position size changed from {old_size} to {size} contracts",
                level="INFO"
            )

            self._notify_callbacks("max_position_size", size)
            logger.info(f"Max position size updated to {size} contracts")
        else:
            raise ValueError("Max position size must be greater than 0")

    def get_stop_loss_pct(self) -> float:
        """Get current stop loss percentage."""
        return self.settings.risk.default_stop_loss_pct

    def set_stop_loss_pct(self, percentage: float) -> None:
        """Set stop loss percentage and notify all components."""
        if 0.1 <= percentage <= 10.0:
            old_pct = self.settings.risk.default_stop_loss_pct
            self.settings.risk.default_stop_loss_pct = percentage

            self.add_system_message(
                component="SettingsManager",
                message=f"Stop loss percentage changed from {old_pct:.1f}% to {percentage:.1f}%",
                level="INFO"
            )

            self._notify_callbacks("stop_loss_pct", percentage)
            logger.info(f"Stop loss percentage updated to {percentage:.1f}%")
        else:
            raise ValueError("Stop loss percentage must be between 0.1% and 10.0%")

    def get_all_settings(self) -> Dict[str, Any]:
        """Get all current settings."""
        return {
            "confidence_threshold": self.settings.trading.min_confidence_threshold,
            "contracts": self.settings.trading.contracts,
            "max_position_size": self.settings.risk.max_position_size,
            "max_daily_loss": self.settings.risk.max_daily_loss,
            "default_stop_loss_pct": self.settings.risk.default_stop_loss_pct,
        }


# Global settings manager instance
settings_manager = SettingsManager()
