"""
Database configuration and models for the trading agent.
Handles SQLAlchemy setup, connection management, and base models.
"""

import asyncio
from datetime import datetime
from typing import Optional, Dict, Any, AsyncGenerator
from contextlib import asynccontextmanager

from sqlalchemy import (
    create_engine, Column, Integer, String, Float, DateTime,
    Boolean, Text, JSON, BigInteger, Index, Numeric
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .config import get_settings
from .logging import get_logger

logger = get_logger(__name__)

# Base class for all database models
Base = declarative_base()


class TimestampMixin:
    """Mixin to add timestamp fields to models."""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class MarketData(Base, TimestampMixin):
    """Market data storage model."""
    __tablename__ = "market_data"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    contract_id = Column(String(50), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(BigInteger, nullable=False)
    timeframe = Column(String(10), nullable=False)  # 1m, 5m, 1h, 1d, etc.
    
    __table_args__ = (
        Index('idx_contract_timestamp', 'contract_id', 'timestamp'),
        Index('idx_contract_timeframe', 'contract_id', 'timeframe'),
    )


class TechnicalIndicators(Base, TimestampMixin):
    """Technical indicators storage model."""
    __tablename__ = "technical_indicators"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    contract_id = Column(String(50), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    timeframe = Column(String(10), nullable=False)
    indicator_name = Column(String(50), nullable=False)
    value = Column(Float, nullable=True)
    indicator_metadata = Column(JSON, nullable=True)
    
    __table_args__ = (
        Index('idx_indicator_lookup', 'contract_id', 'indicator_name', 'timestamp'),
    )


class TradingSignals(Base, TimestampMixin):
    """Trading signals storage model."""
    __tablename__ = "trading_signals"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    strategy_name = Column(String(50), nullable=False, index=True)
    contract_id = Column(String(50), nullable=False, index=True)
    signal_type = Column(String(20), nullable=False)  # BUY, SELL, HOLD
    confidence = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    signal_metadata = Column(JSON, nullable=True)
    processed = Column(Boolean, default=False, nullable=False)
    
    __table_args__ = (
        Index('idx_signal_processing', 'processed', 'created_at'),
    )


class Orders(Base, TimestampMixin):
    """Orders storage model."""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    external_order_id = Column(BigInteger, nullable=True, index=True)  # TopStep order ID
    account_id = Column(Integer, nullable=False)
    contract_id = Column(String(50), nullable=False, index=True)
    order_type = Column(String(20), nullable=False)  # MARKET, LIMIT, STOP, etc.
    side = Column(String(10), nullable=False)  # BUY, SELL
    size = Column(Integer, nullable=False)
    limit_price = Column(Float, nullable=True)
    stop_price = Column(Float, nullable=True)
    status = Column(String(20), nullable=False, index=True)  # PENDING, FILLED, CANCELLED, etc.
    filled_size = Column(Integer, default=0, nullable=False)
    filled_price = Column(Float, nullable=True)
    custom_tag = Column(String(100), nullable=True)
    strategy_name = Column(String(50), nullable=True, index=True)
    order_metadata = Column(JSON, nullable=True)


class Positions(Base, TimestampMixin):
    """Positions storage model."""
    __tablename__ = "positions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    external_position_id = Column(BigInteger, nullable=True, index=True)  # TopStep position ID
    account_id = Column(Integer, nullable=False)
    contract_id = Column(String(50), nullable=False, index=True)
    position_type = Column(String(10), nullable=False)  # LONG, SHORT
    size = Column(Integer, nullable=False)
    average_price = Column(Float, nullable=False)
    unrealized_pnl = Column(Float, default=0.0, nullable=False)
    realized_pnl = Column(Float, default=0.0, nullable=False)
    is_open = Column(Boolean, default=True, nullable=False, index=True)
    strategy_name = Column(String(50), nullable=True, index=True)
    position_metadata = Column(JSON, nullable=True)


class Trades(Base, TimestampMixin):
    """Trades storage model."""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    external_trade_id = Column(BigInteger, nullable=True, index=True)  # TopStep trade ID
    order_id = Column(BigInteger, nullable=False, index=True)
    account_id = Column(Integer, nullable=False)
    contract_id = Column(String(50), nullable=False, index=True)
    side = Column(String(10), nullable=False)  # BUY, SELL
    size = Column(Integer, nullable=False)
    price = Column(Float, nullable=False)
    fees = Column(Float, default=0.0, nullable=False)
    profit_loss = Column(Float, nullable=True)
    strategy_name = Column(String(50), nullable=True, index=True)
    trade_metadata = Column(JSON, nullable=True)


class PerformanceMetrics(Base, TimestampMixin):
    """Performance metrics storage model."""
    __tablename__ = "performance_metrics"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    metric_name = Column(String(50), nullable=False, index=True)
    value = Column(Float, nullable=False)
    period = Column(String(20), nullable=False)  # daily, weekly, monthly
    strategy_name = Column(String(50), nullable=True, index=True)
    metric_metadata = Column(JSON, nullable=True)
    
    __table_args__ = (
        Index('idx_metric_period', 'metric_name', 'period', 'created_at'),
    )


class SystemEvents(Base, TimestampMixin):
    """System events and errors storage model."""
    __tablename__ = "system_events"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    event_type = Column(String(50), nullable=False, index=True)
    severity = Column(String(20), nullable=False, index=True)  # INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False)
    component = Column(String(50), nullable=True, index=True)
    event_metadata = Column(JSON, nullable=True)



class DatabaseManager:
    """Database connection and session management."""
    
    def __init__(self):
        self.settings = get_settings()
        self.engine = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        self._initialized = False
    
    def initialize(self) -> None:
        """Initialize database connections."""
        if self._initialized:
            return
        
        logger.info("Initializing database connections")
        
        # Create synchronous engine
        if "sqlite" in self.settings.database.url:
            # SQLite-specific configuration (no pool parameters)
            self.engine = create_engine(
                self.settings.database.url,
                echo=self.settings.database.echo,
                poolclass=StaticPool,
                connect_args={"check_same_thread": False}
            )
        else:
            # PostgreSQL/other database configuration
            self.engine = create_engine(
                self.settings.database.url,
                pool_size=self.settings.database.pool_size,
                max_overflow=self.settings.database.max_overflow,
                pool_timeout=self.settings.database.pool_timeout,
                echo=self.settings.database.echo
            )
        
        # Create async engine for async operations (with error handling)
        try:
            async_url = self.settings.database.url.replace("sqlite://", "sqlite+aiosqlite://")
            if "postgresql" in self.settings.database.url:
                async_url = self.settings.database.url.replace("postgresql://", "postgresql+asyncpg://")

            if "sqlite" in async_url:
                # SQLite async configuration (no pool parameters)
                self.async_engine = create_async_engine(
                    async_url,
                    echo=self.settings.database.echo
                )
            else:
                # PostgreSQL/other database async configuration
                self.async_engine = create_async_engine(
                    async_url,
                    pool_size=self.settings.database.pool_size,
                    max_overflow=self.settings.database.max_overflow,
                    pool_timeout=self.settings.database.pool_timeout,
                    echo=self.settings.database.echo
                )
        except Exception as e:
            logger.warning(f"Failed to create async engine: {e}. Async operations will be disabled.")
            self.async_engine = None
        
        # Create session factories
        self.session_factory = sessionmaker(bind=self.engine)

        if self.async_engine:
            self.async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
        else:
            self.async_session_factory = None
            logger.warning("Async session factory not created due to async engine failure")
        
        self._initialized = True
        logger.info("Database connections initialized successfully")
    
    def create_tables(self) -> None:
        """Create all database tables."""
        if not self._initialized:
            self.initialize()
        
        logger.info("Creating database tables")
        Base.metadata.create_all(bind=self.engine)
        logger.info("Database tables created successfully")
    
    async def create_tables_async(self) -> None:
        """Create all database tables asynchronously."""
        if not self._initialized:
            self.initialize()
        
        logger.info("Creating database tables (async)")
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully (async)")
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get an async database session."""
        if not self._initialized:
            self.initialize()

        if not self.async_session_factory:
            raise RuntimeError("Async session factory not available. Check async engine initialization.")

        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    def get_session(self) -> Session:
        """Get a synchronous database session."""
        if not self._initialized:
            self.initialize()
        
        return self.session_factory()
    
    async def close(self) -> None:
        """Close database connections."""
        if self.async_engine:
            await self.async_engine.dispose()
        if self.engine:
            self.engine.dispose()
        
        logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    return db_manager


async def init_database() -> None:
    """Initialize the database and create tables."""
    db_manager.initialize()
    await db_manager.create_tables_async()


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get a database session context manager."""
    async with db_manager.get_async_session() as session:
        yield session
