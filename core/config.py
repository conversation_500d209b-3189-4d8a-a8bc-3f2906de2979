"""
Configuration management for the trading agent.
Handles environment variables, validation, and settings.
"""

import os
from enum import Enum
from pathlib import Path
from typing import Optional, List, Dict, Any
from pydantic import Field, validator, SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    """Environment types for the trading agent."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class TopStepEnvironment(str, Enum):
    """TopStep API environment types."""
    DEMO = "demo"
    LIVE = "live"


class LogLevel(str, Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="DB_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )
    
    url: str = Field(default="sqlite:///trading_agent.db", description="Database URL")
    pool_size: int = Field(default=10, description="Connection pool size")
    max_overflow: int = Field(default=20, description="Max overflow connections")
    pool_timeout: int = Field(default=30, description="Pool timeout in seconds")
    echo: bool = Field(default=False, description="Echo SQL queries")


class RedisConfig(BaseSettings):
    """Redis configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="REDIS_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )
    
    host: str = Field(default="localhost", description="Redis host")
    port: int = Field(default=6379, description="Redis port")
    db: int = Field(default=0, description="Redis database number")
    password: Optional[SecretStr] = Field(default=None, description="Redis password")
    max_connections: int = Field(default=10, description="Max connections")
    socket_timeout: int = Field(default=5, description="Socket timeout in seconds")


class TopStepConfig(BaseSettings):
    """TopStep API configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="TOPSTEP_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )

    username: str = Field(..., description="TopStep username")
    api_key: SecretStr = Field(..., description="TopStep API key")
    environment: TopStepEnvironment = Field(default=TopStepEnvironment.LIVE, description="API environment")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, description="Delay between retries in seconds")
    
    @property
    def base_url(self) -> str:
        """Get the base URL for the TopStep API."""
        # Use the working TopStep API endpoints
        if self.environment == TopStepEnvironment.DEMO:
            return "https://api-demo.topstepx.com"
        return "https://api.topstepx.com"
    
    @property
    def websocket_url(self) -> str:
        """Get the WebSocket URL for real-time data."""
        # Use the working TopStep WebSocket endpoints
        if self.environment == TopStepEnvironment.DEMO:
            return "wss://ws-demo.topstepx.com"
        return "wss://ws.topstepx.com"


class QwenConfig(BaseSettings):
    """Qwen LLM configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="QWEN_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )

    api_key: SecretStr = Field(..., description="Qwen API key")
    model: str = Field(default="qwen-turbo", description="Qwen model name")
    base_url: str = Field(default="https://dashscope.aliyuncs.com/compatible-mode/v1", description="Qwen API base URL")
    timeout: int = Field(default=60, description="Request timeout in seconds")
    max_tokens: int = Field(default=2000, description="Maximum tokens per request")
    temperature: float = Field(default=0.1, description="Model temperature")
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('Temperature must be between 0.0 and 2.0')
        return v


class RiskConfig(BaseSettings):
    """Risk management configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="RISK_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )
    
    max_daily_loss: float = Field(default=1000.0, description="Maximum daily loss in USD")
    max_position_size: int = Field(default=10, description="Maximum position size")
    default_stop_loss_pct: float = Field(default=2.0, description="Default stop loss percentage")
    max_portfolio_risk: float = Field(default=5.0, description="Maximum portfolio risk percentage")
    max_portfolio_heat: float = Field(default=15.0, description="Maximum portfolio heat percentage")
    max_correlation_exposure: float = Field(default=50.0, description="Maximum correlation exposure percentage")
    position_size_method: str = Field(default="fixed_fractional", description="Position sizing method")
    kelly_fraction: float = Field(default=0.25, description="Kelly criterion fraction")
    
    @validator('default_stop_loss_pct', 'max_portfolio_risk', 'max_portfolio_heat', 'max_correlation_exposure')
    def validate_percentages(cls, v):
        if not 0.0 <= v <= 100.0:
            raise ValueError('Percentage values must be between 0.0 and 100.0')
        return v


class MonitoringConfig(BaseSettings):
    """Monitoring and alerting configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="MONITORING_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )
    
    enabled: bool = Field(default=True, description="Enable monitoring")
    metrics_port: int = Field(default=8000, description="Prometheus metrics port")
    alert_webhook_url: Optional[str] = Field(default=None, description="Webhook URL for alerts")
    slack_webhook_url: Optional[str] = Field(default=None, description="Slack webhook URL")
    email_alerts: bool = Field(default=False, description="Enable email alerts")
    email_smtp_host: Optional[str] = Field(default=None, description="SMTP host for email alerts")
    email_smtp_port: int = Field(default=587, description="SMTP port for email alerts")
    email_username: Optional[str] = Field(default=None, description="SMTP username")
    email_password: Optional[SecretStr] = Field(default=None, description="SMTP password")
    email_recipients: List[str] = Field(default_factory=list, description="Email alert recipients")


class TradingConfig(BaseSettings):
    """Trading configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="TRADING_",
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )
    
    enabled: bool = Field(default=False, description="Enable live trading")
    paper_trading: bool = Field(default=True, description="Enable paper trading mode")
    default_account_id: Optional[int] = Field(default=None, description="Default trading account ID")

    @validator('default_account_id', pre=True)
    def validate_account_id(cls, v):
        if v == '' or v is None:
            return None
        return int(v)
    contracts: List[str] = Field(default_factory=lambda: ["CON.F.US.EP.U25"], description="Contracts to trade")
    max_open_positions: int = Field(default=5, description="Maximum open positions")
    min_trade_interval: int = Field(default=60, description="Minimum seconds between trades")
    min_confidence_threshold: float = Field(default=0.6, description="Minimum confidence threshold for trading signals")
    strategy_weights: Dict[str, float] = Field(
        default_factory=lambda: {
            "fvg": 0.25,
            "ifvg": 0.20,
            "order_blocks": 0.25,
            "liquidity_sweeps": 0.20,
            "market_structure": 0.10
        },
        description="Strategy weight allocation"
    )


class Settings(BaseSettings):
    """Main application settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # Application settings
    app_name: str = Field(default="Trading Agent", description="Application name")
    version: str = Field(default="1.0.0", description="Application version")
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="Application environment")
    debug: bool = Field(default=False, description="Debug mode")
    log_level: LogLevel = Field(default=LogLevel.INFO, description="Logging level")
    
    # Data directories
    data_dir: Path = Field(default=Path("data"), description="Data directory")
    logs_dir: Path = Field(default=Path("logs"), description="Logs directory")
    cache_dir: Path = Field(default=Path("cache"), description="Cache directory")
    
    # Component configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    topstep: TopStepConfig = Field(default_factory=TopStepConfig)
    qwen: QwenConfig = Field(default_factory=QwenConfig)
    risk: RiskConfig = Field(default_factory=RiskConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    trading: TradingConfig = Field(default_factory=TradingConfig)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        for directory in [self.data_dir, self.logs_dir, self.cache_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    @validator('environment')
    def validate_environment(cls, v):
        if v == Environment.PRODUCTION:
            # Additional validation for production
            pass
        return v
    
    def get_database_url(self) -> str:
        """Get the complete database URL."""
        return self.database.url
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == Environment.PRODUCTION
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == Environment.DEVELOPMENT


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment variables."""
    global settings
    settings = Settings()
    return settings
