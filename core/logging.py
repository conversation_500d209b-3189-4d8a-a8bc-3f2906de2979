"""
Logging configuration for the trading agent.
Provides structured logging with multiple outputs and formatters.
"""

import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

import structlog
from rich.console import Console
from rich.logging import RichHandler

from .config import get_settings


class TradingLoggerMixin:
    """Mixin to add structured logging to any class."""
    
    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        cls.logger = get_logger(cls.__name__)


def configure_logging() -> None:
    """Configure the logging system with structured logging."""
    settings = get_settings()
    
    # Create logs directory
    settings.logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure standard library logging
    logging.basicConfig(
        level=getattr(logging, settings.log_level.value),
        format="%(message)s",
        handlers=[]
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="ISO"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if not settings.debug else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # Console handler with Rich formatting
    console_handler = RichHandler(
        console=Console(stderr=True),
        show_time=True,
        show_path=settings.debug,
        markup=True,
        rich_tracebacks=True,
        tracebacks_show_locals=settings.debug
    )
    console_handler.setLevel(getattr(logging, settings.log_level.value))
    root_logger.addHandler(console_handler)
    
    # File handler for all logs
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.logs_dir / "trading_agent.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding="utf-8"
    )
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        filename=settings.logs_dir / "errors.log",
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3,
        encoding="utf-8"
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(file_formatter)
    root_logger.addHandler(error_handler)
    
    # Trading-specific log handler
    trading_handler = logging.handlers.RotatingFileHandler(
        filename=settings.logs_dir / "trading.log",
        maxBytes=20 * 1024 * 1024,  # 20MB
        backupCount=10,
        encoding="utf-8"
    )
    trading_handler.setLevel(logging.INFO)
    trading_handler.setFormatter(file_formatter)
    
    # Add trading handler to specific loggers
    trading_loggers = [
        "trading_agent.agents.execution",
        "trading_agent.agents.decision",
        "trading_agent.agents.risk",
        "trading_agent.workflows.trading_flow"
    ]
    
    for logger_name in trading_loggers:
        logger = logging.getLogger(logger_name)
        logger.addHandler(trading_handler)
    
    # Set specific log levels for external libraries
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    if not settings.debug:
        logging.getLogger("langchain").setLevel(logging.WARNING)
        logging.getLogger("langgraph").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class TradeLogger:
    """Specialized logger for trading events."""
    
    def __init__(self):
        self.logger = get_logger("trading_agent.trades")
        self.settings = get_settings()
        
        # Create trades log file
        self.trades_file = self.settings.logs_dir / "trades.jsonl"
        self.trades_file.touch(exist_ok=True)
    
    def log_trade_signal(
        self,
        strategy: str,
        symbol: str,
        signal_type: str,
        confidence: float,
        price: float,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log a trading signal."""
        self.logger.info(
            "Trade signal generated",
            strategy=strategy,
            symbol=symbol,
            signal_type=signal_type,
            confidence=confidence,
            price=price,
            metadata=metadata or {},
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_trade_execution(
        self,
        order_id: int,
        symbol: str,
        side: str,
        size: int,
        price: float,
        order_type: str,
        status: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log a trade execution."""
        self.logger.info(
            "Trade executed",
            order_id=order_id,
            symbol=symbol,
            side=side,
            size=size,
            price=price,
            order_type=order_type,
            status=status,
            metadata=metadata or {},
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_position_update(
        self,
        position_id: int,
        symbol: str,
        size: int,
        average_price: float,
        unrealized_pnl: float,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log a position update."""
        self.logger.info(
            "Position updated",
            position_id=position_id,
            symbol=symbol,
            size=size,
            average_price=average_price,
            unrealized_pnl=unrealized_pnl,
            metadata=metadata or {},
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_risk_event(
        self,
        event_type: str,
        description: str,
        severity: str,
        affected_positions: Optional[list] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log a risk management event."""
        self.logger.warning(
            "Risk event",
            event_type=event_type,
            description=description,
            severity=severity,
            affected_positions=affected_positions or [],
            metadata=metadata or {},
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_performance_metric(
        self,
        metric_name: str,
        value: float,
        period: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log a performance metric."""
        self.logger.info(
            "Performance metric",
            metric_name=metric_name,
            value=value,
            period=period,
            metadata=metadata or {},
            timestamp=datetime.utcnow().isoformat()
        )


class PerformanceLogger:
    """Logger for performance monitoring."""
    
    def __init__(self):
        self.logger = get_logger("trading_agent.performance")
    
    def log_api_call(
        self,
        endpoint: str,
        method: str,
        duration_ms: float,
        status_code: int,
        error: Optional[str] = None
    ) -> None:
        """Log an API call performance."""
        self.logger.info(
            "API call",
            endpoint=endpoint,
            method=method,
            duration_ms=duration_ms,
            status_code=status_code,
            error=error,
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_agent_execution(
        self,
        agent_name: str,
        duration_ms: float,
        success: bool,
        error: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log agent execution performance."""
        self.logger.info(
            "Agent execution",
            agent_name=agent_name,
            duration_ms=duration_ms,
            success=success,
            error=error,
            metadata=metadata or {},
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_workflow_execution(
        self,
        workflow_name: str,
        duration_ms: float,
        steps_completed: int,
        success: bool,
        error: Optional[str] = None
    ) -> None:
        """Log workflow execution performance."""
        self.logger.info(
            "Workflow execution",
            workflow_name=workflow_name,
            duration_ms=duration_ms,
            steps_completed=steps_completed,
            success=success,
            error=error,
            timestamp=datetime.utcnow().isoformat()
        )


# Global logger instances
trade_logger = TradeLogger()
performance_logger = PerformanceLogger()


def get_trade_logger() -> TradeLogger:
    """Get the global trade logger instance."""
    return trade_logger


def get_performance_logger() -> PerformanceLogger:
    """Get the global performance logger instance."""
    return performance_logger
