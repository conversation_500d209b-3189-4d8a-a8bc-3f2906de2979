"""
Utility functions for the trading agent.
Common helper functions, decorators, and utilities.
"""

import asyncio
import time
import functools
from datetime import datetime, timezone
from typing import Any, Callable, Dict, List, Optional, TypeVar, Union
from decimal import Decimal, ROUND_HALF_UP
import hashlib
import json

from .logging import get_logger, get_performance_logger

logger = get_logger(__name__)
perf_logger = get_performance_logger()

T = TypeVar('T')


def timing_decorator(func: Callable[..., T]) -> Callable[..., T]:
    """Decorator to measure function execution time."""
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs) -> T:
        start_time = time.perf_counter()
        try:
            result = func(*args, **kwargs)
            duration_ms = (time.perf_counter() - start_time) * 1000
            perf_logger.log_agent_execution(
                agent_name=func.__name__,
                duration_ms=duration_ms,
                success=True
            )
            return result
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            perf_logger.log_agent_execution(
                agent_name=func.__name__,
                duration_ms=duration_ms,
                success=False,
                error=str(e)
            )
            raise
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs) -> T:
        start_time = time.perf_counter()
        try:
            result = await func(*args, **kwargs)
            duration_ms = (time.perf_counter() - start_time) * 1000
            perf_logger.log_agent_execution(
                agent_name=func.__name__,
                duration_ms=duration_ms,
                success=True
            )
            return result
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            perf_logger.log_agent_execution(
                agent_name=func.__name__,
                duration_ms=duration_ms,
                success=False,
                error=str(e)
            )
            raise
    
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


def retry_decorator(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
) -> Callable:
    """Decorator to retry function execution on failure."""
    
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries:
                        logger.error(
                            f"Function {func.__name__} failed after {max_retries} retries",
                            error=str(e),
                            attempt=attempt + 1
                        )
                        break
                    
                    wait_time = delay * (backoff_factor ** attempt)
                    logger.warning(
                        f"Function {func.__name__} failed, retrying in {wait_time}s",
                        error=str(e),
                        attempt=attempt + 1,
                        max_retries=max_retries
                    )
                    await asyncio.sleep(wait_time)
            
            raise last_exception
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries:
                        logger.error(
                            f"Function {func.__name__} failed after {max_retries} retries",
                            error=str(e),
                            attempt=attempt + 1
                        )
                        break
                    
                    wait_time = delay * (backoff_factor ** attempt)
                    logger.warning(
                        f"Function {func.__name__} failed, retrying in {wait_time}s",
                        error=str(e),
                        attempt=attempt + 1,
                        max_retries=max_retries
                    )
                    time.sleep(wait_time)
            
            raise last_exception
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def round_price(price: float, tick_size: float = 0.01) -> float:
    """Round price to the nearest tick size."""
    if tick_size <= 0:
        raise ValueError("Tick size must be positive")

    # Convert to Decimal for precise calculation
    price_decimal = Decimal(str(price))
    tick_decimal = Decimal(str(tick_size))

    # Round to nearest tick
    rounded = (price_decimal / tick_decimal).quantize(Decimal('1'), rounding=ROUND_HALF_UP) * tick_decimal

    return float(rounded)


def calculate_position_size(
    account_balance: float,
    risk_percentage: float,
    entry_price: float,
    stop_loss_price: float,
    contract_multiplier: float = 1.0
) -> int:
    """Calculate position size based on risk management rules."""
    if risk_percentage <= 0 or risk_percentage > 100:
        raise ValueError("Risk percentage must be between 0 and 100")
    
    if entry_price <= 0 or stop_loss_price <= 0:
        raise ValueError("Prices must be positive")
    
    if abs(entry_price - stop_loss_price) < 1e-8:
        raise ValueError("Entry price and stop loss price cannot be the same")
    
    risk_amount = account_balance * (risk_percentage / 100)
    price_difference = abs(entry_price - stop_loss_price)
    risk_per_contract = price_difference * contract_multiplier
    
    if risk_per_contract <= 0:
        return 0
    
    position_size = int(risk_amount / risk_per_contract)
    return max(0, position_size)


def calculate_kelly_fraction(
    win_rate: float,
    avg_win: float,
    avg_loss: float
) -> float:
    """Calculate Kelly criterion fraction for position sizing."""
    if not (0 <= win_rate <= 1):
        raise ValueError("Win rate must be between 0 and 1")
    
    if avg_win <= 0 or avg_loss <= 0:
        raise ValueError("Average win and loss must be positive")
    
    win_loss_ratio = avg_win / avg_loss
    kelly_fraction = win_rate - ((1 - win_rate) / win_loss_ratio)
    
    # Cap Kelly fraction to prevent over-leveraging
    return max(0, min(kelly_fraction, 0.25))


def normalize_contract_id(contract_id: str) -> str:
    """Normalize contract ID to standard format."""
    return contract_id.upper().strip()


def parse_timeframe(timeframe: str) -> int:
    """Parse timeframe string to minutes."""
    timeframe = timeframe.lower().strip()
    
    if timeframe.endswith('s'):
        return int(timeframe[:-1]) // 60  # Convert seconds to minutes
    elif timeframe.endswith('m'):
        return int(timeframe[:-1])
    elif timeframe.endswith('h'):
        return int(timeframe[:-1]) * 60
    elif timeframe.endswith('d'):
        return int(timeframe[:-1]) * 1440  # 24 * 60
    elif timeframe.endswith('w'):
        return int(timeframe[:-1]) * 10080  # 7 * 24 * 60
    else:
        # Assume it's already in minutes
        return int(timeframe)


def utc_now() -> datetime:
    """Get current UTC datetime."""
    return datetime.now(timezone.utc)


def timestamp_to_datetime(timestamp: Union[int, float]) -> datetime:
    """Convert timestamp to UTC datetime."""
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> float:
    """Convert datetime to timestamp."""
    return dt.timestamp()


def generate_order_tag(strategy: str, signal_type: str) -> str:
    """Generate a unique order tag."""
    timestamp = int(time.time() * 1000)  # Milliseconds
    data = f"{strategy}_{signal_type}_{timestamp}"
    hash_obj = hashlib.md5(data.encode())
    return f"{strategy}_{hash_obj.hexdigest()[:8]}"


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, returning default if denominator is zero."""
    if abs(denominator) < 1e-10:
        return default
    return numerator / denominator


def clamp(value: float, min_value: float, max_value: float) -> float:
    """Clamp value between min and max."""
    return max(min_value, min(value, max_value))


def percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values."""
    if abs(old_value) < 1e-10:
        return 0.0
    return ((new_value - old_value) / old_value) * 100


def moving_average(values: List[float], window: int) -> List[float]:
    """Calculate simple moving average."""
    if window <= 0 or window > len(values):
        return []
    
    result = []
    for i in range(window - 1, len(values)):
        avg = sum(values[i - window + 1:i + 1]) / window
        result.append(avg)
    
    return result


def exponential_moving_average(values: List[float], alpha: float) -> List[float]:
    """Calculate exponential moving average."""
    if not values or not (0 < alpha <= 1):
        return []
    
    result = [values[0]]
    for i in range(1, len(values)):
        ema = alpha * values[i] + (1 - alpha) * result[-1]
        result.append(ema)
    
    return result


def standard_deviation(values: List[float]) -> float:
    """Calculate standard deviation of values."""
    if len(values) < 2:
        return 0.0
    
    mean = sum(values) / len(values)
    variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
    return variance ** 0.5


def correlation(x_values: List[float], y_values: List[float]) -> float:
    """Calculate correlation coefficient between two series."""
    if len(x_values) != len(y_values) or len(x_values) < 2:
        return 0.0
    
    n = len(x_values)
    sum_x = sum(x_values)
    sum_y = sum(y_values)
    sum_xy = sum(x * y for x, y in zip(x_values, y_values))
    sum_x2 = sum(x * x for x in x_values)
    sum_y2 = sum(y * y for y in y_values)
    
    numerator = n * sum_xy - sum_x * sum_y
    denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5
    
    if abs(denominator) < 1e-10:
        return 0.0
    
    return numerator / denominator


def serialize_for_cache(obj: Any) -> str:
    """Serialize object for caching."""
    try:
        return json.dumps(obj, default=str, sort_keys=True)
    except (TypeError, ValueError):
        return str(obj)


def hash_dict(data: Dict[str, Any]) -> str:
    """Generate hash for dictionary."""
    serialized = serialize_for_cache(data)
    return hashlib.sha256(serialized.encode()).hexdigest()


class RateLimiter:
    """Simple rate limiter implementation."""
    
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    async def acquire(self) -> bool:
        """Acquire rate limit permission."""
        now = time.time()
        
        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
        
        if len(self.calls) < self.max_calls:
            self.calls.append(now)
            return True
        
        return False
    
    async def wait_if_needed(self) -> None:
        """Wait if rate limit is exceeded."""
        while not await self.acquire():
            await asyncio.sleep(0.1)
