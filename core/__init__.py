"""
Core module for the trading agent.
Provides configuration, logging, database, and utility functions.
"""

from .config import get_settings, Settings, reload_settings
from .logging import configure_logging, get_logger, get_trade_logger, get_performance_logger
from .database import get_db_manager, get_db_session, init_database
from .utils import (
    timing_decorator,
    retry_decorator,
    round_price,
    calculate_position_size,
    calculate_kelly_fraction,
    normalize_contract_id,
    parse_timeframe,
    utc_now,
    generate_order_tag,
    RateLimiter
)

__all__ = [
    # Configuration
    "get_settings",
    "Settings", 
    "reload_settings",
    
    # Logging
    "configure_logging",
    "get_logger",
    "get_trade_logger", 
    "get_performance_logger",
    
    # Database
    "get_db_manager",
    "get_db_session",
    "init_database",
    
    # Utilities
    "timing_decorator",
    "retry_decorator",
    "round_price",
    "calculate_position_size",
    "calculate_kelly_fraction",
    "normalize_contract_id",
    "parse_timeframe",
    "utc_now",
    "generate_order_tag",
    "RateLimiter"
]
