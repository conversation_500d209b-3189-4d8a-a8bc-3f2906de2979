"""
Main entry point for the trading agent.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core import (
    configure_logging, get_logger, get_settings,
    init_database, get_db_manager
)


async def main():
    """Main application entry point."""
    try:
        # Configure logging first
        configure_logging()
        logger = get_logger(__name__)
        
        logger.info("Starting Trading Agent")
        
        # Load settings
        settings = get_settings()
        logger.info(f"Loaded settings for environment: {settings.environment}")
        logger.info(f"TopStep environment: {settings.topstep.environment}")
        logger.info(f"Database URL: {settings.database.url}")
        
        # Initialize database
        logger.info("Initializing database...")
        await init_database()
        logger.info("Database initialized successfully")
        
        # Test database connection
        db_manager = get_db_manager()
        async with db_manager.get_async_session() as session:
            logger.info("Database connection test successful")
        
        logger.info("Trading Agent core components initialized successfully")
        logger.info("Ready to start building agents...")
        
    except Exception as e:
        logger.error(f"Failed to initialize Trading Agent: {e}", exc_info=True)
        sys.exit(1)
    finally:
        # Cleanup
        db_manager = get_db_manager()
        await db_manager.close()


if __name__ == "__main__":
    asyncio.run(main())
