#!/usr/bin/env python3
"""
Test APIs with working endpoints.
"""

import os
import asyncio
import ssl
import aiohttp
import json
from datetime import datetime, timezone, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

TOPSTEP_USERNAME = os.getenv("TOPSTEP_USERNAME")
TOPSTEP_API_KEY = os.getenv("TOPSTEP_API_KEY")

def create_ssl_session():
    """Create aiohttp session with SSL verification disabled for testing."""
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    return aiohttp.ClientSession(connector=connector)

async def test_topstep_demo_detailed():
    """Test TopStep demo API with detailed response analysis."""
    print("🔍 Testing TopStep Demo API (Detailed)...")
    
    try:
        async with create_ssl_session() as session:
            auth_data = {
                "userName": TOPSTEP_USERNAME,
                "apiKey": TOPSTEP_API_KEY
            }
            
            url = "https://gateway-api-demo.s2f.projectx.com/api/Auth/loginKey"
            
            print(f"  📤 Sending request to: {url}")
            print(f"  📤 Username: {TOPSTEP_USERNAME}")
            print(f"  📤 API Key: {TOPSTEP_API_KEY[:20]}...")
            
            async with session.post(url, json=auth_data) as response:
                response_text = await response.text()
                
                print(f"  📥 Response Status: {response.status}")
                print(f"  📥 Response Headers: {dict(response.headers)}")
                print(f"  📥 Response Body: {response_text}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        print(f"  📊 Parsed Response:")
                        for key, value in data.items():
                            if key == "token" and value:
                                print(f"    {key}: {value[:30]}...")
                            else:
                                print(f"    {key}: {value}")
                        
                        if data.get("success"):
                            print("  ✅ Authentication successful!")
                            return data.get("token")
                        else:
                            print(f"  ❌ Authentication failed: {data.get('errorMessage', 'Unknown error')}")
                            print(f"  ❌ Error Code: {data.get('errorCode', 'Unknown')}")
                            return None
                    except json.JSONDecodeError:
                        print(f"  ❌ Invalid JSON response")
                        return None
                else:
                    print(f"  ❌ HTTP Error: {response.status}")
                    return None
                    
    except Exception as e:
        print(f"  ❌ Request failed: {e}")
        return None

async def test_topstep_accounts_demo(token):
    """Test TopStep account retrieval on demo."""
    print("🔍 Testing TopStep Accounts (Demo)...")
    
    if not token:
        print("  ❌ No token available")
        return False
    
    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            url = "https://gateway-api-demo.s2f.projectx.com/api/Account/search"
            
            async with session.get(url, headers=headers) as response:
                response_text = await response.text()
                
                print(f"  📥 Response Status: {response.status}")
                print(f"  📥 Response: {response_text}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        if data.get("success"):
                            accounts = data.get("accounts", [])
                            print(f"  ✅ Found {len(accounts)} accounts:")
                            for account in accounts:
                                print(f"    - {account.get('name')} (ID: {account.get('id')}) - Balance: ${account.get('balance', 0)}")
                            return accounts
                        else:
                            print(f"  ❌ Failed: {data.get('errorMessage')}")
                            return False
                    except json.JSONDecodeError:
                        print(f"  ❌ Invalid JSON response")
                        return False
                else:
                    print(f"  ❌ HTTP Error: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"  ❌ Request failed: {e}")
        return False

async def test_topstep_contracts_demo(token):
    """Test TopStep contract search on demo."""
    print("🔍 Testing TopStep Contracts (Demo)...")
    
    if not token:
        print("  ❌ No token available")
        return False
    
    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Try to get available contracts
            url = "https://gateway-api-demo.s2f.projectx.com/api/Contract/available"
            
            async with session.get(url, headers=headers) as response:
                response_text = await response.text()
                
                print(f"  📥 Response Status: {response.status}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        if data.get("success"):
                            contracts = data.get("contracts", [])
                            print(f"  ✅ Found {len(contracts)} contracts:")
                            for contract in contracts[:5]:  # Show first 5
                                print(f"    - {contract.get('symbol', 'N/A')} - {contract.get('name', 'N/A')}")
                            return contracts
                        else:
                            print(f"  ❌ Failed: {data.get('errorMessage')}")
                            return False
                    except json.JSONDecodeError:
                        print(f"  ❌ Invalid JSON response: {response_text[:200]}")
                        return False
                else:
                    print(f"  ❌ HTTP Error: {response.status} - {response_text[:200]}")
                    return False
                    
    except Exception as e:
        print(f"  ❌ Request failed: {e}")
        return False

async def test_topstep_market_data_demo(token):
    """Test TopStep market data on demo."""
    print("🔍 Testing TopStep Market Data (Demo)...")
    
    if not token:
        print("  ❌ No token available")
        return False
    
    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Get recent market data for a common contract
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=1)
            
            market_data_request = {
                "contractId": "CON.F.US.EP.U25",  # ES futures
                "live": False,
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "unit": 2,  # Minutes
                "unitNumber": 1,  # 1-minute bars
                "limit": 5,
                "includePartialBar": False
            }
            
            url = "https://gateway-api-demo.s2f.projectx.com/api/History/retrieveBars"
            
            print(f"  📤 Requesting data for: {market_data_request['contractId']}")
            print(f"  📤 Time range: {start_time.strftime('%H:%M')} - {end_time.strftime('%H:%M')} UTC")
            
            async with session.post(url, json=market_data_request, headers=headers) as response:
                response_text = await response.text()
                
                print(f"  📥 Response Status: {response.status}")
                
                if response.status == 200:
                    try:
                        data = json.loads(response_text)
                        if data.get("success"):
                            bars = data.get("bars", [])
                            print(f"  ✅ Retrieved {len(bars)} market data bars:")
                            for i, bar in enumerate(bars):
                                timestamp = bar.get('t', 'N/A')
                                ohlc = f"O:{bar.get('o')} H:{bar.get('h')} L:{bar.get('l')} C:{bar.get('c')}"
                                volume = f"V:{bar.get('v')}"
                                print(f"    {i+1}. {timestamp} - {ohlc} {volume}")
                            return bars
                        else:
                            print(f"  ❌ Failed: {data.get('errorMessage')}")
                            return False
                    except json.JSONDecodeError:
                        print(f"  ❌ Invalid JSON response: {response_text[:200]}")
                        return False
                else:
                    print(f"  ❌ HTTP Error: {response.status} - {response_text[:200]}")
                    return False
                    
    except Exception as e:
        print(f"  ❌ Request failed: {e}")
        return False

async def main():
    """Run TopStep demo API tests."""
    print("🚀 TESTING TOPSTEP DEMO API (WORKING ENDPOINT)")
    print("=" * 70)
    
    print(f"📋 Using Credentials:")
    print(f"  Username: {TOPSTEP_USERNAME}")
    print(f"  API Key: {TOPSTEP_API_KEY[:20]}..." if TOPSTEP_API_KEY else "None")
    print()
    
    # Test authentication
    token = await test_topstep_demo_detailed()
    print()
    
    if token:
        # Test accounts
        accounts = await test_topstep_accounts_demo(token)
        print()
        
        # Test contracts
        contracts = await test_topstep_contracts_demo(token)
        print()
        
        # Test market data
        market_data = await test_topstep_market_data_demo(token)
        print()
    else:
        accounts = False
        contracts = False
        market_data = False
    
    print("=" * 70)
    print("📊 RESULTS:")
    
    tests = [
        ("Authentication", bool(token)),
        ("Accounts", bool(accounts)),
        ("Contracts", bool(contracts)),
        ("Market Data", bool(market_data))
    ]
    
    for test_name, passed in tests:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(result for _, result in tests)
    total = len(tests)
    
    print()
    if passed == total:
        print(f"🎉 ALL {total} TOPSTEP TESTS PASSED!")
        print("✅ TopStep Demo API is fully functional")
        print("✅ Ready to build trading agents with real market data")
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("❌ Need to investigate TopStep API issues")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
