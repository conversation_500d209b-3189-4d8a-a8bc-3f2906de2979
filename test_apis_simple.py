#!/usr/bin/env python3
"""
Simple API testing with real credentials.
Direct API calls without complex configuration.
"""

import os
import sys
import asyncio
import json
import ssl
import aiohttp
from datetime import datetime, timezone, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Real credentials from .env
TOPSTEP_USERNAME = os.getenv("TOPSTEP_USERNAME")
TOPSTEP_API_KEY = os.getenv("TOPSTEP_API_KEY")
QWEN_API_KEY = os.getenv("QWEN_API_KEY")
QWEN_MODEL = os.getenv("QWEN_MODEL", "qwen-turbo")

print(f"🔑 Loaded credentials:")
print(f"  TopStep Username: {TOPSTEP_USERNAME}")
print(f"  TopStep API Key: {TOPSTEP_API_KEY[:20]}..." if TOPSTEP_API_KEY else "  TopStep API Key: None")
print(f"  Qwen API Key: {QWEN_API_KEY[:20]}..." if QWEN_API_KEY else "  Qwen API Key: None")
print()


def create_ssl_session():
    """Create aiohttp session with SSL verification disabled for testing."""
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    connector = aiohttp.TCPConnector(ssl=ssl_context)
    return aiohttp.ClientSession(connector=connector)


async def test_topstep_authentication():
    """Test TopStep authentication with real credentials."""
    print("🔍 Testing TopStep Authentication...")
    
    if not TOPSTEP_USERNAME or not TOPSTEP_API_KEY:
        print("❌ TopStep credentials not found in environment")
        return False
    
    try:
        async with create_ssl_session() as session:
            auth_data = {
                "userName": TOPSTEP_USERNAME,
                "apiKey": TOPSTEP_API_KEY
            }
            
            url = "https://gateway-api-live.s2f.projectx.com/api/Auth/loginKey"
            
            async with session.post(url, json=auth_data) as response:
                response_text = await response.text()
                print(f"  Response Status: {response.status}")
                print(f"  Response: {response_text[:200]}...")
                
                if response.status == 200:
                    data = json.loads(response_text)
                    if data.get("success"):
                        token = data.get("token")
                        print(f"✅ TopStep authentication successful!")
                        print(f"  Token: {token[:30]}...")
                        return token
                    else:
                        print(f"❌ Authentication failed: {data.get('errorMessage')}")
                        return False
                else:
                    print(f"❌ HTTP error: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ TopStep authentication failed: {e}")
        return False


async def test_topstep_accounts(token):
    """Test TopStep account retrieval."""
    print("🔍 Testing TopStep Account Retrieval...")

    if not token:
        print("❌ No valid token for account test")
        return False

    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            url = "https://gateway-api-live.s2f.projectx.com/api/Account/search"
            
            async with session.get(url, headers=headers) as response:
                response_text = await response.text()
                print(f"  Response Status: {response.status}")
                
                if response.status == 200:
                    data = json.loads(response_text)
                    if data.get("success"):
                        accounts = data.get("accounts", [])
                        print(f"✅ Retrieved {len(accounts)} accounts:")
                        for account in accounts:
                            print(f"    Account: {account.get('name')} (ID: {account.get('id')}) - Balance: ${account.get('balance', 0)}")
                        return accounts
                    else:
                        print(f"❌ Account retrieval failed: {data.get('errorMessage')}")
                        return False
                else:
                    print(f"❌ HTTP error: {response.status} - {response_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Account retrieval failed: {e}")
        return False


async def test_topstep_market_data(token):
    """Test TopStep market data retrieval."""
    print("🔍 Testing TopStep Market Data...")
    
    if not token:
        print("❌ No valid token for market data test")
        return False
    
    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Get recent market data
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=2)
            
            market_data_request = {
                "contractId": "CON.F.US.EP.U25",  # ES futures
                "live": False,
                "startTime": start_time.isoformat(),
                "endTime": end_time.isoformat(),
                "unit": 2,  # Minutes
                "unitNumber": 5,  # 5-minute bars
                "limit": 10,
                "includePartialBar": False
            }
            
            url = "https://gateway-api-live.s2f.projectx.com/api/History/retrieveBars"
            
            async with session.post(url, json=market_data_request, headers=headers) as response:
                response_text = await response.text()
                print(f"  Response Status: {response.status}")
                
                if response.status == 200:
                    data = json.loads(response_text)
                    if data.get("success"):
                        bars = data.get("bars", [])
                        print(f"✅ Retrieved {len(bars)} market data bars:")
                        for i, bar in enumerate(bars[:3]):  # Show first 3
                            print(f"    Bar {i+1}: {bar.get('t')} - O:{bar.get('o')} H:{bar.get('h')} L:{bar.get('l')} C:{bar.get('c')} V:{bar.get('v')}")
                        return bars
                    else:
                        print(f"❌ Market data retrieval failed: {data.get('errorMessage')}")
                        return False
                else:
                    print(f"❌ HTTP error: {response.status} - {response_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Market data retrieval failed: {e}")
        return False


async def test_qwen_llm():
    """Test Qwen LLM with real credentials."""
    print("🔍 Testing Qwen LLM...")
    
    if not QWEN_API_KEY:
        print("❌ Qwen API key not found in environment")
        return False
    
    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {QWEN_API_KEY}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": QWEN_MODEL,
                "messages": [
                    {"role": "system", "content": "You are a helpful trading assistant."},
                    {"role": "user", "content": "What are the key factors to consider when trading ES futures? Give me 3 main points."}
                ],
                "max_tokens": 300,
                "temperature": 0.1
            }
            
            url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
            
            async with session.post(url, json=payload, headers=headers) as response:
                response_text = await response.text()
                print(f"  Response Status: {response.status}")
                
                if response.status == 200:
                    data = json.loads(response_text)
                    content = data["choices"][0]["message"]["content"]
                    usage = data.get("usage", {})
                    
                    print(f"✅ Qwen LLM response received!")
                    print(f"  Tokens used: {usage.get('total_tokens', 0)}")
                    print(f"  Response: {content[:200]}...")
                    return content
                else:
                    print(f"❌ HTTP error: {response.status} - {response_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Qwen LLM test failed: {e}")
        return False


async def test_qwen_market_analysis():
    """Test Qwen market analysis with trading data."""
    print("🔍 Testing Qwen Market Analysis...")
    
    if not QWEN_API_KEY:
        print("❌ Qwen API key not found in environment")
        return False
    
    try:
        async with create_ssl_session() as session:
            headers = {
                "Authorization": f"Bearer {QWEN_API_KEY}",
                "Content-Type": "application/json"
            }
            
            market_data = {
                "symbol": "ES",
                "current_price": 4500.0,
                "daily_change": 15.0,
                "volume": 150000,
                "high": 4515.0,
                "low": 4485.0
            }
            
            analysis_prompt = f"""
            Analyze the following ES futures market data and provide a brief trading assessment:
            
            Current Price: ${market_data['current_price']}
            Daily Change: ${market_data['daily_change']}
            Volume: {market_data['volume']}
            High: ${market_data['high']}
            Low: ${market_data['low']}
            
            Provide:
            1. Market sentiment (Bullish/Bearish/Neutral)
            2. Key price levels to watch
            3. Risk assessment
            
            Keep response under 150 words.
            """
            
            payload = {
                "model": QWEN_MODEL,
                "messages": [
                    {"role": "system", "content": "You are an expert futures market analyst."},
                    {"role": "user", "content": analysis_prompt}
                ],
                "max_tokens": 200,
                "temperature": 0.1
            }
            
            url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
            
            async with session.post(url, json=payload, headers=headers) as response:
                response_text = await response.text()
                print(f"  Response Status: {response.status}")
                
                if response.status == 200:
                    data = json.loads(response_text)
                    content = data["choices"][0]["message"]["content"]
                    usage = data.get("usage", {})
                    
                    print(f"✅ Qwen market analysis completed!")
                    print(f"  Tokens used: {usage.get('total_tokens', 0)}")
                    print(f"  Analysis: {content}")
                    return content
                else:
                    print(f"❌ HTTP error: {response.status} - {response_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Qwen market analysis failed: {e}")
        return False


async def main():
    """Run all API tests with real credentials."""
    print("🚀 TESTING REAL API INTEGRATIONS")
    print("=" * 60)
    
    # Test TopStep APIs
    token = await test_topstep_authentication()
    if token:
        accounts = await test_topstep_accounts(token)
        market_data = await test_topstep_market_data(token)
    else:
        accounts = False
        market_data = False
    
    print()
    
    # Test Qwen LLM
    qwen_basic = await test_qwen_llm()
    qwen_analysis = await test_qwen_market_analysis()
    
    print()
    print("=" * 60)
    
    # Summary
    tests = [
        bool(token),
        bool(accounts),
        bool(market_data),
        bool(qwen_basic),
        bool(qwen_analysis)
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    if passed == total:
        print(f"🎉 ALL {total} API TESTS PASSED! 100% SUCCESS!")
        print("✅ TopStep authentication working")
        print("✅ TopStep account retrieval working")
        print("✅ TopStep market data working")
        print("✅ Qwen LLM basic functionality working")
        print("✅ Qwen market analysis working")
        print("✅ ALL REAL API INTEGRATIONS FUNCTIONAL!")
        return True
    else:
        print(f"❌ {total - passed} out of {total} tests failed")
        print("❌ Some API integrations need fixing")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
