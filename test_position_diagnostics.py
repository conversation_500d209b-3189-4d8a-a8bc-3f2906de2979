#!/usr/bin/env python3
"""
POSITION DIAGNOSTICS TEST
Comprehensive test to verify account balance, position status, and current price updates.
"""

import asyncio
import sys
import json
from datetime import datetime, timezone
from typing import Dict, Any, List

# Add project root to path
sys.path.append('.')

from api import TopStepClient
from agents import MarketDataAgent


async def test_position_diagnostics():
    """Comprehensive test to diagnose position and balance issues."""
    
    print("🔍 POSITION DIAGNOSTICS TEST")
    print("=" * 60)
    print("Testing account balance, position status, and price updates")
    print("=" * 60)
    
    try:
        # Initialize TopStep client
        topstep_client = TopStepClient(preferred_account_type="PRACTICE")
        
        # Test authentication
        auth_success = await topstep_client.authenticate()
        if not auth_success:
            print("❌ TopStep API authentication failed")
            return
        
        print("✅ TopStep API authentication successful")
        print(f"   Account ID: {topstep_client.account_id}")
        print(f"   Account Type: {topstep_client.preferred_account_type}")
        
        # Test 1: Account Balance Verification
        print("\n💰 TEST 1: ACCOUNT BALANCE VERIFICATION")
        print("-" * 50)
        
        balance = await topstep_client.get_account_balance()
        print(f"   Current Account Balance: ${balance:,.2f}")
        
        # Get detailed account info
        try:
            data = {"onlyActiveAccounts": True}
            response = await topstep_client._make_request('POST', '/api/Account/search', data)
            
            if response and response.get('success'):
                accounts = response.get('accounts', [])
                for account in accounts:
                    if account.get('id') == topstep_client.account_id:
                        print(f"   Account Details:")
                        print(f"     - Balance: ${account.get('balance', 0):,.2f}")
                        print(f"     - Equity: ${account.get('equity', 0):,.2f}")
                        print(f"     - Buying Power: ${account.get('buyingPower', 0):,.2f}")
                        print(f"     - Day Trading Buying Power: ${account.get('dayTradingBuyingPower', 0):,.2f}")
                        print(f"     - Unrealized P&L: ${account.get('unrealizedPnl', 0):,.2f}")
                        print(f"     - Realized P&L: ${account.get('realizedPnl', 0):,.2f}")
                        break
        except Exception as e:
            print(f"   ⚠️ Could not get detailed account info: {e}")
        
        # Test 2: Position Status Verification
        print("\n📈 TEST 2: POSITION STATUS VERIFICATION")
        print("-" * 50)
        
        positions = await topstep_client.get_positions(topstep_client.account_id)
        print(f"   Total Positions Found: {len(positions)}")
        
        if positions:
            for i, position in enumerate(positions, 1):
                print(f"\n   Position {i}:")
                print(f"     - ID: {position.id}")
                print(f"     - Symbol: {position.symbol}")
                print(f"     - Contract ID: {position.contract_id}")
                print(f"     - Side: {position.side}")
                print(f"     - Size: {position.size}")
                print(f"     - Entry Price: ${position.entry_price:.2f}")
                print(f"     - Current Price: ${position.current_price:.2f}")
                print(f"     - Unrealized P&L: ${position.unrealized_pnl:.2f}")
                print(f"     - Account ID: {position.account_id}")
                print(f"     - Creation Time: {position.creation_timestamp}")
                
                # Check if current price is zero (problematic)
                if position.current_price == 0.0:
                    print(f"     ⚠️ WARNING: Current price is $0.00 - This indicates a data feed issue!")
                
                # Check if P&L is -100% (problematic)
                if position.entry_price > 0:
                    pnl_percentage = ((position.current_price - position.entry_price) / position.entry_price) * 100
                    if position.side == 'SHORT':
                        pnl_percentage = -pnl_percentage
                    print(f"     - P&L Percentage: {pnl_percentage:.2f}%")
                    
                    if pnl_percentage <= -99:
                        print(f"     ❌ CRITICAL: P&L is {pnl_percentage:.2f}% - Position may be closed or data corrupted!")
        else:
            print("   ℹ️ No open positions found")
        
        # Test 3: Real-Time Price Feed Verification
        print("\n📊 TEST 3: REAL-TIME PRICE FEED VERIFICATION")
        print("-" * 50)
        
        market_agent = MarketDataAgent(topstep_client)
        
        # Test price feeds for the contracts we have positions in
        test_symbols = ["ES", "YM", "NQ"]
        
        for symbol in test_symbols:
            print(f"\n   Testing {symbol} price feed:")
            
            contract_id = topstep_client.convert_symbol_to_contract(symbol)
            print(f"     Contract ID: {contract_id}")
            
            # Get latest market data
            bars = await market_agent.fetch_market_data(contract_id, "1m", 1)
            
            if bars and len(bars) > 0:
                latest_bar = bars[0]
                print(f"     ✅ Latest Price: ${latest_bar.close_price:.2f}")
                print(f"     ✅ Timestamp: {latest_bar.timestamp}")
                print(f"     ✅ Volume: {latest_bar.volume}")
                
                # Check if data is recent
                time_diff = datetime.now(timezone.utc) - latest_bar.timestamp
                if time_diff.total_seconds() < 300:  # 5 minutes
                    print(f"     ✅ Data is recent ({time_diff.total_seconds():.0f} seconds old)")
                else:
                    print(f"     ⚠️ Data is stale ({time_diff.total_seconds():.0f} seconds old)")
            else:
                print(f"     ❌ No market data available for {symbol}")
        
        # Test 4: Position Update Mechanism
        print("\n🔄 TEST 4: POSITION UPDATE MECHANISM")
        print("-" * 50)
        
        if positions:
            for position in positions[:2]:  # Test first 2 positions
                print(f"\n   Testing position update for {position.symbol}:")
                
                # Get current market price
                contract_id = position.contract_id
                bars = await market_agent.fetch_market_data(contract_id, "1m", 1)
                
                if bars and len(bars) > 0:
                    current_market_price = bars[0].close_price
                    print(f"     Current Market Price: ${current_market_price:.2f}")
                    print(f"     Position Current Price: ${position.current_price:.2f}")
                    
                    if abs(current_market_price - position.current_price) > 1.0:
                        print(f"     ⚠️ Price mismatch detected!")
                        print(f"       Market: ${current_market_price:.2f}")
                        print(f"       Position: ${position.current_price:.2f}")
                        print(f"       Difference: ${abs(current_market_price - position.current_price):.2f}")
                    else:
                        print(f"     ✅ Prices are synchronized")
                else:
                    print(f"     ❌ Could not get current market price")
        
        # Test 5: Trade History Verification
        print("\n📋 TEST 5: TRADE HISTORY VERIFICATION")
        print("-" * 50)
        
        try:
            # Get recent trades
            data = {
                'accountId': topstep_client.account_id,
                'startDate': (datetime.now() - timedelta(days=1)).isoformat(),
                'endDate': datetime.now().isoformat()
            }
            response = await topstep_client._make_request('POST', '/api/Trade/search', data)
            
            if response and response.get('success'):
                trades = response.get('trades', [])
                print(f"   Recent Trades Found: {len(trades)}")
                
                for i, trade in enumerate(trades[:5], 1):  # Show first 5 trades
                    print(f"\n   Trade {i}:")
                    print(f"     - ID: {trade.get('id', 'N/A')}")
                    print(f"     - Symbol: {trade.get('symbol', 'N/A')}")
                    print(f"     - Side: {trade.get('side', 'N/A')}")
                    print(f"     - Size: {trade.get('size', 'N/A')}")
                    print(f"     - Price: ${trade.get('price', 0):.2f}")
                    print(f"     - Time: {trade.get('timestamp', 'N/A')}")
                    print(f"     - P&L: ${trade.get('pnl', 0):.2f}")
            else:
                print("   ℹ️ No recent trades found or API error")
        except Exception as e:
            print(f"   ⚠️ Could not get trade history: {e}")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 POSITION DIAGNOSTICS SUMMARY")
        print("=" * 60)
        
        print(f"\n📊 ACCOUNT STATUS:")
        print(f"   Balance: ${balance:,.2f}")
        print(f"   Positions: {len(positions)}")
        
        if positions:
            zero_price_positions = sum(1 for p in positions if p.current_price == 0.0)
            negative_pnl_positions = sum(1 for p in positions if p.unrealized_pnl < -1000)
            
            print(f"\n⚠️ POTENTIAL ISSUES:")
            if zero_price_positions > 0:
                print(f"   - {zero_price_positions} positions with $0.00 current price")
            if negative_pnl_positions > 0:
                print(f"   - {negative_pnl_positions} positions with large negative P&L")
            
            if zero_price_positions == 0 and negative_pnl_positions == 0:
                print(f"   ✅ No major issues detected")
        
        return {
            "balance": balance,
            "positions_count": len(positions),
            "zero_price_positions": zero_price_positions if positions else 0,
            "authentication_success": True
        }
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}
    
    finally:
        # Cleanup
        if 'topstep_client' in locals():
            await topstep_client.close()


if __name__ == "__main__":
    from datetime import timedelta
    results = asyncio.run(test_position_diagnostics())
    print(f"\n🎯 Test completed with results: {results}")
