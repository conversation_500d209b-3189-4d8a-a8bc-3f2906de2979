#!/usr/bin/env python3
"""
Test Confidence Controls and System Messages
"""

import asyncio
import sys
import aiohttp
import json

# Add project root to path
sys.path.append('.')

from core.settings_manager import settings_manager


async def test_confidence_controls():
    """Test the confidence controls and system messages."""
    
    print("🧪 TESTING CONFIDENCE CONTROLS & SYSTEM MESSAGES")
    print("=" * 60)
    
    try:
        # Test 1: Settings Manager
        print("\n🔧 TEST 1: SETTINGS MANAGER")
        print("-" * 40)
        
        # Test initial confidence
        initial_confidence = settings_manager.get_confidence_threshold()
        print(f"✅ Initial confidence threshold: {initial_confidence:.1%}")
        
        # Test changing confidence
        new_confidence = 0.75
        settings_manager.set_confidence_threshold(new_confidence)
        updated_confidence = settings_manager.get_confidence_threshold()
        print(f"✅ Updated confidence threshold: {updated_confidence:.1%}")
        
        assert updated_confidence == new_confidence, "Confidence not updated correctly"
        
        # Test system messages
        messages = settings_manager.get_system_messages(limit=5)
        print(f"✅ System messages: {len(messages)} messages")
        
        if messages:
            latest_msg = messages[-1]
            print(f"   Latest: [{latest_msg.component}] {latest_msg.message}")
        
        # Test 2: API Endpoints
        print("\n🌐 TEST 2: API ENDPOINTS")
        print("-" * 40)
        
        async with aiohttp.ClientSession() as session:
            # Test get settings
            async with session.get('http://localhost:8000/api/settings') as response:
                if response.status == 200:
                    data = await response.json()
                    if data['success']:
                        settings_data = data['data']
                        print(f"✅ API Settings: Confidence = {settings_data['confidence_threshold']:.1%}")
                    else:
                        print(f"❌ API Settings failed: {data['message']}")
                else:
                    print(f"❌ API Settings HTTP error: {response.status}")
            
            # Test set confidence
            test_confidence = 0.65
            async with session.post(
                'http://localhost:8000/api/set-confidence',
                json={'threshold': test_confidence}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data['success']:
                        print(f"✅ API Set Confidence: {data['threshold']:.1%}")
                    else:
                        print(f"❌ API Set Confidence failed: {data['message']}")
                else:
                    print(f"❌ API Set Confidence HTTP error: {response.status}")
            
            # Test get system messages
            async with session.get('http://localhost:8000/api/system-messages') as response:
                if response.status == 200:
                    data = await response.json()
                    if data['success']:
                        messages = data['data']
                        print(f"✅ API System Messages: {len(messages)} messages")
                        if messages:
                            latest = messages[-1]
                            print(f"   Latest: [{latest['component']}] {latest['message']}")
                    else:
                        print(f"❌ API System Messages failed: {data['message']}")
                else:
                    print(f"❌ API System Messages HTTP error: {response.status}")
        
        # Test 3: LLM Integration
        print("\n🧠 TEST 3: LLM INTEGRATION")
        print("-" * 40)
        
        from llm.qwen_client import QwenClient
        from llm.models import TradingDecision, TradingAction
        
        qwen_client = QwenClient()
        
        # Create test trading decisions with different confidence levels
        test_decisions = [
            TradingDecision(
                action=TradingAction.BUY,
                confidence=0.55,  # Below threshold
                reasoning="Low confidence test signal",
                entry_price=4500.0,
                strategy_name="TestFVG"
            ),
            TradingDecision(
                action=TradingAction.SELL,
                confidence=0.80,  # Above threshold
                reasoning="High confidence test signal",
                entry_price=4500.0,
                strategy_name="TestOB"
            )
        ]
        
        # Test with current threshold (should be 0.65)
        market_context = {"symbol": "ES", "current_price": 4500.0}
        
        selected_decision = await qwen_client.analyze_trading_signals(test_decisions, market_context)
        
        if selected_decision:
            print(f"✅ LLM Selected: {selected_decision.strategy_name} {selected_decision.action.value} (Confidence: {selected_decision.confidence:.1%})")
        else:
            print("✅ LLM Rejected all signals (confidence too low)")
        
        # Check system messages for LLM decisions
        recent_messages = settings_manager.get_system_messages(limit=10)
        llm_messages = [msg for msg in recent_messages if msg.component == "LLM"]
        print(f"✅ LLM System Messages: {len(llm_messages)} messages")
        
        for msg in llm_messages[-2:]:  # Show last 2 LLM messages
            print(f"   [{msg.level}] {msg.message}")
        
        # Test 4: Dynamic Threshold Change
        print("\n⚙️ TEST 4: DYNAMIC THRESHOLD CHANGE")
        print("-" * 40)
        
        # Lower threshold to 0.50
        settings_manager.set_confidence_threshold(0.50)
        print(f"✅ Lowered threshold to: {settings_manager.get_confidence_threshold():.1%}")
        
        # Test same decisions again
        selected_decision_2 = await qwen_client.analyze_trading_signals(test_decisions, market_context)
        
        if selected_decision_2:
            print(f"✅ LLM Selected (lower threshold): {selected_decision_2.strategy_name} {selected_decision_2.action.value} (Confidence: {selected_decision_2.confidence:.1%})")
        else:
            print("❌ LLM still rejected signals")
        
        # Reset to original threshold
        settings_manager.set_confidence_threshold(0.6)
        print(f"✅ Reset threshold to: {settings_manager.get_confidence_threshold():.1%}")
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("✅ Confidence controls working correctly")
        print("✅ System messages being generated")
        print("✅ API endpoints functional")
        print("✅ LLM integration with dynamic thresholds")
        print("✅ Real-time threshold changes affecting decisions")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_confidence_controls())
    sys.exit(0 if success else 1)
