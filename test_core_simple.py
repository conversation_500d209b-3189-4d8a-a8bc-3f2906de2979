"""
Simple test script for core functionality without external dependencies.
"""

import os
import sys
import tempfile
from pathlib import Path
from decimal import Decimal, ROUND_HALF_UP

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_round_price():
    """Test price rounding function."""
    def round_price(price: float, tick_size: float = 0.01) -> float:
        """Round price to the nearest tick size."""
        if tick_size <= 0:
            raise ValueError("Tick size must be positive")
        
        decimal_places = len(str(tick_size).split('.')[-1]) if '.' in str(tick_size) else 0
        multiplier = 1 / tick_size
        
        rounded = Decimal(str(price * multiplier)).quantize(
            Decimal('1'), 
            rounding=ROUND_HALF_UP
        ) / Decimal(str(multiplier))
        
        return float(rounded.quantize(
            Decimal('0.' + '0' * decimal_places),
            rounding=ROUND_HALF_UP
        ))
    
    # Test cases
    assert round_price(100.123, 0.01) == 100.12
    assert round_price(100.126, 0.01) == 100.13
    assert round_price(100.5, 0.25) == 100.5
    assert round_price(100.6, 0.25) == 100.75
    
    try:
        round_price(100.0, 0.0)
        assert False, "Should have raised ValueError"
    except ValueError:
        pass
    
    print("✓ Price rounding tests passed")


def test_calculate_position_size():
    """Test position size calculation."""
    def calculate_position_size(
        account_balance: float,
        risk_percentage: float,
        entry_price: float,
        stop_loss_price: float,
        contract_multiplier: float = 1.0
    ) -> int:
        """Calculate position size based on risk management rules."""
        if risk_percentage <= 0 or risk_percentage > 100:
            raise ValueError("Risk percentage must be between 0 and 100")
        
        if entry_price <= 0 or stop_loss_price <= 0:
            raise ValueError("Prices must be positive")
        
        if abs(entry_price - stop_loss_price) < 1e-8:
            raise ValueError("Entry price and stop loss price cannot be the same")
        
        risk_amount = account_balance * (risk_percentage / 100)
        price_difference = abs(entry_price - stop_loss_price)
        risk_per_contract = price_difference * contract_multiplier
        
        if risk_per_contract <= 0:
            return 0
        
        position_size = int(risk_amount / risk_per_contract)
        return max(0, position_size)
    
    # Test normal case
    size = calculate_position_size(
        account_balance=10000,
        risk_percentage=2.0,
        entry_price=100.0,
        stop_loss_price=98.0,
        contract_multiplier=1.0
    )
    assert size == 100  # $200 risk / $2 per contract
    
    # Test edge cases
    try:
        calculate_position_size(10000, 0, 100, 98)  # Invalid risk percentage
        assert False, "Should have raised ValueError"
    except ValueError:
        pass
    
    try:
        calculate_position_size(10000, 2, 0, 98)  # Invalid entry price
        assert False, "Should have raised ValueError"
    except ValueError:
        pass
    
    try:
        calculate_position_size(10000, 2, 100, 100)  # Same entry and stop price
        assert False, "Should have raised ValueError"
    except ValueError:
        pass
    
    print("✓ Position size calculation tests passed")


def test_kelly_fraction():
    """Test Kelly criterion calculation."""
    def calculate_kelly_fraction(
        win_rate: float,
        avg_win: float,
        avg_loss: float
    ) -> float:
        """Calculate Kelly criterion fraction for position sizing."""
        if not (0 <= win_rate <= 1):
            raise ValueError("Win rate must be between 0 and 1")
        
        if avg_win <= 0 or avg_loss <= 0:
            raise ValueError("Average win and loss must be positive")
        
        win_loss_ratio = avg_win / avg_loss
        kelly_fraction = win_rate - ((1 - win_rate) / win_loss_ratio)
        
        # Cap Kelly fraction to prevent over-leveraging
        return max(0, min(kelly_fraction, 0.25))
    
    kelly = calculate_kelly_fraction(0.6, 150, 100)
    assert 0 < kelly <= 0.25  # Should be capped at 0.25
    
    # Test edge cases
    try:
        calculate_kelly_fraction(1.5, 150, 100)  # Invalid win rate
        assert False, "Should have raised ValueError"
    except ValueError:
        pass
    
    try:
        calculate_kelly_fraction(0.6, -150, 100)  # Invalid avg win
        assert False, "Should have raised ValueError"
    except ValueError:
        pass
    
    print("✓ Kelly fraction calculation tests passed")


def test_moving_average():
    """Test moving average calculation."""
    def moving_average(values: list, window: int) -> list:
        """Calculate simple moving average."""
        if window <= 0 or window > len(values):
            return []
        
        result = []
        for i in range(window - 1, len(values)):
            avg = sum(values[i - window + 1:i + 1]) / window
            result.append(avg)
        
        return result
    
    values = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    ma = moving_average(values, 3)
    
    assert len(ma) == 8  # 10 - 3 + 1
    assert ma[0] == 2.0  # (1+2+3)/3
    assert ma[-1] == 9.0  # (8+9+10)/3
    
    print("✓ Moving average calculation tests passed")


def test_correlation():
    """Test correlation calculation."""
    def correlation(x_values: list, y_values: list) -> float:
        """Calculate correlation coefficient between two series."""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0
        
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        sum_y2 = sum(y * y for y in y_values)
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5
        
        if abs(denominator) < 1e-10:
            return 0.0
        
        return numerator / denominator
    
    x = [1, 2, 3, 4, 5]
    y = [2, 4, 6, 8, 10]  # Perfect positive correlation
    
    corr = correlation(x, y)
    assert abs(corr - 1.0) < 1e-10  # Should be 1.0
    
    # Test with no correlation
    y_random = [5, 2, 8, 1, 9]
    corr_random = correlation(x, y_random)
    assert -1 <= corr_random <= 1
    
    print("✓ Correlation calculation tests passed")


def test_contract_id_normalization():
    """Test contract ID normalization."""
    def normalize_contract_id(contract_id: str) -> str:
        """Normalize contract ID to standard format."""
        return contract_id.upper().strip()
    
    assert normalize_contract_id("con.f.us.ep.u25") == "CON.F.US.EP.U25"
    assert normalize_contract_id("  CON.F.US.EP.U25  ") == "CON.F.US.EP.U25"
    
    print("✓ Contract ID normalization tests passed")


def test_timeframe_parsing():
    """Test timeframe parsing."""
    def parse_timeframe(timeframe: str) -> int:
        """Parse timeframe string to minutes."""
        timeframe = timeframe.lower().strip()
        
        if timeframe.endswith('s'):
            return int(timeframe[:-1]) // 60  # Convert seconds to minutes
        elif timeframe.endswith('m'):
            return int(timeframe[:-1])
        elif timeframe.endswith('h'):
            return int(timeframe[:-1]) * 60
        elif timeframe.endswith('d'):
            return int(timeframe[:-1]) * 1440  # 24 * 60
        elif timeframe.endswith('w'):
            return int(timeframe[:-1]) * 10080  # 7 * 24 * 60
        else:
            # Assume it's already in minutes
            return int(timeframe)
    
    assert parse_timeframe("1m") == 1
    assert parse_timeframe("5m") == 5
    assert parse_timeframe("1h") == 60
    assert parse_timeframe("1d") == 1440
    assert parse_timeframe("1w") == 10080
    assert parse_timeframe("60") == 60  # Already in minutes
    
    print("✓ Timeframe parsing tests passed")


def main():
    """Run all tests."""
    print("Running core functionality tests...")
    print()
    
    try:
        test_round_price()
        test_calculate_position_size()
        test_kelly_fraction()
        test_moving_average()
        test_correlation()
        test_contract_id_normalization()
        test_timeframe_parsing()
        
        print()
        print("🎉 All core functionality tests passed!")
        print("✅ Core components are working correctly")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
