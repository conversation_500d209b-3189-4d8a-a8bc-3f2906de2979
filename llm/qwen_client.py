"""
Qwen LLM client implementation.
Real API client for Qwen language model.
"""

import json
import time
from typing import Optional, Dict, Any, List
import aiohttp

from core import get_logger, get_settings, retry_decorator, timing_decorator
from .models import LLMResponse, TradingDecision, MarketAnalysis, LLMPrompt

logger = get_logger(__name__)


class QwenAPIError(Exception):
    """Qwen API error."""
    def __init__(self, message: str, error_code: str = "", status_code: int = 0):
        super().__init__(message)
        self.error_code = error_code
        self.status_code = status_code


class QwenClient:
    """Qwen LLM client with real API integration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def initialize(self) -> None:
        """Initialize the HTTP session."""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=self.settings.qwen.timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    'Authorization': f'Bearer {self.settings.qwen.api_key.get_secret_value()}',
                    'Content-Type': 'application/json'
                }
            )
            logger.info("Qwen LLM client initialized")
    
    async def close(self) -> None:
        """Close the HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("Qwen LLM client closed")
    
    @timing_decorator
    @retry_decorator(max_retries=3, delay=1.0, exceptions=(aiohttp.ClientError, QwenAPIError))
    async def _make_request(self, messages: list, **kwargs) -> Dict[str, Any]:
        """Make request to Qwen API."""
        if not self.session:
            await self.initialize()
        
        payload = {
            "model": self.settings.qwen.model,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", self.settings.qwen.max_tokens),
            "temperature": kwargs.get("temperature", self.settings.qwen.temperature),
            "stream": False
        }
        
        try:
            async with self.session.post(
                f"{self.settings.qwen.base_url}/chat/completions",
                json=payload
            ) as response:
                
                response_text = await response.text()
                
                if response.status >= 400:
                    logger.error(f"Qwen API request failed: {response.status} - {response_text}")
                    try:
                        error_data = json.loads(response_text)
                        error_message = error_data.get("error", {}).get("message", "Unknown error")
                        error_code = error_data.get("error", {}).get("code", "")
                    except json.JSONDecodeError:
                        error_message = response_text
                        error_code = ""
                    
                    raise QwenAPIError(
                        f"Qwen API error: {error_message}",
                        error_code=error_code,
                        status_code=response.status
                    )
                
                try:
                    return json.loads(response_text)
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON response from Qwen: {response_text}")
                    raise QwenAPIError("Invalid JSON response from Qwen API")
        
        except aiohttp.ClientError as e:
            logger.error(f"HTTP request to Qwen failed: {e}")
            raise QwenAPIError(f"HTTP request failed: {e}")
    
    @timing_decorator
    async def generate_response(self, prompt: LLMPrompt) -> LLMResponse:
        """Generate response from Qwen."""
        messages = [
            {"role": "system", "content": prompt.system_prompt},
            {"role": "user", "content": prompt.user_prompt}
        ]
        
        response_data = await self._make_request(
            messages,
            max_tokens=prompt.max_tokens,
            temperature=prompt.temperature
        )
        
        # Extract response content
        content = response_data["choices"][0]["message"]["content"]
        usage_tokens = response_data.get("usage", {}).get("total_tokens", 0)
        
        return LLMResponse(
            content=content,
            usage_tokens=usage_tokens,
            model=self.settings.qwen.model
        )
    
    @timing_decorator
    async def analyze_market(
        self, 
        market_data: Dict[str, Any], 
        technical_indicators: Dict[str, Any],
        symbol: str
    ) -> MarketAnalysis:
        """Analyze market conditions using Qwen."""
        
        system_prompt = """You are an expert financial market analyst specializing in futures trading. 
        Analyze the provided market data and technical indicators to provide comprehensive market analysis.
        
        Focus on:
        1. Current market sentiment and trend direction
        2. Key support and resistance levels
        3. Technical signal analysis
        4. Risk factors and market conditions
        5. Overall confidence in the analysis
        
        Provide your analysis in a structured format with clear reasoning."""
        
        user_prompt = f"""
        Analyze the market for {symbol}:
        
        Market Data:
        {json.dumps(market_data, indent=2)}
        
        Technical Indicators:
        {json.dumps(technical_indicators, indent=2)}
        
        Please provide a comprehensive market analysis including sentiment, trend direction, 
        key levels, and risk factors. Rate your confidence from 0.0 to 1.0.
        """
        
        prompt = LLMPrompt(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            max_tokens=1500,
            temperature=0.1
        )
        
        response = await self.generate_response(prompt)
        
        # Parse the response to extract structured analysis
        # For now, return a basic structure - can be enhanced with JSON parsing
        return MarketAnalysis(
            symbol=symbol,
            sentiment="NEUTRAL",  # Would parse from response
            trend_direction="SIDEWAYS",  # Would parse from response
            key_levels={"support": 0.0, "resistance": 0.0},  # Would parse from response
            technical_signals=[],  # Would parse from response
            confidence=0.7,  # Would parse from response
            analysis_text=response.content
        )
    
    @timing_decorator
    async def make_trading_decision(
        self,
        market_analysis: MarketAnalysis,
        strategy_signals: list,
        risk_parameters: Dict[str, Any],
        current_positions: list
    ) -> TradingDecision:
        """Make trading decision using Qwen."""
        
        system_prompt = """You are an expert algorithmic trading decision maker. 
        Based on market analysis, strategy signals, risk parameters, and current positions,
        make optimal trading decisions.
        
        Consider:
        1. Risk management and position sizing
        2. Entry and exit points
        3. Stop loss and take profit levels
        4. Market conditions and volatility
        5. Portfolio exposure and correlation
        
        Always prioritize risk management over profit potential."""
        
        user_prompt = f"""
        Make a trading decision based on:
        
        Market Analysis:
        {market_analysis.model_dump_json(indent=2)}
        
        Strategy Signals:
        {json.dumps(strategy_signals, indent=2)}
        
        Risk Parameters:
        {json.dumps(risk_parameters, indent=2)}
        
        Current Positions:
        {json.dumps(current_positions, indent=2)}
        
        Provide a clear trading decision with action, confidence level, reasoning,
        and specific price levels for entry, stop loss, and take profit.
        """
        
        prompt = LLMPrompt(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            max_tokens=1000,
            temperature=0.05  # Lower temperature for trading decisions
        )
        
        response = await self.generate_response(prompt)
        
        # Parse the response to extract trading decision
        # For now, return a basic structure - can be enhanced with JSON parsing
        return TradingDecision(
            action="HOLD",  # Would parse from response
            confidence=0.6,  # Would parse from response
            reasoning=response.content,
            entry_price=None,  # Would parse from response
            stop_loss=None,  # Would parse from response
            take_profit=None  # Would parse from response
        )
    
    @timing_decorator
    async def analyze_strategy_signal(
        self,
        strategy_name: str,
        signal_data: Dict[str, Any],
        market_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze a specific strategy signal."""
        
        system_prompt = f"""You are an expert in the {strategy_name} trading strategy.
        Analyze the provided signal data in the context of current market conditions.
        
        Evaluate:
        1. Signal strength and reliability
        2. Entry and exit conditions
        3. Risk assessment
        4. Probability of success
        5. Market context alignment
        
        Provide detailed analysis with specific recommendations."""
        
        user_prompt = f"""
        Analyze this {strategy_name} signal:
        
        Signal Data:
        {json.dumps(signal_data, indent=2)}
        
        Market Context:
        {json.dumps(market_context, indent=2)}
        
        Provide comprehensive analysis of this signal including strength assessment,
        entry/exit conditions, and risk evaluation.
        """
        
        prompt = LLMPrompt(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            max_tokens=800,
            temperature=0.1
        )
        
        response = await self.generate_response(prompt)
        
        return {
            "strategy": strategy_name,
            "analysis": response.content,
            "confidence": 0.7,  # Would parse from response
            "recommendation": "NEUTRAL"  # Would parse from response
        }

    async def analyze_trading_signals(
        self,
        trading_decisions: List[TradingDecision],
        market_context: Dict[str, Any]
    ) -> Optional[TradingDecision]:
        """Analyze multiple trading signals and select the best one."""
        try:
            from core.settings_manager import settings_manager

            if not trading_decisions:
                settings_manager.add_system_message(
                    component="LLM",
                    message="No trading signals to analyze",
                    level="INFO"
                )
                return None

            # Get dynamic confidence threshold
            min_confidence = settings_manager.get_confidence_threshold()

            # If only one signal, return it if confidence is high enough
            if len(trading_decisions) == 1:
                decision = trading_decisions[0]
                if decision.confidence >= min_confidence:
                    settings_manager.add_system_message(
                        component="LLM",
                        message=f"Signal approved: {decision.strategy_name} {decision.action.value} (Confidence: {decision.confidence:.1%} >= {min_confidence:.1%})",
                        level="INFO"
                    )
                    return decision
                else:
                    settings_manager.add_system_message(
                        component="LLM",
                        message=f"Signal rejected: {decision.strategy_name} {decision.action.value} (Confidence: {decision.confidence:.1%} < {min_confidence:.1%})",
                        level="WARNING"
                    )
                    return None

            # For multiple signals, select the one with highest confidence
            best_decision = max(trading_decisions, key=lambda d: d.confidence)

            # Apply minimum confidence threshold
            if best_decision.confidence >= min_confidence:
                settings_manager.add_system_message(
                    component="LLM",
                    message=f"Best signal selected: {best_decision.strategy_name} {best_decision.action.value} (Confidence: {best_decision.confidence:.1%} >= {min_confidence:.1%})",
                    level="INFO"
                )
                return best_decision
            else:
                settings_manager.add_system_message(
                    component="LLM",
                    message=f"All signals rejected: Best was {best_decision.strategy_name} {best_decision.action.value} (Confidence: {best_decision.confidence:.1%} < {min_confidence:.1%})",
                    level="WARNING"
                )
                return None

        except Exception as e:
            logger.error(f"Error analyzing trading signals: {e}")
            return None
