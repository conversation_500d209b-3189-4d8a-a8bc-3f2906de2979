"""
LLM models and data structures.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum


class TradingAction(str, Enum):
    """Trading actions."""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    CLOSE_LONG = "CLOSE_LONG"
    CLOSE_SHORT = "CLOSE_SHORT"


class MarketSentiment(str, Enum):
    """Market sentiment."""
    BULLISH = "BULLISH"
    BEARISH = "BEARISH"
    NEUTRAL = "NEUTRAL"
    UNCERTAIN = "UNCERTAIN"


class LLMResponse(BaseModel):
    """Base LLM response model."""
    content: str
    usage_tokens: int = 0
    model: str
    timestamp: datetime = Field(default_factory=lambda: datetime.utcnow())


class TradingDecision(BaseModel):
    """Trading decision from LLM."""
    action: TradingAction
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size: Optional[int] = None
    risk_reward_ratio: Optional[float] = None
    timeframe: Optional[str] = None
    strategy_name: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class MarketAnalysis(BaseModel):
    """Market analysis from LLM."""
    symbol: str
    sentiment: MarketSentiment
    trend_direction: str
    key_levels: Dict[str, float] = Field(default_factory=dict)
    technical_signals: List[str] = Field(default_factory=list)
    fundamental_factors: List[str] = Field(default_factory=list)
    risk_factors: List[str] = Field(default_factory=list)
    confidence: float = Field(ge=0.0, le=1.0)
    analysis_text: str
    timestamp: datetime = Field(default_factory=lambda: datetime.utcnow())


class StrategySignal(BaseModel):
    """Strategy signal analysis."""
    strategy_name: str
    signal_strength: float = Field(ge=0.0, le=1.0)
    signal_type: TradingAction
    entry_conditions: List[str] = Field(default_factory=list)
    exit_conditions: List[str] = Field(default_factory=list)
    risk_assessment: str
    probability: float = Field(ge=0.0, le=1.0)


class LLMPrompt(BaseModel):
    """LLM prompt structure."""
    system_prompt: str
    user_prompt: str
    context: Dict[str, Any] = Field(default_factory=dict)
    max_tokens: int = 2000
    temperature: float = 0.1
