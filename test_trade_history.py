#!/usr/bin/env python3
"""
Test Trade History API - Verify the new trade history endpoints work
"""

import asyncio
import aiohttp
import json
from datetime import datetime


async def test_trade_history_api():
    """Test the trade history API endpoints."""
    
    print("🧪 TESTING TRADE HISTORY API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Get dashboard state (should include trade_history)
        print("\n1️⃣ Testing Dashboard State API...")
        try:
            async with session.get(f"{base_url}/api/status") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        dashboard_state = data.get('data', {})
                        trade_history = dashboard_state.get('trade_history', [])
                        order_history = dashboard_state.get('order_history', [])
                        
                        print(f"   ✅ Dashboard State API working")
                        print(f"   📊 Trade History Entries: {len(trade_history)}")
                        print(f"   📋 Order History Entries: {len(order_history)}")
                        
                        if trade_history:
                            print(f"\n   📈 Latest Trade:")
                            latest_trade = trade_history[-1]
                            print(f"      - Strategy: {latest_trade.get('strategy', 'N/A')}")
                            print(f"      - Symbol: {latest_trade.get('symbol', 'N/A')}")
                            print(f"      - Action: {latest_trade.get('action', 'N/A')}")
                            print(f"      - Size: {latest_trade.get('size', 'N/A')}")
                            print(f"      - Entry Price: ${latest_trade.get('entry_price', 0):.2f}")
                            print(f"      - Filled Price: ${latest_trade.get('filled_price', 0):.2f}")
                            print(f"      - Confidence: {(latest_trade.get('confidence', 0) * 100):.1f}%")
                            print(f"      - Timestamp: {latest_trade.get('timestamp', 'N/A')}")
                        
                        if order_history:
                            print(f"\n   📋 Latest Order:")
                            latest_order = order_history[-1]
                            print(f"      - Symbol: {latest_order.get('symbol', 'N/A')}")
                            print(f"      - Action: {latest_order.get('action', 'N/A')}")
                            print(f"      - Status: {latest_order.get('status', 'N/A')}")
                            print(f"      - Success: {latest_order.get('success', False)}")
                            print(f"      - Strategy: {latest_order.get('strategy', 'N/A')}")
                    else:
                        print(f"   ❌ Dashboard State API failed: {data.get('message', 'Unknown error')}")
                else:
                    print(f"   ❌ Dashboard State API failed: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ Dashboard State API error: {e}")
        
        # Test 2: Get trade history directly
        print("\n2️⃣ Testing Trade History API...")
        try:
            async with session.get(f"{base_url}/api/trade-history") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        trade_history = data.get('trade_history', [])
                        order_history = data.get('order_history', [])
                        
                        print(f"   ✅ Trade History API working")
                        print(f"   📊 Trade History Entries: {len(trade_history)}")
                        print(f"   📋 Order History Entries: {len(order_history)}")
                    else:
                        print(f"   ❌ Trade History API failed: {data.get('message', 'Unknown error')}")
                else:
                    print(f"   ❌ Trade History API failed: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ Trade History API error: {e}")
        
        # Test 3: Get recent trades from TopStep
        print("\n3️⃣ Testing Recent Trades API...")
        try:
            async with session.get(f"{base_url}/api/recent-trades") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        trades = data.get('trades', [])
                        print(f"   ✅ Recent Trades API working")
                        print(f"   💼 Recent TopStep Trades: {len(trades)}")
                        
                        if trades:
                            print(f"\n   💰 Latest TopStep Trade:")
                            latest_trade = trades[-1]
                            print(f"      - ID: {latest_trade.get('id', 'N/A')}")
                            print(f"      - Symbol: {latest_trade.get('symbol', 'N/A')}")
                            print(f"      - Side: {latest_trade.get('side', 'N/A')}")
                            print(f"      - Size: {latest_trade.get('size', 'N/A')}")
                            print(f"      - Price: ${latest_trade.get('price', 0):.2f}")
                            print(f"      - P&L: ${latest_trade.get('pnl', 0):.2f}")
                    else:
                        print(f"   ❌ Recent Trades API failed: {data.get('message', 'Unknown error')}")
                else:
                    print(f"   ❌ Recent Trades API failed: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ Recent Trades API error: {e}")
        
        # Test 4: Check system messages
        print("\n4️⃣ Testing System Messages API...")
        try:
            async with session.get(f"{base_url}/api/system-messages") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        messages = data.get('messages', [])
                        print(f"   ✅ System Messages API working")
                        print(f"   📨 Recent Messages: {len(messages)}")
                        
                        # Look for trade-related messages
                        trade_messages = [msg for msg in messages if 'trade' in msg.get('message', '').lower() or 'order' in msg.get('message', '').lower()]
                        print(f"   🔍 Trade-related Messages: {len(trade_messages)}")
                        
                        if trade_messages:
                            print(f"\n   📝 Latest Trade Message:")
                            latest_msg = trade_messages[-1]
                            print(f"      - Level: {latest_msg.get('level', 'N/A')}")
                            print(f"      - Source: {latest_msg.get('source', 'N/A')}")
                            print(f"      - Message: {latest_msg.get('message', 'N/A')}")
                            print(f"      - Time: {latest_msg.get('timestamp', 'N/A')}")
                    else:
                        print(f"   ❌ System Messages API failed: {data.get('message', 'Unknown error')}")
                else:
                    print(f"   ❌ System Messages API failed: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ System Messages API error: {e}")
    
    print(f"\n🎯 Trade History API Test Complete!")
    print(f"📊 Dashboard URL: {base_url}")
    print(f"🔗 Trade History API: {base_url}/api/trade-history")
    print(f"🔗 Recent Trades API: {base_url}/api/recent-trades")


if __name__ == "__main__":
    asyncio.run(test_trade_history_api())
