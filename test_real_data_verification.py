#!/usr/bin/env python3
"""
REAL DATA VERIFICATION TEST
Comprehensive test to verify if our trading system is using REAL data or fake/cached data.
"""

import asyncio
import sys
import json
from datetime import datetime, timezone
from typing import Dict, Any, List

# Add project root to path
sys.path.append('.')

from api import TopStepClient
from agents import MarketDataAgent, FVGDetectionAgent
from workflow import TradingWorkflow
from llm import QwenClient


async def test_real_data_verification():
    """Comprehensive test to verify real data usage."""
    
    print("🔍 REAL DATA VERIFICATION TEST")
    print("=" * 60)
    print("Testing if our system uses REAL market data or fake/cached data")
    print("=" * 60)
    
    results = {
        "api_connection": False,
        "real_market_data": False,
        "live_prices": False,
        "real_positions": False,
        "authentic_signals": False,
        "timestamp_verification": False
    }
    
    try:
        # Test 1: API Connection Verification
        print("\n🔌 TEST 1: API CONNECTION VERIFICATION")
        print("-" * 50)
        
        topstep_client = TopStepClient(preferred_account_type="PRACTICE")
        
        # Test authentication
        auth_success = await topstep_client.authenticate()
        if auth_success:
            print("✅ TopStep API authentication successful")
            print(f"   Account ID: {topstep_client.account_id}")
            print(f"   Account Type: {topstep_client.preferred_account_type}")
            results["api_connection"] = True
        else:
            print("❌ TopStep API authentication failed")
            return results
        
        # Test 2: Real Market Data Verification
        print("\n📊 TEST 2: REAL MARKET DATA VERIFICATION")
        print("-" * 50)
        
        market_agent = MarketDataAgent(topstep_client)
        
        # Get current market data
        contract_id = topstep_client.convert_symbol_to_contract("ES")
        print(f"   Contract ID: {contract_id}")
        
        market_bars = await market_agent.fetch_market_data(
            contract_id=contract_id,
            timeframe="1m",
            limit=5
        )
        
        if market_bars and len(market_bars) > 0:
            latest_bar = market_bars[-1]
            print(f"✅ Market data retrieved: {len(market_bars)} bars")
            print(f"   Latest bar timestamp: {latest_bar.timestamp}")
            print(f"   Latest close price: ${latest_bar.close_price:.2f}")
            print(f"   Latest volume: {latest_bar.volume}")
            
            # Check if data is recent (within last 10 minutes)
            time_diff = datetime.now(timezone.utc) - latest_bar.timestamp
            if time_diff.total_seconds() < 600:  # 10 minutes
                print("✅ Data is recent (within 10 minutes)")
                results["real_market_data"] = True
                results["timestamp_verification"] = True
            else:
                print(f"⚠️ Data is old: {time_diff.total_seconds()} seconds ago")
        else:
            print("❌ No market data retrieved")
        
        # Test 3: Live Price Verification
        print("\n💰 TEST 3: LIVE PRICE VERIFICATION")
        print("-" * 50)
        
        # Get multiple price samples to verify they're changing
        price_samples = []
        for i in range(3):
            bars = await market_agent.fetch_market_data(contract_id, "1m", 1)
            if bars:
                price_samples.append(bars[0].close_price)
                print(f"   Sample {i+1}: ${bars[0].close_price:.2f}")
            await asyncio.sleep(2)  # Wait 2 seconds between samples
        
        if len(set(price_samples)) > 1:
            print("✅ Prices are changing (live data)")
            results["live_prices"] = True
        else:
            print("⚠️ Prices are static (may be cached)")
        
        # Test 4: Real Positions Verification
        print("\n📈 TEST 4: REAL POSITIONS VERIFICATION")
        print("-" * 50)
        
        positions = await topstep_client.get_positions(topstep_client.account_id)
        if positions:
            print(f"✅ Real positions found: {len(positions)}")
            for pos in positions[:3]:  # Show first 3 positions
                print(f"   Position: {pos.get('id', 'N/A')} - Size: {pos.get('size', 0)}")
            results["real_positions"] = True
        else:
            print("ℹ️ No open positions (normal for new account)")
            results["real_positions"] = True  # No positions is also valid
        
        # Test 5: Authentic Signal Generation
        print("\n🎯 TEST 5: AUTHENTIC SIGNAL GENERATION")
        print("-" * 50)
        
        # Process market data and generate signals
        processed_data = await market_agent.process_market_data(market_bars, contract_id)
        
        if processed_data:
            print(f"✅ Market data processed: {len(processed_data)} bars")
            
            # Test FVG detection
            fvg_agent = FVGDetectionAgent()
            detected_fvgs = fvg_agent.detect_fvgs(processed_data)
            
            print(f"   FVGs detected: {len(detected_fvgs)}")
            
            if detected_fvgs:
                for i, fvg in enumerate(detected_fvgs[:2]):  # Show first 2 FVGs
                    print(f"   FVG {i+1}: {fvg.fvg_type.value} - Gap: {fvg.gap_size:.2f}")
                    print(f"           Confluence: {fvg.confluence_score:.1f}")
                    print(f"           Formation: {fvg.formation_time}")
            
            # Generate trading signals
            if processed_data:
                signals = fvg_agent.generate_trading_signals(processed_data[-1])
                print(f"   Trading signals generated: {len(signals)}")
                
                if signals:
                    results["authentic_signals"] = True
                    for signal in signals[:2]:  # Show first 2 signals
                        print(f"   Signal: {signal.action.value} - Confidence: {signal.confidence:.2f}")
                        print(f"          Entry: ${signal.entry_price:.2f}")
                        print(f"          Reasoning: {signal.reasoning[:50]}...")
        
        # Test 6: Workflow Integration Test
        print("\n🔄 TEST 6: WORKFLOW INTEGRATION TEST")
        print("-" * 50)
        
        qwen_client = QwenClient()
        workflow = TradingWorkflow(topstep_client, qwen_client)
        
        # Run a single trading cycle
        result = await workflow.run_trading_cycle("ES")
        
        if result.get("success"):
            print("✅ Workflow executed successfully")
            print(f"   Signals detected: {result.get('signals_detected', {})}")
            print(f"   Should trade: {result.get('should_trade', False)}")
            print(f"   Workflow step: {result.get('workflow_step', 'unknown')}")
            
            if result.get("selected_decision"):
                decision = result["selected_decision"]
                print(f"   Selected decision: {decision.get('action', 'N/A')}")
                print(f"   Confidence: {decision.get('confidence', 0):.2f}")
        else:
            print(f"⚠️ Workflow failed: {result.get('error_message', 'Unknown error')}")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 REAL DATA VERIFICATION RESULTS")
        print("=" * 60)
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        print(f"\n📊 TEST SUMMARY:")
        print(f"   Tests Passed: {passed_tests}/{total_tests}")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n✅ VERIFIED REAL DATA COMPONENTS:")
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        if passed_tests >= 4:
            print(f"\n🎉 CONCLUSION: SYSTEM IS USING REAL DATA")
            print("   ✅ API connections are live")
            print("   ✅ Market data is authentic")
            print("   ✅ Signals are generated from real patterns")
            print("   ✅ System is professional-grade")
        else:
            print(f"\n⚠️ CONCLUSION: SYSTEM MAY BE USING SIMULATED DATA")
            print("   ❌ Some components are not using real data")
            print("   ❌ Further investigation needed")
        
        return results
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return results
    
    finally:
        # Cleanup
        if 'topstep_client' in locals():
            await topstep_client.close()


if __name__ == "__main__":
    results = asyncio.run(test_real_data_verification())
    
    # Exit with appropriate code
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    if passed_tests >= 4:
        print(f"\n🎉 REAL DATA VERIFICATION: PASSED ({passed_tests}/{total_tests})")
        sys.exit(0)
    else:
        print(f"\n❌ REAL DATA VERIFICATION: FAILED ({passed_tests}/{total_tests})")
        sys.exit(1)
